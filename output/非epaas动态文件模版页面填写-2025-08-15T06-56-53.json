[{"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/doc-templates/doc-template-fill-url' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateId\": \"0f0b5d63ca754f7ab9f506734f2216a6\",\n    \"customBizNum\": \"测试\",\n    \"componentFillingtValues\": [\n        {\n            \"componentId\": \"2d12afbca2904606ad4a19ee6b0b59ee\",\n            \"componentValue\": \"f0a318f9e1394d9e8779f8953599d849\"\n        },\n        {\n            \"componentId\": \"3ce2327cd1e941768c77d59908bea697\",\n            \"componentValue\": \"1\"\n        },\n        {\n            \"componentId\": \"5c36ace2845b415abbc309448274475f\",\n            \"componentValue\": \"2025/08/09\"\n        }\n    ],\n    \"editFillingValue\": true,\n    \"clientType\": \"ALL\",\n    \"notifyUrl\": \"https://libaohui.com.cn/callback/ding\",\n    \"redirectUrl\": \"https://www.esign.cn\"\n}'`,\n     data: {\n      url:\"data.docTemplateFillUrl\",\nfillTaskId:\"data.fillTaskId\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "点击css://textarea", "type": "click", "element": "css://textarea", "desc": "点击${点击}"}, {"text": "输入哈哈哈", "type": "input", "value": "哈哈哈", "desc": "输入哈哈哈"}, {"text": "点击css://div:nth-child(3) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(3) > div:nth-child(2) > input", "desc": "点击${点击请输入整数}"}, {"text": "输入123", "type": "input", "value": "123", "desc": "输入123"}, {"text": "点击css://div:nth-child(4) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(4) > div:nth-child(2) > input", "desc": "点击${点击请输入}"}, {"text": "输入13506712563", "type": "input", "value": "13506712563", "desc": "输入13506712563"}, {"text": "点击css://div:nth-child(5) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(5) > div:nth-child(2) > input", "desc": "点击${点击请输入 1}"}, {"text": "输入111111111111111", "type": "input", "value": "111111111111111", "desc": "输入111111111111111"}, {"text": "点击css://div:nth-child(1) > label > span > span", "type": "click", "element": "css://div:nth-child(1) > label > span > span", "desc": "点击${点击 1}"}, {"text": "点击css://div:nth-child(2) > label > span > span", "type": "click", "element": "css://div:nth-child(2) > label > span > span", "desc": "点击${点击 2}"}, {"text": "点击css://div:nth-child(3) > label > span > span", "type": "click", "element": "css://div:nth-child(3) > label > span > span", "desc": "点击${点击 3}"}, {"text": "点击css://button > span > span > span", "type": "click", "element": "css://button > span > span > span", "desc": "点击${点击确认提交}"}, {"text": "选择确定.button", "type": "choose", "value": "确定.button", "desc": "选择确定.button"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/doc-templates/fill-task-result' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple' \\\n--header 'Content-Type: application/json' \\\n--data '{\n  \"docTemplateId\": \"0f0b5d63ca754f7ab9f506734f2216a6\",\n  \"fillTaskId\": \"{fillTaskId}\"\n}'`,\n     data: {\n      fileId:\"data.fileId\"\n    }\n  },\n{\n     curl: `curl --location --request GET 'http://in-test-openapi.tsign.cn/v3/files/{fileId}/detail' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple'`,\n     data: {\n      url:\"data.fileDownloadUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}]