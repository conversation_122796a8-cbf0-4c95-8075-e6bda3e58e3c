[{"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/doc-templates/doc-template-create-url' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateName\": \"api静态文件模板080201\",\n    \"docTemplateType\": 0,\n    \"fileId\": \"afae7fe85fe2459db669667f035e1b0d\",\n    \"redirectUrl\": \"\"\n}'`,\n     data: {\n      url:\"data.docTemplateCreateUrl\",\ndocTemplateId:\"data.docTemplateId\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i移动659,1", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i", "type": "drag", "value": {"type": "relative", "x": "659", "y": "1"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i移动659,1"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入单行文本", "type": "input", "value": "单行文本", "desc": "输入单行文本"}, {"text": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > div移动595,51", "element": "css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > div", "type": "drag", "value": {"type": "relative", "x": "595", "y": "51"}, "desc": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > div移动595,51"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入多行文本", "type": "input", "value": "多行文本", "desc": "输入多行文本"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div移动370,117", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div", "type": "drag", "value": {"type": "relative", "x": "370", "y": "117"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div移动370,117"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入身份证号", "type": "input", "value": "身份证号", "desc": "输入身份证号"}, {"text": "拖拽css://div:nth-child(1) > div:nth-child(4) > i移动505,119", "element": "css://div:nth-child(1) > div:nth-child(4) > i", "type": "drag", "value": {"type": "relative", "x": "505", "y": "119"}, "desc": "拖拽css://div:nth-child(1) > div:nth-child(4) > i移动505,119"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入数字", "type": "input", "value": "数字", "desc": "输入数字"}, {"text": "拖拽css://div > div > div:nth-child(1) > div:nth-child(5) > div移动438,122", "element": "css://div > div > div:nth-child(1) > div:nth-child(5) > div", "type": "drag", "value": {"type": "relative", "x": "438", "y": "122"}, "desc": "拖拽css://div > div > div:nth-child(1) > div:nth-child(5) > div移动438,122"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入手机", "type": "input", "value": "手机", "desc": "输入手机"}, {"text": "拖拽css://div:nth-child(6) > i移动555,108", "element": "css://div:nth-child(6) > i", "type": "drag", "value": {"type": "relative", "x": "555", "y": "108"}, "desc": "拖拽css://div:nth-child(6) > i移动555,108"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入日期", "type": "input", "value": "日期", "desc": "输入日期"}, {"text": "拖拽css://div:nth-child(7) > div移动555,156", "element": "css://div:nth-child(7) > div", "type": "drag", "value": {"type": "relative", "x": "555", "y": "156"}, "desc": "拖拽css://div:nth-child(7) > div移动555,156"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入下拉选择", "type": "input", "value": "下拉选择", "desc": "输入下拉选择"}, {"text": "拖拽css://div:nth-child(8) > div移动343,228", "element": "css://div:nth-child(8) > div", "type": "drag", "value": {"type": "relative", "x": "343", "y": "228"}, "desc": "拖拽css://div:nth-child(8) > div移动343,228"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入单选", "type": "input", "value": "单选", "desc": "输入单选"}, {"text": "拖拽css://div:nth-child(9) > i移动701,185", "element": "css://div:nth-child(9) > i", "type": "drag", "value": {"type": "relative", "x": "701", "y": "185"}, "desc": "拖拽css://div:nth-child(9) > i移动701,185"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入多选", "type": "input", "value": "多选", "desc": "输入多选"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(10) > div移动603,268", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(10) > div", "type": "drag", "value": {"type": "relative", "x": "603", "y": "268"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(10) > div移动603,268"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入勾选", "type": "input", "value": "勾选", "desc": "输入勾选"}, {"text": "拖拽css://div:nth-child(11) > i移动325,350", "element": "css://div:nth-child(11) > i", "type": "drag", "value": {"type": "relative", "x": "325", "y": "350"}, "desc": "拖拽css://div:nth-child(11) > i移动325,350"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入图片", "type": "input", "value": "图片", "desc": "输入图片"}, {"text": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1)移动640,349", "element": "css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1)", "type": "drag", "value": {"type": "relative", "x": "640", "y": "349"}, "desc": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1)移动640,349"}, {"text": "点击css://#attr-key", "type": "click", "element": "css://#attr-key", "desc": "点击${点击请输入}"}, {"text": "输入签署区", "type": "input", "value": "签署区", "desc": "输入签署区"}, {"text": "点击css://div:nth-child(3) > button > span > span > span", "type": "click", "element": "css://div:nth-child(3) > button > span > span > span", "desc": "点击${点击确认提交}"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/files/create-by-doc-template' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateId\": \"{docTemplateId}\",\n    \"fileName\": \"1页文档\",\n    \"components\": [\n        {\n            \"componentKey\": \"单行文本\",\n            \"componentValue\": \"哈哈哈\"\n        },\n        {\n            \"componentKey\": \"多行文本\",\n            \"componentValue\": \"测试123\"\n        },\n        {\n            \"componentKey\": \"身份证号\",\n            \"componentValue\": \"111111111111111\"\n        },\n        {\n            \"componentKey\": \"数字\",\n            \"componentValue\": \"111\"\n        },\n        {\n            \"componentKey\": \"手机号\",\n            \"componentValue\": \"13506712563\"\n        },\n        {\n            \"componentKey\": \"日期\",\n            \"componentValue\": \"2025-08-09\"\n        },\n        {\n            \"componentKey\": \"下拉选择\",\n            \"componentValue\": \"0\"\n        },\n        {\n            \"componentKey\": \"单选\",\n            \"componentValue\": \"1\"\n        },\n        {\n            \"componentKey\": \"多选\",\n            \"componentValue\": \"[0,1,2]\"\n        },\n        {\n            \"componentKey\": \"勾选\",\n            \"componentValue\": \"true\"\n        },\n        {\n            \"componentKey\": \"图片\",\n            \"componentValue\": \"f0a318f9e1394d9e8779f8953599d849\"\n        }\n    ]\n}'`,\n     data: {\n      fileId:\"data.fileId\",\nurl:\"data.fileDownloadUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}]