[{"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/sign-templates/sign-template-create-url' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: **********' \\\n--header 'X-Tsign-Open-App-Secret: a52af705a618566e0a433b9f789cdf38' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"orgId\": \"265210bd57734f61aabd5e363396e5d2\",\n    \"transactorPsnId\": \"276b5a8f2a964142bb1ebcf0dd82bf72\",\n    \"redirectUrl\": \"\",\n    \"participants\":[\n        {\n            \"participantFlag\": \"甲方\",\n            \"participantType\": 2,\n            \"participateBizType\":\"1,2\",\n            \"participantSetMode\": 1,\n            \"signOrder\": 1,\n            \"draftOrder\": 1\n        }\n    ]\n}'`,\n     data: {\n      url:\"data.signTemplateCreateUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "选择添加签署文件.button", "type": "choose", "value": "添加签署文件.button", "desc": "选择添加签署文件.button"}, {"text": "使用css://div:nth-child(2) > div:nth-child(2) > div:nth-child(1) > input上传文件", "type": "upload", "element": "css://div:nth-child(2) > div:nth-child(2) > div:nth-child(1) > input", "desc": "使用css://div:nth-child(2) > div:nth-child(2) > div:nth-child(1) > input上传文件"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(1) > div:nth-child(2) > form > div > div > div > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(1) > div:nth-child(2) > form > div > div > div > input", "desc": "点击${点击请输入模板名称}"}, {"text": "输入api非epaas流程模板081603", "type": "input", "value": "api非epaas流程模板081603", "desc": "输入api非epaas流程模板081603"}, {"text": "选择下一步.button", "type": "choose", "value": "下一步.button", "desc": "选择下一步.button"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i移动606,0", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i", "type": "drag", "value": {"type": "relative", "x": "606", "y": "0"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(1) > i移动606,0"}, {"text": "点击css://div:nth-child(5) > div > div > div:nth-child(1) > div", "type": "click", "element": "css://div:nth-child(5) > div > div > div:nth-child(1) > div", "desc": "点击${点击高级配置}"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入单行文本", "type": "input", "value": "单行文本", "desc": "输入单行文本"}, {"text": "拖拽css://div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > i移动565,55", "element": "css://div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > i", "type": "drag", "value": {"type": "relative", "x": "565", "y": "55"}, "desc": "拖拽css://div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > i移动565,55"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入多行文本", "type": "input", "value": "多行文本", "desc": "输入多行文本"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div移动364,117", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div", "type": "drag", "value": {"type": "relative", "x": "364", "y": "117"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(3) > div移动364,117"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入手机号", "type": "input", "value": "手机号", "desc": "输入手机号"}, {"text": "拖拽css://div:nth-child(1) > div:nth-child(4) > i移动441,107", "element": "css://div:nth-child(1) > div:nth-child(4) > i", "type": "drag", "value": {"type": "relative", "x": "441", "y": "107"}, "desc": "拖拽css://div:nth-child(1) > div:nth-child(4) > i移动441,107"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入身份证号", "type": "input", "value": "身份证号", "desc": "输入身份证号"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(5) > div移动479,121", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(5) > div", "type": "drag", "value": {"type": "relative", "x": "479", "y": "121"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(5) > div移动479,121"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入数字", "type": "input", "value": "数字", "desc": "输入数字"}, {"text": "拖拽css://div:nth-child(6) > i移动605,118", "element": "css://div:nth-child(6) > i", "type": "drag", "value": {"type": "relative", "x": "605", "y": "118"}, "desc": "拖拽css://div:nth-child(6) > i移动605,118"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入日期", "type": "input", "value": "日期", "desc": "输入日期"}, {"text": "拖拽css://div:nth-child(7) > i移动379,214", "element": "css://div:nth-child(7) > i", "type": "drag", "value": {"type": "relative", "x": "379", "y": "214"}, "desc": "拖拽css://div:nth-child(7) > i移动379,214"}, {"text": "点击css://div:nth-child(7) > div > div > div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(7) > div > div > div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击 1}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入下拉选择", "type": "input", "value": "下拉选择", "desc": "输入下拉选择"}, {"text": "拖拽css://div:nth-child(1) > div:nth-child(8) > div移动501,217", "element": "css://div:nth-child(1) > div:nth-child(8) > div", "type": "drag", "value": {"type": "relative", "x": "501", "y": "217"}, "desc": "拖拽css://div:nth-child(1) > div:nth-child(8) > div移动501,217"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入单选", "type": "input", "value": "单选", "desc": "输入单选"}, {"text": "拖拽css://div:nth-child(9) > i移动435,230", "element": "css://div:nth-child(9) > i", "type": "drag", "value": {"type": "relative", "x": "435", "y": "230"}, "desc": "拖拽css://div:nth-child(9) > i移动435,230"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入多选", "type": "input", "value": "多选", "desc": "输入多选"}, {"text": "拖拽css://div:nth-child(10) > i移动256,-124", "element": "css://div:nth-child(10) > i", "type": "drag", "value": {"type": "relative", "x": "256", "y": "-124"}, "desc": "拖拽css://div:nth-child(10) > i移动256,-124"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入勾选", "type": "input", "value": "勾选", "desc": "输入勾选"}, {"text": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(11) > div移动554,307", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(11) > div", "type": "drag", "value": {"type": "relative", "x": "554", "y": "307"}, "desc": "拖拽css://div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(11) > div移动554,307"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入图片", "type": "input", "value": "图片", "desc": "输入图片"}, {"text": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1) > div移动400,314", "element": "css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1) > div", "type": "drag", "value": {"type": "relative", "x": "400", "y": "314"}, "desc": "拖拽css://div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(2) > div:nth-child(1) > div移动400,314"}, {"text": "点击css://div:nth-child(2) > div > div > label > span > span", "type": "click", "element": "css://div:nth-child(2) > div > div > label > span > span", "desc": "点击${点击}"}, {"text": "点击css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > div > div:nth-child(2) > div:nth-child(2) > input", "desc": "点击${点击请输入控件编码}"}, {"text": "输入签署区", "type": "input", "value": "签署区", "desc": "输入签署区"}, {"text": "点击css://div:nth-child(1) > button:nth-child(2) > span > span > span", "type": "click", "element": "css://div:nth-child(1) > button:nth-child(2) > span > span > span", "desc": "点击${点击保存模板}"}, {"text": "等待5秒", "type": "wait", "value": "5000", "desc": "等待5秒"}, {"text": "使用正则/flowTemplateId=([^&]+)/提取URL中的数据作为变量signTemplateId", "type": "captureURL", "value": {"regex": "/flowTemplateId=([^&]+)/", "variableName": "signTemplateId"}, "desc": "使用正则/flowTemplateId=([^&]+)/提取URL中的数据作为变量signTemplateId"}, {"text": "点击css://button > p", "type": "click", "element": "css://button > p", "desc": "点击${点击立即开启}"}, {"text": "请求接口", "type": "request", "value": "[\n      {\n     curl: `curl --location --request GET 'http://in-test-openapi.tsign.cn/v3/sign-templates/detail?signTemplateId={signTemplateId}&orgId=265210bd57734f61aabd5e363396e5d2&queryComponents=true' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: **********' \\\n--header 'X-Tsign-Open-App-Secret: a52af705a618566e0a433b9f789cdf38'`,\n     data: {\n participantId:\"data.participants[0].participantId\",\nfileId:\"data.docs[0].fileId\"\n    }\n  },{\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/sign-flow/create-by-sign-template' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: **********' \\\n--header 'X-Tsign-Open-App-Secret: a52af705a618566e0a433b9f789cdf38' \\\n--header 'Content-Type: application/json' \\\n--data '{\n  \"signTemplateId\": \"{signTemplateId}\",\n  \"signFlowConfig\": {\n    \"signFlowTitle\": \"api流程模板发起081601\",\n    \"autoFinish\": true,\n    \"autoStart\": true,\n    \"noticeConfig\": {\n      \"noticeTypes\": \"1\"\n    },\n    \"notifyUrl\": \"\",\n    \"signConfig\": {\n      \"availableSignClientTypes\": \"1\",\n      \"autoFillAndSubmit\": false,\n      \"editComponentValue\": false\n    }\n  },\n  \"participants\": [\n    {\n      \"participantId\": \"{participantId}\",\n      \"psnParticipant\": {\n        \"psnAccount\": \"***********\",\n        \"psnName\": \"谢佳\"\n      }\n    }\n  ],\n  \"components\": [\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"单行文本\",\n        \"componentValue\": \"哈哈哈\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"多行文本\",\n        \"componentValue\": \"测试abc\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"身份证号\",\n        \"componentValue\": \"***************\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"手机号\",\n        \"componentValue\": \"***********\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"日期\",\n        \"componentValue\": \"2025-08-10\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"数字\",\n        \"componentValue\": \"123\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"下拉选择\",\n        \"componentValue\": \"0\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"单选\",\n        \"componentValue\": \"1\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"多选\",\n        \"componentValue\": \"[0,1,2]\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"勾选\",\n        \"componentValue\": \"true\"\n    },\n    {\n        \"fileId\": \"{fileId}\",\n        \"componentKey\": \"图片\",\n        \"componentValue\": \"f0a318f9e1394d9e8779f8953599d849\"\n    }\n  ]\n}'`,\n     data: {\n signFlowId:\"data.signFlowId\"\n    }\n  }\n]", "desc": "请求接口"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/sign-flow/{signFlowId}/draft-url' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: **********' \\\n--header 'X-Tsign-Open-App-Secret: a52af705a618566e0a433b9f789cdf38' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"operator\": {\n        \"psnAccount\": \"***********\"\n    },\n    \"needLogin\": false,\n    \"urlType\": 2,\n    \"clientType\": \"ALL\",\n    \"redirectConfig1\": {\n        \"redirectUrl\": \"http://esign.cn\",\n        \"redirectDelayTime\": 3\n    }\n}'`,\n     data: {\n      url:\"data.draftShortUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "选择确认提交.button", "type": "choose", "value": "确认提交.button", "desc": "选择确认提交.button"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "判断css://div:nth-child(3) > div > button:nth-child(2) > span的内容为提交签署", "type": "assert", "element": "css://div:nth-child(3) > div > button:nth-child(2) > span", "value": "have.text:提交签署", "desc": "ER-判断css://div:nth-child(3) > div > button:nth-child(2) > span的内容为提交签署"}, {"text": "请求接口", "type": "request", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/sign-templates/delete' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: **********' \\\n--header 'X-Tsign-Open-App-Secret: a52af705a618566e0a433b9f789cdf38' \\\n--header 'Content-Type: application/json' \\\n--data '{\n  \"orgId\": \"265210bd57734f61aabd5e363396e5d2\",\n  \"transactorPsnId\": \"276b5a8f2a964142bb1ebcf0dd82bf72\",\n  \"signTemplateId\": \"{signTemplateId}\"\n}'`\n  }\n]", "desc": "请求接口"}]