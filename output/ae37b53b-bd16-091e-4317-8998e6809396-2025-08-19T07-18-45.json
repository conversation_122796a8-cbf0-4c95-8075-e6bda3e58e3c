[{"text": "点击css://form > div:nth-child(1) > input", "type": "click", "element": "css://form > div:nth-child(1) > input", "desc": "点击${点击请输入手机号/邮箱}"}, {"text": "输入********************", "type": "input", "value": "<EMAIL>", "desc": "输入********************"}, {"text": "点击css://div:nth-child(2) > input", "type": "click", "element": "css://div:nth-child(2) > input", "desc": "点击${点击请输入密码}"}, {"text": "输入test1234", "type": "input", "value": "test1234", "desc": "输入test1234"}, {"text": "点击css://div:nth-child(1) > div:nth-child(3) > button > span", "type": "click", "element": "css://div:nth-child(1) > div:nth-child(3) > button > span", "desc": "点击${点击登录}"}, {"text": "选择我已阅读并同意.button", "type": "choose", "value": "我已阅读并同意.button", "desc": "选择我已阅读并同意.button"}, {"text": "点击css://div:nth-child(1) > div > p:nth-child(1)", "type": "click", "element": "css://div:nth-child(1) > div > p:nth-child(1)", "desc": "点击${点击上传文件}"}, {"text": "点击css://div:nth-child(2) > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > div > div > div > div:nth-child(1) > span", "type": "click", "element": "css://div:nth-child(2) > div:nth-child(2) > div > div > div:nth-child(1) > div:nth-child(2) > div > div > div > div:nth-child(1) > span", "desc": "点击${点击添加签署文件}"}, {"text": "使用css://div:nth-child(2) > input上传文件", "type": "upload", "element": "css://div:nth-child(2) > input", "desc": "使用css://div:nth-child(2) > input上传文件"}, {"text": "点击css://#guideTaskName:nth-child(1) > div > div:nth-child(1) > input", "type": "click", "element": "css://#guideTaskName:nth-child(1) > div > div:nth-child(1) > input", "desc": "点击${点击请输入合同名称}"}, {"text": "输入2页文档081901", "type": "input", "value": "2页文档081901", "desc": "输入2页文档081901"}, {"text": "选择直接发起.button", "type": "choose", "value": "直接发起.button", "desc": "选择直接发起.button"}, {"text": "等待5秒", "type": "wait", "value": "5000", "desc": "等待5秒"}, {"text": "判断css://#qiankun_wrapper > div > div:nth-child(2) > div > div:nth-child(2)的内容包含发起成功", "type": "assert", "element": "css://#qiankun_wrapper > div > div:nth-child(2) > div > div:nth-child(2)", "value": "include.text:发起成功", "desc": "ER-判断css://#qiankun_wrapper > div > div:nth-child(2) > div > div:nth-child(2)的内容包含发起成功"}]