[{"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/doc-templates/doc-template-create-url' \\\n--header 'X-Tsign-Open-App-Id: 7876723440' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'x-tsign-open-app-secret: 7e08e4c47a2aaddc02bddc129264b760' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateName\": \"api静态文件模板082301\",\n    \"docTemplateType\": 0,\n    \"fileId\": \"0a4cb8d542fc42d189dea1b78c98227d\",\n    \"redirectUrl\": \"\"\n}'`,\n     data: {\n      url:\"data.docTemplateCreateUrl\",\ndocTemplateId:\"data.docTemplateId\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "拖拽单行文本移动-306,-373", "element": "单行文本", "type": "drag", "value": {"type": "relative", "x": "-306", "y": "-373"}, "desc": "拖拽单行文本移动-306,-373"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入单行文本", "type": "input", "value": "单行文本", "desc": "输入单行文本"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽css://[data-esign-inject-name=\"多行文本\"] > div:nth-child(2) > div > span移动-437,-344", "element": "css://[data-esign-inject-name=\"多行文本\"] > div:nth-child(2) > div > span", "type": "drag", "value": {"type": "relative", "x": "-437", "y": "-344"}, "desc": "拖拽css://[data-esign-inject-name=\"多行文本\"] > div:nth-child(2) > div > span移动-437,-344"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入多行文本", "type": "input", "value": "多行文本", "desc": "输入多行文本"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽数字移动-401,-334", "element": "数字", "type": "drag", "value": {"type": "relative", "x": "-401", "y": "-334"}, "desc": "拖拽数字移动-401,-334"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入数字", "type": "input", "value": "数字", "desc": "输入数字"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽日期移动-545,-312", "element": "日期", "type": "drag", "value": {"type": "relative", "x": "-545", "y": "-312"}, "desc": "拖拽日期移动-545,-312"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入日期", "type": "input", "value": "日期", "desc": "输入日期"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽手机号移动-442,-347", "element": "手机号", "type": "drag", "value": {"type": "relative", "x": "-442", "y": "-347"}, "desc": "拖拽手机号移动-442,-347"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入手机号", "type": "input", "value": "手机号", "desc": "输入手机号"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽身份证号移动-565,-325", "element": "身份证号", "type": "drag", "value": {"type": "relative", "x": "-565", "y": "-325"}, "desc": "拖拽身份证号移动-565,-325"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入身份证", "type": "input", "value": "身份证", "desc": "输入身份证"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽css://[data-esign-inject-name=\"单选控件\"]:nth-child(1) > span:nth-child(2)移动-253,-396", "element": "css://[data-esign-inject-name=\"单选控件\"]:nth-child(1) > span:nth-child(2)", "type": "drag", "value": {"type": "relative", "x": "-253", "y": "-396"}, "desc": "拖拽css://[data-esign-inject-name=\"单选控件\"]:nth-child(1) > span:nth-child(2)移动-253,-396"}, {"text": "点击css://#tab-selected-list:nth-child(3) > span > i", "type": "click", "element": "css://#tab-selected-list:nth-child(3) > span > i", "desc": "点击${点击 1}"}, {"text": "点击css://div:nth-child(7) > div:nth-child(3) > span", "type": "click", "element": "css://div:nth-child(7) > div:nth-child(3) > span", "desc": "点击${点击单选1}"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入单选", "type": "input", "value": "单选", "desc": "输入单选"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "点击css://#tab-list:nth-child(2) > span", "type": "click", "element": "css://#tab-list:nth-child(2) > span", "desc": "点击${点击 2}"}, {"text": "拖拽css://[data-esign-inject-name=\"多选控件\"]:nth-child(1)移动-514,-310", "element": "css://[data-esign-inject-name=\"多选控件\"]:nth-child(1)", "type": "drag", "value": {"type": "relative", "x": "-514", "y": "-310"}, "desc": "拖拽css://[data-esign-inject-name=\"多选控件\"]:nth-child(1)移动-514,-310"}, {"text": "点击css://#tab-selected-list:nth-child(3) > span > i", "type": "click", "element": "css://#tab-selected-list:nth-child(3) > span > i", "desc": "点击${点击 1}"}, {"text": "点击css://div:nth-child(8) > div:nth-child(3) > span", "type": "click", "element": "css://div:nth-child(8) > div:nth-child(3) > span", "desc": "点击${点击多选1}"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入多选", "type": "input", "value": "多选", "desc": "输入多选"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "点击css://#tab-list:nth-child(2) > span > i", "type": "click", "element": "css://#tab-list:nth-child(2) > span > i", "desc": "点击${点击 4}"}, {"text": "拖拽勾选移动-305,-345", "element": "勾选", "type": "drag", "value": {"type": "relative", "x": "-305", "y": "-345"}, "desc": "拖拽勾选移动-305,-345"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入勾选", "type": "input", "value": "勾选", "desc": "输入勾选"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽下拉选择移动-402,-295", "element": "下拉选择", "type": "drag", "value": {"type": "relative", "x": "-402", "y": "-295"}, "desc": "拖拽下拉选择移动-402,-295"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入下拉选择", "type": "input", "value": "下拉选择", "desc": "输入下拉选择"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽图片控件移动-470,-190", "element": "图片控件", "type": "drag", "value": {"type": "relative", "x": "-470", "y": "-190"}, "desc": "拖拽图片控件移动-470,-190"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入图片", "type": "input", "value": "图片", "desc": "输入图片"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽人民币大写移动-411,-248", "element": "人民币大写", "type": "drag", "value": {"type": "relative", "x": "-411", "y": "-248"}, "desc": "拖拽人民币大写移动-411,-248"}, {"text": "点击css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "type": "click", "element": "css://[data-esign-inject-name=\"控件编码\"] > div > div:nth-child(2) > input", "desc": "点击${点击输入控件编码}"}, {"text": "输入人民币大写", "type": "input", "value": "人民币大写", "desc": "输入人民币大写"}, {"text": "点击css://div:nth-child(1) > div:nth-child(2) > div > div > div:nth-child(2) > div > div:nth-child(2) > div:nth-child(1) > input", "type": "click", "element": "css://div:nth-child(1) > div:nth-child(2) > div > div > div:nth-child(2) > div > div:nth-child(2) > div:nth-child(1) > input", "desc": "点击${点击请选择}"}, {"text": "选择数字1.p", "type": "choose", "value": "数字1.p", "desc": "选择数字1.p"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽签署区控件移动-545,121", "element": "签署区控件", "type": "drag", "value": {"type": "relative", "x": "-545", "y": "121"}, "desc": "拖拽签署区控件移动-545,121"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽骑缝签署区控件移动-335,-138", "element": "骑缝签署区控件", "type": "drag", "value": {"type": "relative", "x": "-335", "y": "-138"}, "desc": "拖拽骑缝签署区控件移动-335,-138"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "拖拽css://[data-esign-inject-name=\"备注签署区控件\"] > span移动-333,133", "element": "css://[data-esign-inject-name=\"备注签署区控件\"] > span", "type": "drag", "value": {"type": "relative", "x": "-333", "y": "133"}, "desc": "拖拽css://[data-esign-inject-name=\"备注签署区控件\"] > span移动-333,133"}, {"text": "点击css://[data-esign-inject-name=\"关闭属性面板\"]", "type": "click", "element": "css://[data-esign-inject-name=\"关闭属性面板\"]", "desc": "点击${点击}"}, {"text": "选择保存.button", "type": "choose", "value": "保存.button", "desc": "选择保存.button"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/files/create-by-doc-template' \\\n--header 'X-Tsign-Open-App-Id: 7876723440' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'x-tsign-open-app-secret: 7e08e4c47a2aaddc02bddc129264b760' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateId\":\"{docTemplateId}\",\n    \"fileName\":\"2页文档\",\n    \"components\":[\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"单行文本\",\n            \"componentValue\":\"haha\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"多行文本\",\n            \"componentValue\":\"test123\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"数字\",\n            \"componentValue\":\"123\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"日期\",\n            \"componentValue\":\"2025-08-23\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"身份证\",\n            \"componentValue\":\"111111111111111\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"手机号\",\n            \"componentValue\":\"13506712563\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"单选\",\n            \"componentValue\":\"0\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"多选\",\n            \"componentValue\":\"[0,1]\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"勾选\",\n            \"componentValue\":true\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"下拉选择\",\n            \"componentValue\":\"1\"\n        },\n        {\n            \"componentId\":\"\",\n            \"componentKey\":\"图片\",\n            \"componentValue\":\"f0a318f9e1394d9e8779f8953599d849\"\n        }\n    ]\n}'`,\n     data: {\n      fileId:\"data.fileId\",\nurl:\"data.fileDownloadUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}]