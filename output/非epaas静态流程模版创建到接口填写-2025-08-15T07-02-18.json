[{"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST '${开放网关host}/v3/sign-templates/sign-template-create-url' \\\n--header 'X-Tsign-Open-Auth-Mode: secret' \\\n--header 'X-Tsign-Open-App-Id: ${非epaas_appid}' \\\n--header 'X-Tsign-Open-App-Secret: ${非epaas_appSecret}' \\\n--header 'X-Tsign-Service-Group: ${group}' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"orgId\": \"${企业oid1}\",\n    \"transactorPsnId\": \"${个人oid1}\",\n    \"redirectUrl\": \"\"\n}'`,\n     data: {\n      url:\"data.signTemplateCreateUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "等待10秒", "type": "wait", "value": "10000", "desc": "等待10秒"}, {"text": "请求接口并跳转", "type": "requestAndRedirect", "value": "[\n      {\n     curl: `curl --location --request POST 'http://in-test-openapi.tsign.cn/v3/files/create-by-doc-template' \\\n--header 'X-Tsign-Open-App-Id: 7876687227' \\\n--header 'X-Tsign-Open-Auth-Mode: simple' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"docTemplateId\": \"{docTemplateId}\",\n    \"fileName\": \"1页文档\",\n    \"components\": [\n        {\n            \"componentKey\": \"单行文本\",\n            \"componentValue\": \"哈哈哈\"\n        },\n        {\n            \"componentKey\": \"多行文本\",\n            \"componentValue\": \"测试123\"\n        },\n        {\n            \"componentKey\": \"身份证号\",\n            \"componentValue\": \"111111111111111\"\n        },\n        {\n            \"componentKey\": \"数字\",\n            \"componentValue\": \"111\"\n        },\n        {\n            \"componentKey\": \"手机号\",\n            \"componentValue\": \"13506712563\"\n        },\n        {\n            \"componentKey\": \"日期\",\n            \"componentValue\": \"2025-08-09\"\n        },\n        {\n            \"componentKey\": \"下拉选择\",\n            \"componentValue\": \"0\"\n        },\n        {\n            \"componentKey\": \"单选\",\n            \"componentValue\": \"1\"\n        },\n        {\n            \"componentKey\": \"多选\",\n            \"componentValue\": \"[0,1,2]\"\n        },\n        {\n            \"componentKey\": \"勾选\",\n            \"componentValue\": \"true\"\n        },\n        {\n            \"componentKey\": \"图片\",\n            \"componentValue\": \"f0a318f9e1394d9e8779f8953599d849\"\n        }\n    ]\n}'`,\n     data: {\n      fileId:\"data.fileId\",\nurl:\"data.fileDownloadUrl\"\n    }\n  }\n]", "desc": "请求接口并跳转"}]