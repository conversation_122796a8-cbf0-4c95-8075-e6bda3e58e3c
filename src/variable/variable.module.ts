import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Variable } from './variable.entity';
import { Config } from '../config/config.entity';
import { Case } from '../case/case.entity';
import { VariableService } from './variable.service';
import { VariableController } from './variable.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Variable, Config, Case])],
  providers: [VariableService],
  controllers: [VariableController],
  exports: [VariableService, TypeOrmModule.forFeature([Variable])]
})
export class VariableModule {}