import { Entity, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Config } from '../config/config.entity';
import {JsonTextTransformer} from '../util'
@Entity('monitor_uitest_variable')

export class Variable {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 64 })
  variableName: string;

  @ManyToMany(() => Config, config => config.variables)
  @JoinTable()
  configs: Config[];
  @Column({ type: 'text',transformer: JsonTextTransformer })
  prefix: string;

  @Column({ type: 'text',transformer: JsonTextTransformer, })
  suffix: string;

  @Column({ type: 'text',transformer: JsonTextTransformer, })
  data: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  app: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  creator: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  updater: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}