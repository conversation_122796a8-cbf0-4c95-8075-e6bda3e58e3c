import { Controller, Get, Post, Body, Param, Query, Delete } from '@nestjs/common';
import { VariableService } from './variable.service';
import { CreateVariableInfoDto, UpdateVariableInfoDto } from './dto/variable-info.dto';

@Controller('variables')
export class VariableController {
  constructor(private readonly variableService: VariableService) {}

  @Post('create')
  create(@Body() createVariableDto: CreateVariableInfoDto) {
    return this.variableService.create(createVariableDto);
  }

  @Get('list')
  findAll(@Query('') query?: any) {
    return this.variableService.paginate(query);
  }

  @Get('search')
  async searchByKeyword(@Query('keyword') keyword?: string) {
    return this.variableService.searchByKeyword(keyword);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.variableService.findOne(+id);
  }

  @Post(':id')
  update(@Param('id') id: string, @Body() updateVariableDto: UpdateVariableInfoDto) {
    return this.variableService.update(+id, updateVariableDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.variableService.remove(+id);
  }

  @Get(':id/configs')
  findReferencedConfigs(@Param('id') id: string) {
    return this.variableService.findReferencedConfigs(+id);
  }
}