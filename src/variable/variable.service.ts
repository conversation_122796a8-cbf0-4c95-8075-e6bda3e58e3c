import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { Variable } from './variable.entity';
import { Config } from '../config/config.entity';
import { Case } from '../case/case.entity';
import { CreateVariableInfoDto, UpdateVariableInfoDto } from './dto/variable-info.dto';

@Injectable()
export class VariableService {
  constructor(
    @InjectRepository(Variable)
    private variableRepository: Repository<Variable>,
    @InjectRepository(Config)
    private configRepository: Repository<Config>,
    @InjectRepository(Case)
    private caseRepository: Repository<Case>,
  ) {}

  async create(createVariableDto: CreateVariableInfoDto): Promise<Variable> {
    const { configId, ...variableData } = createVariableDto;
    
    // 清理空字符串字段，避免数据库约束错误
    if (variableData.updater === '') {
      delete variableData.updater;
    }
    if (variableData.creator === '') {
      delete variableData.creator;
    }
    if (variableData.app === '') {
      delete variableData.app;
    }
    
    const variable = this.variableRepository.create(variableData);
    
    // 保存variable
    const savedVariable = await this.variableRepository.save(variable);
    
    // 如果提供了configId，建立关联关系
    if (configId) {
      // 处理configId数组或单个值
      const configIds = Array.isArray(configId) ? configId : [configId];
      
      for (const configIdItem of configIds) {
        const config = await this.configRepository.findOne({ 
          where: { id: configIdItem },
          relations: ['variables']
        });
        
        if (!config) {
          throw new Error(`Config with id ${configIdItem} not found`);
        }
        
        // 建立关联关系
        if (!config.variables) {
          config.variables = [];
        }
        
        // 检查是否已经关联
        const isAlreadyLinked = config.variables.some(v => v.id === savedVariable.id);
        if (!isAlreadyLinked) {
          config.variables.push(savedVariable);
          await this.configRepository.save(config);
        }
      }
    }
    
    return savedVariable;
  }

  async paginate(query?: any): Promise<{ data: Variable[]; page: number; total: number }> {
    let queryBuilder = this.variableRepository.createQueryBuilder('variable')
      .leftJoinAndSelect('variable.configs', 'config');
    
    // 支持变量名搜索
    if (query?.variableName) {
      queryBuilder = queryBuilder.andWhere('variable.variableName LIKE :variableName', { 
        variableName: `%${query.variableName}%` 
      });
    }
    
    // 支持业务域搜索
    if (query?.app) {
      queryBuilder = queryBuilder.andWhere('variable.app LIKE :app', { 
        app: `%${query.app}%` 
      });
    }
    
    // 支持更新人搜索
    if (query?.updater) {
      queryBuilder = queryBuilder.andWhere('variable.updater LIKE :updater', { 
        updater: `%${query.updater}%` 
      });
    }
    
    // 支持创建人搜索
    if (query?.creator) {
      queryBuilder = queryBuilder.andWhere('variable.creator LIKE :creator', { 
        creator: `%${query.creator}%` 
      });
    }
    
    // 支持按配置ID查询
    if (query?.configId) {
      queryBuilder = queryBuilder.andWhere('config.id = :configId', { configId: query.configId });
    }

    const [data, total] = await queryBuilder
      .skip(query?.limit * (query?.page - 1) || 0)
      .take(query?.limit || 10)
      .orderBy('variable.id', 'DESC')
      .getManyAndCount();
    
    // 为每个变量添加配置名称信息
    const dataWithConfigNames = data.map(variable => ({
      ...variable,
      configNames: variable.configs ? variable.configs.map(config => config.configName) : []
    }));
    
    return {
      data: dataWithConfigNames,
      page: query?.page || 1,
      total,
    };
  }

  async searchByKeyword(keyword?: string): Promise<Variable[]> {
      if (keyword) {
        return this.variableRepository.find({
          where: {
            variableName: Like(`%${keyword}%`)
          },
          take: 1000
        });
      }
      return this.variableRepository.find({
        take: 1000
      });
  }

  async findByIds(ids: number[]): Promise<Variable[]> {
    return this.variableRepository.find({ where: { id: In(ids) } });
  }

  async findOne(id: number): Promise<Variable> {
    const variable = await this.variableRepository.findOne({ 
      where: { id },
      relations: ['configs']
    });
    if (!variable) {
      throw new Error(`Variable with id ${id} not found`);
    }
    return variable;
  }

  async update(id: number, updateVariableDto: UpdateVariableInfoDto): Promise<Variable> {
    const { configId, ...variableData } = updateVariableDto;
    
    // 更新变量基本信息（排除关系字段）
    const updateData = { ...variableData };
    if ('configs' in updateData) {
      delete (updateData as any).configs; // 确保不包含关系字段
    }
    await this.variableRepository.update(id, updateData);
    
    // 如果提供了configId，更新关联关系
    if (configId !== undefined) {
      const variable = await this.variableRepository.findOne({ 
        where: { id },
        relations: ['configs']
      });
      
      if (!variable) {
        throw new Error(`Variable with id ${id} not found`);
      }
      
      // 处理configId数组或单个值
      const configIds = Array.isArray(configId) ? configId : (configId ? [configId] : []);
      
      // 清除现有关联关系
      if (variable.configs && variable.configs.length > 0) {
        for (const existingConfig of variable.configs) {
          const config = await this.configRepository.findOne({
            where: { id: existingConfig.id },
            relations: ['variables']
          });
          if (config && config.variables) {
            config.variables = config.variables.filter(v => v.id !== variable.id);
            await this.configRepository.save(config);
          }
        }
      }
      
      // 如果configIds不为空，添加新的配置关联
      if (configIds.length > 0) {
        for (const configIdItem of configIds) {
          const config = await this.configRepository.findOne({ 
            where: { id: configIdItem },
            relations: ['variables']
          });
          
          if (!config) {
            throw new Error(`Config with id ${configIdItem} not found`);
          }
          
          // 检查是否已经关联
          const isAlreadyLinked = config.variables && config.variables.some(v => v.id === variable.id);
          if (!isAlreadyLinked) {
            if (!config.variables) {
              config.variables = [];
            }
            config.variables.push(variable);
            await this.configRepository.save(config);
          }
        }
      }
    }
    
    const variable = await this.variableRepository.findOne({ 
      where: { id },
      relations: ['configs']
    });
    if (!variable) {
      throw new Error(`Variable with id ${id} not found`);
    }
    return variable;
  }

  async remove(id: number): Promise<void> {
    const variable = await this.variableRepository.findOne({ 
      where: { id },
      relations: ['configs'] 
    });
    
    if (!variable) {
      throw new Error(`Variable with id ${id} not found`);
    }
    
    if (variable.configs && variable.configs.length > 0) {
      throw new Error('Cannot delete variable as it is referenced by one or more configs');
    }
    
    await this.variableRepository.delete(id);
  }

  async findReferencedConfigs(id: number) {
    const variable = await this.variableRepository.findOne({ 
      where: { id },
      relations: ['configs']
    });
    
    if (!variable) {
      throw new Error(`Variable with id ${id} not found`);
    }
    
    // 为每个配置查询关联的用例
    const configsWithCases = await Promise.all(
      (variable.configs || []).map(async (config) => {
        const cases = await this.caseRepository.find({
          where: { config: { id: config.id } }
        });
        
        return {
          ...config,
          cases: cases || []
        };
      })
    );
    
    return configsWithCases;
  }
}