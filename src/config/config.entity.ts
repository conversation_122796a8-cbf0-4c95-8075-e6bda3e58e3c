import { Entity, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable, UpdateDateColumn } from 'typeorm';

import { Variable } from '../variable/variable.entity'; // 导入 Script 实体
import {JsonTextTransformer} from '../util'
@Entity('monitor_uitest_config')
export class Config {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: '' })
  configName: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ default: 'null'})
  creator: string;

  @Column({ type: 'varchar', length: 64, comment: '业务域', nullable: true })
  app?: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: '' })
  url: string;

  @Column({ default: '' })
  width: string;

  @Column({ default: '' })
  height: string;

  @Column({ default: '' })
  env: string;

  @Column({ type: 'text' ,transformer: JsonTextTransformer})
  localstorage: string;

  @Column({ type: 'text' ,transformer: JsonTextTransformer})
  session: string;

  @Column({ type: 'text' ,transformer: JsonTextTransformer})
  cookie: string;

  @Column({ type: 'text' ,transformer: JsonTextTransformer})
  request: string;

  @Column({ type: 'text' ,transformer: JsonTextTransformer, nullable: true })
  envConfigs: string;

  @ManyToMany(() => Variable, variable => variable.configs)
  @JoinTable()
  variables: Variable[];
}