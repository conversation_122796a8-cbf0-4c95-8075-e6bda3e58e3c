import { ConfigService } from './config.service';
import { CreateConfigInfoDto, UpdateConfigInfoDto } from './dto/config-info.dto';
import { Config } from './config.entity';
import { Controller, Get, Post, Body, Put, Param, Delete, Query, BadRequestException } from '@nestjs/common';
import { VariableService } from '../variable/variable.service';
@Controller('configs')
export class ConfigController {
  constructor(
    private readonly configService: ConfigService,
    private readonly variableService: VariableService) {}

  @Post('create')
  async create(@Body() createConfigDto: CreateConfigInfoDto): Promise<Config> {

    // 处理envConfigs字段中的变量ID验证
    if (createConfigDto.envConfigs) {
      const allVariableIds = new Set<number>();
      
      // 收集所有环境配置中的变量ID
      Object.values(createConfigDto.envConfigs).forEach((envConfig: any) => {
        if (envConfig.variableValues && Array.isArray(envConfig.variableValues)) {
          envConfig.variableValues.forEach((id: number) => allVariableIds.add(id));
        }
      });

      // 验证所有变量ID的有效性
      if (allVariableIds.size > 0) {
        const allIds = Array.from(allVariableIds);
        const allVariables = await this.variableService.findByIds(allIds);
        
        if (allVariables.length !== allIds.length) {
          const existingIds = allVariables.map(v => v.id);
          const invalidIds = allIds.filter(id => !existingIds.includes(id));
          throw new BadRequestException(`envConfigs中包含无效的变量ID: ${invalidIds.join(', ')}`);
        }
      }
    }

    // 合并所有变量ID（去重）
    const allVariableIds = new Set<number>();
    if (createConfigDto.variables) {
      createConfigDto.variables.forEach(data => allVariableIds.add(data));
    }
    if (createConfigDto.envConfigs) {
      Object.values(createConfigDto.envConfigs).forEach((envConfig: any) => {
        if (envConfig.variableValues && Array.isArray(envConfig.variableValues)) {
          envConfig.variableValues.forEach((id: number) => allVariableIds.add(id));
        }
      });
    }

    // 获取所有变量对象
    const allVariables = allVariableIds.size > 0 ? await this.variableService.findByIds(Array.from(allVariableIds)) : [];

    const partialConfigData: Partial<Config> = {
      ...createConfigDto,
      variables: allVariables, // 使用Variable对象数组
    };
    // 确保 configName 不是 undefined
    if (!partialConfigData.configName) {
      throw new BadRequestException('配置名称不能为空');
    }
    // 先将 partialConfigData 转换为 unknown 类型，再转换为 CreateConfigInfoDto 类型
    return this.configService.create(partialConfigData as unknown as CreateConfigInfoDto);
  }

  @Get('list')
  findAll(@Query('') query?: any){
    return this.configService.paginate(query);
  }

  @Get('search')
  async searchByKeyword(@Query('keyword') keyword?: string): Promise<Config[]> {
    return this.configService.searchByKeyword(keyword);
  }

  @Get('detail')
  findOne(@Query('id') id?: number): Promise<Config> {
    if (id === undefined) {
      throw new BadRequestException('id 是必需参数');
    }
    return this.configService.findOne(id);
  }

  @Get(':id')
  findById(@Param('id') id: string): Promise<Config> {
    const configId = parseInt(id, 10);
    if (isNaN(configId)) {
      throw new BadRequestException('无效的配置ID');
    }
    return this.configService.findOne(configId);
  }

  @Post(':id')
  async update(@Param('id') id: string, @Body() updateConfigDto: UpdateConfigInfoDto): Promise<Config> {
    // 直接使用传入的variables数组，支持增删改操作
    // 如果需要从envConfigs中提取变量ID，可以在前端处理或单独处理
    console.log('更新配置:', updateConfigDto);
    return this.configService.update(+id, updateConfigDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.configService.remove(+id);
  }
}