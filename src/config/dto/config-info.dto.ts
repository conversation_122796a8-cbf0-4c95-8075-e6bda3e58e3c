

export class CreateConfigInfoDto {
    configName: string;
    creator: string;
    app?: string;
    url: string;
    width: string;
    height: string;
    env: string;
    localstorage: string;
    session: string;
    cookie: string;
    request: string;
    envConfigs?: any;
    variables?: any[];
}

export class UpdateConfigInfoDto {
    configName?: string;
    creator?: string;
    app?: string;
    url?: string;
    width?: string;
    height?: string;
    env?: string;
    localstorage?: string;
    session?: string;
    cookie?: string;
    request?: string;
    envConfigs?: any;
    variables?: any[];
}