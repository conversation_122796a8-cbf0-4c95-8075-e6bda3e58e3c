import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Config } from './config.entity';
import { Case } from '../case/case.entity';
import { ConfigService } from './config.service';
import { ConfigController } from './config.controller';
import { VariableModule } from '../variable/variable.module';
import { VariableService } from '../variable/variable.service';
@Module({
  imports: [TypeOrmModule.forFeature([Config, Case]),VariableModule],
  controllers: [ConfigController],
  providers: [ConfigService, VariableService],
  exports: [ConfigService]
})
export class TestConfigModule {}