import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Config } from './config.entity';
import { Case } from '../case/case.entity';
import { CreateConfigInfoDto, UpdateConfigInfoDto } from './dto/config-info.dto';

@Injectable()
export class ConfigService {
  constructor(
    @InjectRepository(Config)
    private configRepository: Repository<Config>,
    @InjectRepository(Case)
    private caseRepository: Repository<Case>,
  ) { }

  async create(createConfigDto: CreateConfigInfoDto): Promise<Config> {
    const { variables, ...rest } = createConfigDto;
    const config = this.configRepository.create(rest);
    if (variables) {
      config.variables = variables.map(id => ({ id } as any));
    }
    return await this.configRepository.save(config);
  }
  async paginate(query: any) {
    const { page = 1, limit = 10, ...filters } = query;
    const where = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value) {
        if (key === 'keyword') {
          acc['configName'] = Like(`%${value}%`);
        } else if (['configName', 'creator', 'app'].includes(key)) {
          acc[key] = value;
        }
      }
      return acc;
    }, {});

    const [data, total] = await this.configRepository.findAndCount({
      where,
      skip: (page - 1) * limit,
      take: limit,
      relations: ['variables'],
      join: {
        alias: 'config',
        leftJoinAndSelect: {
          variables: 'config.variables'
        }
      },
      order: {
        id: 'DESC'
      }
    });

    // 为每个配置查询使用它的用例
    const dataWithCases = await Promise.all(
      data.map(async (config) => {
        const cases = await this.caseRepository.find({
          where: { config: { id: config.id } },
          select: ['id', 'caseName']
        });
        return {
          ...config,
          usedByCases: cases.map(c => ({ id: c.id, caseName: c.caseName }))
        };
      })
    );

    return {
      data: dataWithCases,
      total,
      page: Number(page)
    };
  }
  async findAll(name?: string, creator?: string): Promise<Config[]> {
    const queryBuilder = this.configRepository.createQueryBuilder('config');

    if (name) {
      queryBuilder.andWhere('config.name LIKE :name', { name: `%${name}%` });
    }

    if (creator) {
      queryBuilder.andWhere('config.creator LIKE :creator', { creator: `%${creator}%` });
    }

    return await queryBuilder.getMany();
  }

  async findOne(id: number): Promise<Config> {
    const config = await this.configRepository.findOne({
      where: { id },
      relations: ['variables'],
      join: {
        alias: 'config',
        leftJoinAndSelect:{ variables: 'config.variables' }
      }
    });
    if (!config) {
      throw new Error(`Config with id ${id} not found`);
    }
    return config;
  }

  async update(id: number, updateConfigDto: UpdateConfigInfoDto): Promise<Config> {
    const { variables, ...rest } = updateConfigDto;
    await this.configRepository.update(id, rest);
    const config = await this.configRepository.findOne({ 
      where: { id },
      relations: ['variables']
    });
    if (!config) {
      throw new Error(`Config with id ${id} not found`);
    }
    if (variables !== undefined) {
      // 清空现有的变量关系
      config.variables = [];
      await this.configRepository.save(config);
      
      // 设置新的变量关系
      if (variables.length > 0) {
        config.variables = variables.map(id => ({ id } as any));
        await this.configRepository.save(config);
      }
    }
    return config;
  }

  async remove(id: number): Promise<any> {
    // 检查配置是否被case表引用
    const isReferenced = await this.configRepository
      .createQueryBuilder('config')
      .innerJoin('case', 'case', 'case.configId = config.id')
      .where('config.id = :id', { id })
      .getCount();

    if (isReferenced > 0) {
      return {
        code: 400,
        message: `无法删除配置ID ${id}，因为该配置已被用例引用`
      }
    } else {
      await this.configRepository.delete(id);
    }


  }

  async searchByKeyword(keyword?: string): Promise<Config[]> {
    const queryBuilder = this.configRepository.createQueryBuilder('config');

    if (keyword) {
      queryBuilder.where('config.configName LIKE :keyword', { keyword: `%${keyword}%` });
    }

    queryBuilder.orderBy('config.createdAt', 'DESC').take(1000);

    return await queryBuilder.getMany();
  }
}