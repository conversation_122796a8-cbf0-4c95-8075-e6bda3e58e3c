import { Injectable } from '@nestjs/common';
import * as path from 'path';
import axios from 'axios';
import { CaseService } from './case/case.service';
import { TestRecordService } from './record/record.service';
const testService = require('../core/index.js');
const xmindparser = require('../core/xmindparserlib');

interface Task {
  caseId: number;
  operator: string;
  caseName: string;
  browser: string;
  groupName: string;
  notifyUrl: string;
  app: string;
  product: string;
  jsonData: any;
  taskId?: number;
  runId?: string;
  fileTag?: string;
}

@Injectable()
export class AppService {
  constructor(private readonly caseService: CaseService, private readonly recordService: TestRecordService) { }

  // 使用单个运行任务数组维护执行状态
  private runningTasks: Task[] = [];
  private pendingQueue: Task[] = [];
  private readonly MAX_CONCURRENT = 2; // 最大并发数定义为常量

  async addTask(task: Task) {
    // 检查任务是否已存在队列或执行中
    console.log('task add:', task);
    if ([this.pendingQueue, this.runningTasks].some(queue =>
      queue.some(t => t.caseName === task.caseName && t.groupName === task.groupName)
    )) {
      console.log(`用例 ${task.caseName} 已在队列中或执行中`);
      return {
        status: 'error',
        msg: `用例 ${task.caseName} 已在队列中或执行中`
      };
    }
  
    
    
    // 创建数据库执行记录
    try {
      // 生成唯一文件标识
    task.fileTag = task.caseName+Math.floor(new Date().getTime() / 1000);
      const record = await this.recordService.create({
        caseId: task.caseId || 0,
        operator: task.operator,
        caseName: task.caseName,
        browser: task.browser,
        app: task.app,
        product: task.product,
        fileTag: task.fileTag,
        groupName: task.groupName || '未设置分组',
        notifyUrl: task.notifyUrl,
        status: 'pending',
        startTime: new Date(),
        runId: task.runId||'',
        duration: 0,
        userAgent: '',
        env: ''
      });
      task.taskId = record.id;
      console.log('创建数据库执行记录:', record);
    }   catch (e) {
      console.log(e)
    }
    // 添加任务到待处理队列
    this.pendingQueue.push(task);
    const newQueuePosition = this.pendingQueue.length + this.runningTasks.length;  // 修正后的队列位置计算
  
    // 触发队列处理
    this.processQueue();
    
    console.log(`用例 ${task.caseName} 已加入队列，等待中${this.pendingQueue.length}, 执行中${this.runningTasks.length}`);
    return {
      status: 'success',
      msg: `用例 ${task.caseName} 已加入队列，当前队列位置：${newQueuePosition}，等待中${this.pendingQueue.length}, 执行中${this.runningTasks.length}`
    };
  }
  

  private processQueue(): void {
    while (this.runningTasks.length < this.MAX_CONCURRENT
      && this.pendingQueue.length > 0) {
      this.processNextTask();
    }
  }
  private async processNextTask() {
    const task = this.pendingQueue.shift()!;
    this.runningTasks.push(task);
    if(task.taskId){
      await this.recordService.updateFields(task.taskId, {
        status: 'running',
        startTime: new Date(),
      });
    }
    this.executeTask(task)
      .then(async result => await this.handleTaskSuccess(task.taskId, result))
      .catch(async error => await this.handleTaskError(task.taskId, error))
      .finally(async () => await this.finalizeTaskProcessing(task));
  }
  private async executeTask(task: Task): Promise<any> {
    console.log(`### 【${task.caseName}】UI自动测试开始唤起执行，请耐心等待`, task);
    return testService.startWithJson(
      task,
      (reportData) => this.sendResult(reportData, task.notifyUrl),
    );

  }
  sendMsg = async (task, info) => {
    try {
      if (!task.notifyUrl || typeof task.notifyUrl !== 'string') {
        throw new Error('通知URL无效或未提供');
      }

      // 基本URL验证
      try {
        new URL(task.notifyUrl);
      } catch (e) {
        throw new Error(`无效的通知URL格式: ${task.notifyUrl}`);
      }

      await axios.post(
        task.notifyUrl,
        JSON.stringify({
          msgtype: 'markdown',
          markdown: {
            title: '测试结果',
            text: info
          }
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          httpsAgent: null,
          httpAgent: null,
          proxy: false,
        }
      );
    } catch (error) {
      console.error('发送通知失败:', error.message);
    }
  }
  async stopTask(caseName: string, id): Promise<void> {
    try {
      console.log(`正在停止任务 ${caseName}`);
      await testService.stop(caseName);
      // 更新测试记录状态为failed并记录错误信息
      const res = await this.recordService.updateFields(id, {
        status: 'failed',
        endTime: new Date(),
        runInfo: '手动终止任务'
      });
    } catch (error) {
      console.error(`停止任务 ${caseName} 失败:`, error.message);
    } finally {
      // 无论停止是否成功，都从运行队列移除
      this.runningTasks = this.runningTasks.filter(t => t.caseName !== caseName);
      this.processQueue(); // 触发后续任务处理
    }
  }
  private async handleTaskSuccess(id: any, result: any): Promise<void> {

    // 统一更新测试记录状态和结束时间
    const res = await this.recordService.updateFields(id, {
      status: 'success',
      endTime: new Date()
    });

    // 如果有结果数据，记录到测试记录中
    if (result) {
      await this.recordService.log(id, result);
    }
    console.log(`用例 ${res.caseName} 执行成功`);
  }
  private async handleTaskError(id, error: Error): Promise<void> {

    // 更新测试记录状态为failed并记录错误信息
    const res = await this.recordService.updateFields(id, {
      status: 'failed',
      endTime: new Date(),
      runInfo: error.message
    });
    console.error(`用例 ${res.caseName} 执行失败:`, error.message);
    // 发送失败通知
    this.sendMsg(res, `### 【${res.caseName}】UI自动测试执行失败\n错误信息: ${error.message}`);
  }
  private finalizeTaskProcessing(task: Task): void {
    // 使用过滤方式避免引用问题
    this.runningTasks = this.runningTasks.filter(t => t.caseName !== task.caseName);
    this.processQueue(); // 触发后续任务处理
  }
  sendResult = async (reportData, url) => {
    try {
      const notification = {
        timestamp: new Date().toISOString(),
        port: reportData.port,
        caseName: reportData.caseName,
        startTime: reportData.startTime,
        testInfo: JSON.stringify(reportData.caseList),
        browser: reportData.browser,
        duration: (Date.now() - new Date(reportData.startTime).getTime()),
        totalCases: reportData.totalCases,
        isSuccess: reportData.isSuccess,
        userAgent: reportData.userAgent,
        fileTag: reportData.fileTag,
        groupName: reportData.groupName,
        env: process.env.NODE_ENV,
        notifyUrl: url,
        apiBaseUrl: process.env.API_SERVER_URL,
      }
      console.log('[Success] Test finish notification:');
      const notifyUrl = `${process.env.API_SERVER_URL}/notify/finishTest`;
      await axios.post(notifyUrl, notification, {
        headers: {
          'Content-Type': 'application/json',
          'X-Request-Source': 'testcafe-runner'
        }
      });
    } catch (notificationError) {
      console.error('[Critical] Failed to send notification:', notificationError.message);
    }
  }
  async generateXmind(jsonData: object): Promise<any> {
    console.log('jsonData:', jsonData);
    const reportPath = path.join(__dirname, '../core/temp')
    console.log('reportPath:', reportPath);


    // 在文件生成后清除定时器
    let parser = new xmindparser()
    const result = await parser.JSONToXmind(jsonData, reportPath);
    const regex = /[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/;

    const match = result.match(regex);

    if (match) {
      console.log("Extracted UUID:", match[0]);
      return match[0];
    } else {
      console.log("No UUID found.");
      return null;
    }
  }
}
