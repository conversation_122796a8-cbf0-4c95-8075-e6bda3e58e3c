import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TaskProcessorC1 } from './case/task.processor.c1';
import { TaskProcessorC2 } from './case/task.processor.c2';
import { TaskProcessorC3 } from './case/task.processor.c3'; // 导入C3处理器
import { TaskProcessorC4 } from './case/task.processor.c4'; // 导入C4处理器
import { TaskProcessorC5 } from './case/task.processor.c5'; // 导入C5处理器
import { TestRecordModule } from './record/record.module'; // 导入 TestRecordModule
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRecord } from './record/record.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TestRecord]),
    TestRecordModule, // 添加 TestRecordModule 以提供 TestRecordService
  ],
  providers: [
    ...(process.env.APP_ROLE === 'TASK_RUNNER_WORKER' ? [TaskProcessorC1, TaskProcessorC2, TaskProcessorC3, TaskProcessorC4, TaskProcessorC5] : []),
  ],
})
export class WorkerModule {}