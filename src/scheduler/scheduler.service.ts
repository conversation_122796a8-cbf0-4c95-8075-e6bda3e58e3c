import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON> } from "@nestjs/schedule";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class SchedulerService {
    constructor(private readonly http: HttpService) {}

    @Cron("0 0 3 * * *", {
        timeZone: "Asia/Shanghai" // 添加时区配置（北京时间）
    })
    async baseTask() {
        if (process.env.APP_ROLE === "SCHEDULER_WORKER") {
            await this.executeTask();
        }
    }

    // @Cron("0 23 18 13 3 *")
    // async testTask() {
    //     if (process.env.APP_ROLE === 'SCHEDULER_WORKER') {
    //         await this.executeTask();
    //     }
    // }

    async executeTask() {
        // 生成当前日期字符串（格式：YYYYMMDD）
        const dateStr = Date.now();
        try {
            await this.http.axiosRef.post(
                `${process.env.API_SERVER_URL}/cases/runTestGroup`,
                {
                    cases: [25, 26, 27, 28, 29, 30, 31, 32, 33, 36, 38, 39, 40, 41, 42],
                    runId: `${Date.now()}_1`,
                    testSuiteName: `test_SaaS定时任务${dateStr}`,
                    operator: "system",
                    projectType: "debug",
                    strategy: "cases",
                    groupName: "",
                    notifyUrl:
                        "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
                    browser: "chrome:headless --allow-insecure-localhost"
                },
                {
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            );
        } catch (error) {
            console.error("Error running scheduled task:", error);
            /* empty */
        }
        try {
            await this.http.axiosRef.post(
                `${process.env.API_SERVER_URL}/cases/runTestGroup`,
                {
                    cases: [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
                    runId: `${Date.now()}_2`,
                    testSuiteName: `test_签署H5${dateStr}`,
                    operator: "system",
                    projectType: "debug",
                    strategy: "cases",
                    groupName: "",
                    notifyUrl:
                        "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
                    browser: "chrome:headless --allow-insecure-localhost"
                },
                {
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            );
        } catch (error) {
            console.error("Error running scheduled task:", error);
            /* empty */
        }
        try {
            await this.http.axiosRef.post(
                `${process.env.API_SERVER_URL}/cases/runTestGroup`,
                {
                    cases: [59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74],
                    runId: `${Date.now()}_3`,
                    testSuiteName: `test_签署PC${dateStr}`,
                    operator: "system",
                    projectType: "debug",
                    strategy: "cases",
                    groupName: "",
                    notifyUrl:
                        "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
                    browser: "chrome:headless --allow-insecure-localhost"
                },
                {
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            );
        } catch (error) {
            console.error("Error running scheduled task:", error);
            /* empty */
        }
        try {
            await this.http.axiosRef.post(
                `${process.env.API_SERVER_URL}/cases/runTestGroup`,
                {
                    cases: [76, 77, 78, 79, 80, 147, 148, 149, 150, 145, 146, 137, 152, 163, 139, 81],
                    runId: `${Date.now()}_4`,
                    testSuiteName: `test_SaaS基础中台${dateStr}`,
                    operator: "system",
                    projectType: "debug",
                    strategy: "cases",
                    groupName: "",
                    notifyUrl:
                        "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
                    browser: "chrome:headless --allow-insecure-localhost"
                },
                {
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            );
        } catch (error) {
            console.error("Error running scheduled task:", error);
            /* empty */
        }
        try {
            await this.http.axiosRef.post(
                `${process.env.API_SERVER_URL}/cases/runTestGroup`,
                {
                    cases: [92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107],
                    runId: `${Date.now()}_5`,
                    testSuiteName: `test_SaaS发起${dateStr}`,
                    operator: "system",
                    projectType: "debug",
                    strategy: "cases",
                    groupName: "",
                    notifyUrl:
                        "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
                    browser: "chrome:headless --allow-insecure-localhost"
                },
                {
                    headers: {
                        "Content-Type": "application/json"
                    }
                }
            );
        } catch (error) {
            console.error("Error running scheduled task:", error);
            /* empty */
        }
        // try {
        //     await this.http.axiosRef.post(
        //         `${process.env.API_SERVER_URL}/cases/runTestGroup`,
        //         {
        //             cases: [112, 114, 118, 120, 123, 124, 141, 154, 155, 157, 158, 159, 160],
        //             runId: `${Date.now()}_4`,
        //             testSuiteName: `test_ePaaS文件模板${dateStr}`,
        //             operator: "system",
        //             projectType: "debug",
        //             strategy: "cases",
        //             groupName: "",
        //             notifyUrl:
        //                 "https://oapi.dingtalk.com/robot/send?access_token=f0e1d23394f976262e2aee20c31be1c71535c35c597d6601a613b459cf15e5ed",
        //             browser: "chrome:headless --allow-insecure-localhost"
        //         },
        //         {
        //             headers: {
        //                 "Content-Type": "application/json"
        //             }
        //         }
        //     );
        // } catch (error) {
        //     console.error("Error running scheduled task:", error);
        //     /* empty */
        // }
    }
}
