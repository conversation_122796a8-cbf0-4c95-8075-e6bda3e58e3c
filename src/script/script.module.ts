import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Script } from './script.entity';
import { ScriptHistory } from './script-history.entity';
import { Case } from '../case/case.entity';
import { <PERSON>riptController } from './script.controller';
import { ScriptHistoryController } from './script-history.controller';
import { ScriptService } from './script.service';
import { ScriptHistoryService } from './script-history.service';

@Module({
  imports: [TypeOrmModule.forFeature([Script, ScriptHistory, Case])],
  controllers: [ScriptController, ScriptHistoryController],
  providers: [ScriptService, ScriptHistoryService],
  exports: [ScriptService, ScriptHistoryService],
})
export class ScriptModule {}