import { Controller, Get, Post, Body, Put, Delete, Param, Query } from '@nestjs/common';
import { ScriptService } from './script.service';
import { Script } from './script.entity';
import { CreateScriptDto } from './dto/create-script.dto';

@Controller('scripts')
export class ScriptController {
  constructor(private readonly scriptService: ScriptService) { }

  @Get('list')
  async findAll(@Query() query: any) {
    return this.scriptService.paginate(query);
  }

  @Get('search')
  async searchByKeyword(
    @Query('keyword') keyword?: string,
    @Query('editor') editor?: string,
    @Query('app') app?: string
  ) {
    return this.scriptService.searchByKeyword(keyword, editor, app);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const scriptId = parseInt(id, 10);
    if (isNaN(scriptId)) {
      return { code: 400, message: '无效的脚本ID，ID必须是一个数字' };
    }
    const script = await this.scriptService.findOne(scriptId);
    if (!script) {
      return { code: 404, message: '脚本不存在' };
    }
    return { code: 200, data: script };
  }

  @Post('create')
  async create(@Body() scriptsData: any[]) {
    if (!Array.isArray(scriptsData)) {
      return { code: 400, message: 'Invalid input data' };
    } 
    const result = await this.scriptService.create(scriptsData);
    return result;
  }
  @Post('createData')
  async createData(@Body() data: {scriptName: string, content: string, app: string, description: string, remark: string}) {
    try {
      const contentArray = JSON.parse(data.content);
      if (!Array.isArray(contentArray)) {
        throw new Error('Content must be a JSON array');
      }
      return this.scriptService.createByStep([{
        scriptName: data.scriptName,
        content: data.content,
        app: data.app,
        description: data.description,
        remark: data.remark
      }]);
    } catch (e) {
      return { code: 400, message: e.message };
    }
  }
  @Delete(':id')
  async remove(@Param('id') id: number) {
    return this.scriptService.remove(id);
  }

  @Post(':id')
  async update(@Param('id') id: number, @Body() updateScriptDto: any) {
    return this.scriptService.update(id, updateScriptDto);
  }

  @Get('count')
  async countByPeriod(@Query('start') start: number, @Query('end') end: number) {
    if (!start || !end) {
      return { code: 400, message: '需要提供start和end时间戳参数' };
    }
    const result = await this.scriptService.countByPeriod(start, end);
    return { 
      code: 200, 
      data: result,
      message: '查询成功'
    };
  }
}