import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Code, Repository } from 'typeorm';
import { Script } from './script.entity';
import { Case } from '../case/case.entity';
import { In } from 'typeorm';
import { Like } from 'typeorm';
// 在导入部分添加
import { ScriptHistoryService } from './script-history.service';

@Injectable()
export class ScriptService {
  // 在构造函数中注入
  constructor(
    @InjectRepository(Script)
    private readonly scriptRepository: Repository<Script>,
    @InjectRepository(Case)
    private readonly caseRepository: Repository<Case>,
    private readonly scriptHistoryService: ScriptHistoryService, // 新增
  ) { }

  async paginate(query: any) {
    const { page = 1, limit = 10, ...filters } = query;
    const where = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value) {
        if (key === 'keyword') {
          acc['scriptName'] = Like(`%${value}%`);
        } else if (['app', 'scriptName', 'editor'].includes(key)) {
          acc[key] = value;
        }
      }
      return acc;
    }, {});

    const [data, total] = await this.scriptRepository.findAndCount({
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: {
        id: 'DESC'
      }
    });

    // 为每个脚本查询使用它的用例
    const dataWithCases = await Promise.all(
      data.map(async (script) => {
        const cases = await this.caseRepository
          .createQueryBuilder('case')
          .innerJoin('case.scripts', 'script')
          .where('script.id = :scriptId', { scriptId: script.id })
          .select(['case.caseName'])
          .getMany();
        
        return {
          ...script,
          usedInCases: cases.map(c => c.caseName)
        };
      })
    );

    return {
      data: dataWithCases,
      total,
      page: Number(page)
    };
  }

  // 修改 create 方法
  async create(createScriptDto: any) {
    function processSteps(inputArray) {
      // 收集所有分割点
      let splits = [] as any[];
      inputArray.forEach((item, index) => {
        if (item.type === 'split') {
          splits.push({
            index: index,
            name: item.value,
            description: item.desc || '',
            app: item.app
          });
        }
      });
      console.log(splits);
      const steps = [] as any[];
      for (let i = 0; i < splits.length; i++) {
        const currentSplit = splits[i];
        const nextSplitIndex = i < splits.length - 1 ? splits[i + 1].index : inputArray.length;
        const start = currentSplit.index + 1;
        const end = nextSplitIndex;
        const events = inputArray.slice(start, end);

        steps.push({
          scriptName: currentSplit.name,
          description: currentSplit.description,
          app: currentSplit.app,
          content: events ? JSON.stringify(events) : null,
          recordTime: new Date(),
        });
      }
      console.log(steps);
      return steps;
    }
    const scripts = processSteps(createScriptDto);

    for (const script of scripts) {
      if (script.content) {
        const savedScript = await this.scriptRepository.save(script);
        
        // 记录创建历史
        await this.scriptHistoryService.recordScriptChange(
          savedScript.id,
          savedScript.content,
          savedScript.scriptName,
          savedScript.app,
          savedScript.tag,
          savedScript.remark,
          savedScript.editor || 'system',
          'create',
          `创建脚本: ${savedScript.scriptName}`
        );
      }
    }



    return {
      code: 0,
      message: '脚本创建成功',
      data: scripts
    }
  }
  // 修改 createByStep 方法
  async createByStep(createScriptDto: any) {
    if(createScriptDto){
      const data = await this.scriptRepository.save(createScriptDto);
      const res = data[0]
      // 记录创建历史
      await this.scriptHistoryService.recordScriptChange(
        res.id,
        res.content,
        res.scriptName,
        res.app,
        res.tag,
        res.remark,
        res.editor || 'system',
        'create',
        `创建脚本: ${res.scriptName}`
      );
      
      return {
        code: 0,
        message: '脚本创建成功',
        data: res
      }
    }
  }
  async searchByKeyword(keyword?: string, editor?: string, app?: string): Promise<Script[]> {
    const where: any = {};
    
    if (keyword) {
      where.scriptName = Like(`%${keyword}%`);
    }
    
    if (editor) {
      where.editor = Like(`%${editor}%`);
    }
    
    if (app) {
      where.app = Like(`%${app}%`);
    }
    
    return this.scriptRepository.find({
      where,
      take: 1000,
      order: { recordTime: 'DESC' }
    });
  }
  // 修改 update 方法
  async update(id: number, updateScriptDto: any) {
    const oldScript = await this.scriptRepository.findOne({ where: { id } });
    await this.scriptRepository.update(id, updateScriptDto);
    const updatedScript = await this.scriptRepository.findOne({ where: { id } });
    
    // 记录更新历史
    if (updatedScript) {
      await this.scriptHistoryService.recordScriptChange(
        updatedScript.id,
        updatedScript.content,
        updatedScript.scriptName,
        updatedScript.app|| '',
        updatedScript.tag|| '',
        updatedScript.remark|| '',
        updatedScript.editor || 'system',
        'update',
        `更新脚本: ${updatedScript.scriptName}`
      );
    }
    
    return updatedScript;
  }

  async remove(id: number) {
    // 检查脚本是否被case表引用
    const isReferenced = await this.scriptRepository
      .createQueryBuilder('script')
      .innerJoin('case_scripts_monitor_uitest_script', 'case_script', 'case_script.monitorUitestScriptId = script.id')
      .where('script.id = :id', { id })
      .getCount();

    if (isReferenced > 0) {
      return {
        code: 400,
        message: `无法删除脚本ID ${id}，因为该脚本已被用例引用`
      };
    } else {
      // 先删除脚本历史记录，避免外键约束错误
      await this.scriptHistoryService.deleteByScriptId(id);
      
      // 然后删除脚本本身
      await this.scriptRepository.delete(id);
      return { success: true };
    }
  }

  async findByIds(ids: number[]): Promise<Script[]> {
    return this.scriptRepository.find({
      where: { id: In(ids) }
    });
  }

  async findOne(id: number): Promise<any> {
    return this.scriptRepository.findOne({
      where: { id }
    });
  }

  async countByPeriod(start: number, end: number) {
    const currentCount = await this.scriptRepository
      .createQueryBuilder('script')
      .where('script.recordTime BETWEEN :start AND :end', { start:new Date(start), end: new Date(end) })
      .getCount();

    const previousStart = new Date(start - 604800000);
    const previousEnd = new Date(end - 604800000);
    
    const previousCount = await this.scriptRepository
      .createQueryBuilder('script')
      .where('script.recordTime BETWEEN :previousStart AND :previousEnd', {
        previousStart,
        previousEnd
      })
      .getCount();

    return {
      currentPeriod: currentCount,
      previousPeriod: previousCount
    };
  }
}