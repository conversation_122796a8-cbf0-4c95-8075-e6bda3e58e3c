import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { ScriptHistory } from './script-history.entity';
import { CreateScriptHistoryDto, QueryScriptHistoryDto } from './dto/script-history.dto';

@Injectable()
export class ScriptHistoryService {
    constructor(
        @InjectRepository(ScriptHistory)
        private readonly scriptHistoryRepository: Repository<ScriptHistory>,
    ) { }

    async create(createDto: CreateScriptHistoryDto): Promise<ScriptHistory> {
        const history = this.scriptHistoryRepository.create(createDto);
        return await this.scriptHistoryRepository.save(history);
    }

    async findByScriptId(scriptId: number): Promise<ScriptHistory[]> {
        return await this.scriptHistoryRepository.find({
            where: { scriptId },
            order: { changeTime: 'DESC' },
            relations: ['script']
        });
    }

    async paginate(query: QueryScriptHistoryDto) {
        const { page = 1, limit = 10, ...filters } = query;
        const where = Object.entries(filters).reduce((acc, [key, value]) => {
            if (value) {
                if (['scriptId', 'changeType', 'editor'].includes(key)) {
                    acc[key] = value;
                }
            }
            return acc;
        }, {});

        const [data, total] = await this.scriptHistoryRepository.findAndCount({
            where,
            skip: (page - 1) * limit,
            take: limit,
            order: { changeTime: 'DESC' },
            relations: ['script']
        });

        return {
            data,
            total,
            page: Number(page)
        };
    }

    async recordScriptChange(
        scriptId: number,
        content: string,
        scriptName: string,
        app: string,
        tag: string,
        remark: string,
        editor: string,
        changeType: 'create' | 'update',
        changeDescription?: string
    ): Promise<ScriptHistory> {
        const historyDto: CreateScriptHistoryDto = {
            scriptName,
            app,
            tag,
            remark,
            scriptId,
            content,
            editor,
            changeType,
            changeDescription
        };
        return await this.create(historyDto);
    }

    async deleteByScriptId(scriptId: number): Promise<void> {
        await this.scriptHistoryRepository.delete({ scriptId });
    }
}