import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';


@Entity('monitor_uitest_script')
export class Script {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', comment: '脚本内容' })
  content: string;

  @CreateDateColumn({ comment: '录制时间' })
  recordTime: Date;

  @Column({ type: 'text', comment: '脚本描述', nullable: true })
  description?: string;

  @Column({ type: 'text', comment: '脚本标签', nullable: true })
  tag?: string;

  @Column({ type: 'text', comment: '脚本备注', nullable: true })
  remark?: string;
  
  @Column({ type: 'text', comment: '脚本名称', nullable: true })
  scriptName: string;
  
  @Column({ type: 'text', comment: '所属应用', nullable: true })
  app?: string;

  @Column({ type: 'text', comment: '编辑人', nullable: true })
  editor?: string;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
}