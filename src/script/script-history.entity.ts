import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Script } from './script.entity';

@Entity('monitor_uitest_script_history')
export class ScriptHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '脚本ID' })
  scriptId: number;

  @Column({ type: 'text', comment: '变更后的脚本内容' })
  content: string;

  @Column({ type: 'text', comment: '脚本描述', nullable: true })
  description?: string;

  @Column({ type: 'text', comment: '脚本标签', nullable: true })
  tag?: string;

  @Column({ type: 'text', comment: '脚本备注', nullable: true })
  remark?: string;
  
  @Column({ type: 'text', comment: '脚本名称', nullable: true })
  scriptName: string;
  
  @Column({ type: 'text', comment: '所属应用', nullable: true })
  app?: string;

  @Column({ type: 'datetime', comment: '原脚本录制时间', nullable: true })
  recordTime?: Date;

  @Column({ type: 'varchar', length: 64, comment: '变更人', nullable: true })
  editor?: string;

  @Column({ type: 'varchar', length: 20, comment: '变更类型：create/update' })
  changeType: string;

  @Column({ type: 'text', comment: '变更描述', nullable: true })
  changeDescription?: string;

  @CreateDateColumn({ comment: '变更时间' })
  changeTime: Date;

  @ManyToOne(() => Script)
  @JoinColumn({ name: 'scriptId' })
  script: Script;
}