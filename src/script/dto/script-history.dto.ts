export class CreateScriptHistoryDto {
  scriptId: number;
  content: string;
  description?: string;
  tag?: string;
  remark?: string;
  scriptName: string;
  app?: string;
  recordTime?: Date;
  editor?: string;
  changeType: string;
  changeDescription?: string;
}

export class QueryScriptHistoryDto {
  scriptId?: number;
  editor?: string;
  changeType?: string;
  scriptName?: string;
  app?: string;
  page?: number;
  limit?: number;
}