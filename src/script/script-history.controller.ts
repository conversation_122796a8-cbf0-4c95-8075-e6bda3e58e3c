import { Controller, Get, Query, Param } from '@nestjs/common';
import { ScriptHistoryService } from './script-history.service';
import { QueryScriptHistoryDto } from './dto/script-history.dto';

@Controller('scripthistory')
export class ScriptHistoryController {
  constructor(private readonly scriptHistoryService: ScriptHistoryService) {}

  @Get('list')
  async paginate(@Query() query: QueryScriptHistoryDto) {
    return await this.scriptHistoryService.paginate(query);
  }

  @Get('scripthistory/:id')
  async getByScriptId(@Param('id') id: number) {
    return await this.scriptHistoryService.findByScriptId(id);
  }
}