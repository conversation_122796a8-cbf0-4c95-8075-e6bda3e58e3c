// src/test-record/test-record.controller.ts
import { Controller, Get, Post, Body, Query, Param, Delete, Patch, Res, BadRequestException, Req } from '@nestjs/common';
import { TestRecordService } from './record.service';
import { CreateTestRecordDto } from './dto/create-record.dto';
import { Response, Request } from 'express';

@Controller('records')
export class TestRecordController {
  constructor(private readonly service: TestRecordService) { }
  @Post('log')
  log(@Body() data: any) {
    return this.service.log(data.id, data.runInfo);
  }

  @Post()
  create(@Body() dto: CreateTestRecordDto) {
    return this.service.create(dto);
  }
  @Get('list')
  async findList(@Query() query: any) {
    return this.service.paginate(query);
  }
  @Get('groupByRunId')
  async groupByRunId(@Query() query: any, @Query('testSuiteName') testSuiteName?: string) {
    return this.service.groupByRunId(query);
  }
  @Get('getLogs')
  async getReports(@Query('fileTag') fileTag: string) {
    console.log('fileTag', fileTag)
    return await this.service.getLogs( fileTag )
  }
  
  @Get('getJsonFiles')
  async getJsonFiles(@Query('fileTag') fileTag: string) {
    return await this.service.getJsonFiles(fileTag);
  }
  @Get('queryByRunId')
  async queryByRunId(@Query('runId') runId: string) {
    return this.service.findByRunId(runId);
  }
  @Get('getDataByTime')
  findByTimeRange(
    @Query('start') start: number,
    @Query('end') end: number,
    @Query('app') app?: string,
    @Query('product') product?: string,
    @Query('groupName') groupName?: string,
  ) {
    return this.service.findByTimeRange(start, end, app, product,groupName);
  }
  @Get('/findByCaseName/:caseName')
  findResultByCaseName(@Param('caseName') caseName: string) {
    return this.service.findByCaseName(caseName);
  }
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const recordId = parseInt(id, 10);
    if (isNaN(recordId)) {
      throw new BadRequestException('Invalid record ID. ID must be a number.');
    }
    return this.service.findOne(recordId);
  }
  @Get()
  findAll() {
    return this.service.findAll();
  }
  @Get('/video/:fileName')
  async getMp4(@Param('fileName') fileName: string, @Req() req: Request, @Res() res: Response) {
    const range = req.headers.range;
    
    // 添加调试日志
    console.log('Video request debug info:', {
      fileName,
      range: req.headers.range,
      userAgent: req.headers['user-agent'],
      host: req.headers.host,
      xForwardedFor: req.headers['x-forwarded-for'],
      xRealIp: req.headers['x-real-ip'],
      allHeaders: Object.keys(req.headers)
    });
    
    const result = await this.service.getMp4(fileName, range);
    
    if (typeof result === 'string') {
      console.log('Video error:', result);
      res.status(404).send(result);
      return;
    }
    
    const { stream, headers, statusCode } = result;
    
    // 记录响应信息
    console.log('Video response info:', {
      statusCode,
      headers,
      hasRange: !!range
    });
    
    // 设置响应头
    Object.keys(headers).forEach(key => {
      res.setHeader(key, headers[key]);
    });
    
    res.status(statusCode);
    stream.pipe(res);
  }
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const recordId = parseInt(id, 10);
    if (isNaN(recordId)) {
      throw new BadRequestException('Invalid record ID. ID must be a number.');
    }
    return this.service.remove(recordId);
  }


  @Post('bulkUpdate')
  async updateMultipleFields(@Body() body: { ids: string[], data: any }) {
    if (!body.ids || !Array.isArray(body.ids)) {
      throw new BadRequestException('ids must be a non-empty array');
    }
  
    const invalidIds: string[] = [];
    const recordIds: number[] = [];
  
    for (const id of body.ids) {
      const parsedId = parseInt(id, 10);
      if (isNaN(parsedId)) {
        invalidIds.push(id);
      } else {
        recordIds.push(parsedId);
      }
    }
  
    if (recordIds.length === 0) {
      throw new BadRequestException('No valid record IDs provided');
    }
  
    const result = await this.service.updateMultipleFields(recordIds, body.data);
  
    return {
      ...result,
      invalidIds: invalidIds.length > 0 ? invalidIds : undefined
    };
  }

  

  @Post(':id')
  async updateFields(@Param('id') id: string, @Body() data: any) {
    const recordId = parseInt(id, 10);
    if (isNaN(recordId)) {
      throw new BadRequestException('Invalid record ID. ID must be a number.');
    }
    return this.service.updateFields(recordId, data);
  }

}
