import { Column, Entity, PrimaryGeneratedColumn, Index } from 'typeorm';
// 告警类型名称 recordName 告警内容recordInfo  告警环境 recordEnv 告警时间createAt 告警处理状态isActive
@Entity('monitor_uitest_record')
@Index('IDX_START_TIME', ['startTime'])
@Index('IDX_CASE_NAME', ['caseName'])
export class TestRecord {
  @PrimaryGeneratedColumn()
  id: number;
  // 基础信息
  @Column({ comment: '用例名称' })
  caseId: number;
  @Column({ comment: '用例名称' })
  caseName: string;
  @Column({ comment: '开始执行时间' })
  startTime: Date;
  @Column({ comment: '执行时间', nullable: true })
  endTime?: Date;
  @Column({ comment: '运行时长(秒)',default: 0 })
  duration: number;
  @Column({ comment: '执行状态' })
  status: string;
  @Column({ comment: '执行结果', default: ''})
  runResult: string;
  @Column({type:'text', nullable: true, comment: '错误信息' })
  testInfo?: string;
  @Column({type:'text', nullable: true, comment: '执行信息' })
  runInfo?: string;
  @Column({type:'text', nullable: true, comment: '异常标记' })
  reason?: string;
  // 环境信息
  @Column({ comment: '浏览器' })
  browser: string;

  @Column({ comment: '操作系统' })
  userAgent: string;
  @Column({ comment: '运行环境' })
  env: string;
  @Column({ nullable: true, comment: '执行人' })
  operator?: string;
  @Column({ nullable: true, comment: '通知群组' })
  notifyUrl?: string;
  @Column({ comment: '执行组ID' })
  runId?: string;
  @Column({ comment: '执行测试集',default: ''  })
  testSuiteName?: string;
  @Column({ nullable: true, comment: '报告标记' })
  fileTag: string;
  @Column({ nullable: true, comment: '项目名称' })
  groupName: string;
  @Column({ nullable: true, comment: '业务域' })
  app: string;
  @Column({ nullable: true, comment: '产品线' })
  product: string;
  @Column({ nullable: true, comment: '备注' })
  remark: string;
  @Column({ nullable: true, comment: '项目类型' })
  projectType: string;
  @Column({ nullable: true, comment: '执行类型' })
  runType: string;
}
