// src/test-record/test-record.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { TestRecord } from './record.entity';
import { CreateTestRecordDto } from './dto/create-record.dto';
import * as fs from 'fs';
import * as path from 'path';
@Injectable()
export class TestRecordService {
  constructor(
    @InjectRepository(TestRecord)
    private readonly testRecordRepo: Repository<TestRecord>,
  ) { }

  async create(dto: CreateTestRecordDto) {
    return this.testRecordRepo.save(dto);
  }

  async findAll(): Promise<TestRecord[]> {
    return this.testRecordRepo.find();
  }

  async findOne(id: number) {
    return this.testRecordRepo.findOneBy({ id });
  }


  async remove(id: number): Promise<void> {
    await this.testRecordRepo.delete(id);
  }

  // 按用例名称搜索
  async findByCaseName(name: string): Promise<TestRecord[]> {
    return this.testRecordRepo.find({ where: { caseName: Like(`%${name}%`) } });
  }

  // 按时间范围查询
  async findByTimeRange(start: number, end: number, app?: string, product?: string, groupName?: string): Promise<TestRecord[]> {
    const startDate = new Date(Number(start));
    const endDate = new Date(Number(end));
    console.log('查询时间范围:', startDate, endDate);
    const queryBuilder = this.testRecordRepo.createQueryBuilder('record')
      .where('record.startTime BETWEEN :start AND :end', { start: startDate, end: endDate });

    if (app) {
      queryBuilder.andWhere('record.app = :app', { app });
    }
    if (product) {
      queryBuilder.andWhere('record.product = :product', { product });
    }
    if (groupName) {
      queryBuilder.andWhere('record.groupName LIKE :groupName', { groupName: `%${groupName}%` });
    }
    const result = await queryBuilder.getMany();
    console.log('查询结果数量:', result.length);
    return result;
  }
  async getLogs(fileTag: string) {
    const reportPath = path.join(__dirname, '../../reports/logs', `${fileTag}.log`);
    console.log('reportPath:', reportPath);
    if (fs.existsSync(reportPath)) {
      return await fs.readFileSync(reportPath, 'utf8');
    } else {
      return `Report for ${fileTag} not found.`;
    }
  }
  async paginate(query: any): Promise<any> {
    const { page = 1, limit = 10,env, caseName, operator, app, product, groupName, status, runResult, startTime, endTime, reason, projectType, ...where } = query;
    const queryBuilder = this.testRecordRepo.createQueryBuilder('record');

    if (caseName) {
      queryBuilder.andWhere('record.caseName LIKE :caseName', { caseName: `%${caseName}%` });
    }
    if (operator) {
      queryBuilder.andWhere('record.operator LIKE :operator', { operator: `%${operator}%` });
    } else {
      queryBuilder.andWhere('record.operator != :system', { system: 'system' });
    }
    if (app) {
      queryBuilder.andWhere('record.app = :app', { app });
    }
    if (env) {
      queryBuilder.andWhere('record.env = :env', { env });
    }
    if (product) {
      queryBuilder.andWhere('record.product = :product', { product });
    }
    if (groupName) {
      queryBuilder.andWhere('record.groupName LIKE :groupName', { groupName: `%${groupName}%` });
      // queryBuilder.andWhere('record.groupName = :groupName', { groupName });
    }
    if (status) {
      queryBuilder.andWhere('record.status = :status', { status });
    }
    if (runResult) {
      queryBuilder.andWhere("record.runResult = :runResult", { runResult: runResult});
    }
    if (startTime && endTime) {
      const startDate = new Date(Number(startTime));
      const endDate = new Date(Number(endTime));
      queryBuilder.andWhere('record.startTime BETWEEN :start AND :end', { start: startDate, end: endDate });
    }
    if (reason) {
      if(reason==='unmarked'){
        queryBuilder.andWhere('record.runResult = :runResult', { runResult: 'fail' });
        queryBuilder.andWhere('record.reason IS NULL');
      } else {
        queryBuilder.andWhere('record.reason = :reason', { reason });
      }
    }
    if (projectType) {
      queryBuilder.andWhere('record.projectType = :projectType', { projectType });
    }
    const [data, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('record.id', 'DESC')
      .getManyAndCount();
    return { data, total };
  }

  async log(id: number, logData: any) {
    if (!logData) {
      throw new Error('Log data is required');
    }

    const record = await this.testRecordRepo.findOneBy({ id });
    if (!record) {
      throw new Error('Record not found');
    }

    const logText = JSON.stringify(logData);
    record.runInfo = logText;

    await this.testRecordRepo.save(record);

    return { success: true, message: 'Log updated successfully' };
  }
  async findByRunIdAndStatus(runId: string, runResult: string, page: number = 1, limit: number = 100): Promise<{ data: TestRecord[]; total: number }> {
    if (!runId || !runResult) {
      throw new Error('runId和runResult不能为空');
    }

    const queryBuilder = this.testRecordRepo.createQueryBuilder('record')
      .where('record.runId = :runId', { runId })
      .andWhere('record.runResult = :runResult', { runResult })
      .orderBy('record.id', 'DESC');

    const total = await queryBuilder.getCount();
    const data = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();
      console.log('查询到运行ID %s 状态 %s 的记录共 %s 条', runId, runResult, total);
    return { data, total };
  }

  async findByRunId(runId: string): Promise<TestRecord[]> {
    return this.testRecordRepo.find({ 
      where: { runId },
      order: { id: 'DESC' }
    });
  }
  async findByTestSuite(testSuiteName: string): Promise<TestRecord[]> {
    return this.testRecordRepo.find({ where: { testSuiteName } });
  }
  async groupByRunId(query: any): Promise<any> {
    const { page = 1, limit = 10, operator, runId, projectType, product, startDate, endDate,env } = query;

    const filteredRunId = runId?.trim() || null;
    const filteredOperator = operator?.trim() || null;
    const filteredTestSuite = query.testSuiteName?.trim() || null;
    const filteredProjectType = projectType?.trim() || null;
    const filteredProduct = product?.trim() || null;
    const filteredEnv = env?.trim() || null;

    // 分页数据查询
    const dataQuery = this.testRecordRepo.createQueryBuilder('record')
      .select([
        'record.runId AS runId',
        'MIN(record.startTime) AS startTime',
        'MAX(record.operator) AS operator',
        'MAX(record.testSuiteName) AS testSuiteName',
        'MAX(record.projectType) AS projectType',
        'MAX(record.env) AS env',
        'COUNT(record.id) AS caseCount',
        'COUNT(CASE WHEN record.status = \'success\' THEN 1 END) AS finishCount',
        `COUNT(CASE WHEN latest.runResult = 'success' THEN 1 END) AS successCount`,
        `COUNT(CASE WHEN latest.runResult = 'fail' THEN 1 END) AS failedCount`,
      ])
      .leftJoin(
        subQuery => {
          return subQuery
            .select('r1.id', 'id')
            .from('monitor_uitest_record', 'r1')
            .leftJoin('monitor_uitest_record', 'r2', 'r1.caseName = r2.caseName AND r1.runId = r2.runId AND r1.id < r2.id')
            .where('r2.id IS NULL');
        },
        'latest_records',
        'record.id = latest_records.id'
      )
      .leftJoin('monitor_uitest_record', 'latest', 'latest.id = latest_records.id')
      .andWhere('record.runId IS NOT NULL')
      .andWhere("record.runId != ''")
      .groupBy('record.runId')
      .orderBy('startTime', 'DESC');

    // 总数统计查询
    const countQuery = this.testRecordRepo.createQueryBuilder('record')
      .select('COUNT(DISTINCT record.runId)', 'total')
      .andWhere('record.runId IS NOT NULL')
      .andWhere("record.runId != ''");

    // 应用过滤条件
    [dataQuery, countQuery].forEach(query => {
      if (filteredTestSuite) {
        query.andWhere('record.testSuiteName LIKE :testSuite', { testSuite: `%${filteredTestSuite}%` });
      }
      if (filteredOperator) {
        query.andWhere('record.operator LIKE :operator', { operator: `%${filteredOperator}%` });
      }
      if (filteredRunId) {
        query.andWhere('record.runId LIKE :runId', { runId: `%${filteredRunId}%` });
      }
      if (filteredProjectType) {
        query.andWhere('record.projectType = :projectType', { projectType: filteredProjectType });
      }
      if (filteredProduct) {
        query.andWhere('record.product = :product', { product: filteredProduct });
      }
      if (filteredEnv) {
        query.andWhere('record.env = :env', { env: filteredEnv });
      }
      if (startDate && endDate) {
        console.log('startDate:', startDate, 'endDate:', endDate);
        const start = new Date(startDate);
        start.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        query.andWhere('record.startTime BETWEEN :start AND :end ', { start, end });
      }
    });

    const data = await dataQuery
      .offset((page - 1) * limit)
      .limit(limit)
      .getRawMany();

    const totalResult = await countQuery.getRawOne();

    return {
      data,
      total: Number(totalResult.total),
      page: Number(page)
    };
  }

  async getJsonFiles(fileTag): Promise<any> {
    try {
       const jsonDir = path.join(__dirname, '../../reports/json', `${fileTag}.json`);
         if (fs.existsSync(jsonDir)) {
          return await fs.readFileSync(jsonDir, 'utf8');
        } else {
          return `Report for ${fileTag} not found.`;
        }
    } catch (error) {
      return { error: `读取json目录失败: ${error.message}` };
    }
  }

  async getMp4(fileName: string, range?: string): Promise<{ stream: fs.ReadStream; headers: any; statusCode: number } | string> {
    const videoPath = path.join(__dirname, '../../reports/videos', fileName);
    
    console.log('getMp4 service debug:', {
      fileName,
      videoPath,
      range,
      fileExists: fs.existsSync(videoPath)
    });
    
    if (!fs.existsSync(videoPath)) {
      return `Video ${fileName} not found.`;
    }
    
    const stat = fs.statSync(videoPath);
    const fileSize = stat.size;
    
    console.log('File info:', { fileSize, hasRange: !!range });
    
    if (range) {
      // 解析Range请求头
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      
      console.log('Range parsing:', {
        originalRange: range,
        parts,
        start,
        end,
        fileSize
      });
      
      if (start >= fileSize) {
        console.log('Range not satisfiable:', { start, fileSize });
        return `Range not satisfiable`;
      }
      
      const chunksize = (end - start) + 1;
      const stream = fs.createReadStream(videoPath, { start, end });
      
      const headers = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'video/mp4',
      };
      
      console.log('Returning 206 with headers:', headers);
      return { stream, headers, statusCode: 206 }; // 206 Partial Content
    } else {
      // 完整文件请求
      const stream = fs.createReadStream(videoPath);
      
      const headers = {
        'Content-Length': fileSize,
        'Content-Type': 'video/mp4',
        'Accept-Ranges': 'bytes',
      };
      
      console.log('Returning 200 with headers:', headers);
      return { stream, headers, statusCode: 200 };
    }
  }

  async updateFields(id: number, data: any) {
    const record = await this.testRecordRepo.findOneBy({ id });
    if (!record) {
      throw new Error('Record not found');
    }

    Object.assign(record, data);
    ;

    return await this.testRecordRepo.save(record)
  }
  async updateMultipleFields(ids: number[], data: any) {
    await this.testRecordRepo.update({ id: In(ids) }, data);
    return {
      message: `成功更新 ${ids.length} 条记录`,
      updatedCount: ids.length
    };
  }
}
