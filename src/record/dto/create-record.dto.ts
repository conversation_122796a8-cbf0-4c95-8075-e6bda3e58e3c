export class CreateTestRecordDto {
  caseName: string;
  caseId: number;
  startTime: Date;
  status: string;
  operator?: string;
  duration?: number;
  groupName?: string;
  notifyUrl?: string;
  testInfo?: string;
  browser?: string;
  userAgent?: string;
  env?: string;
  endTime?: Date;
  app?: string;
  product?: string;
  fileTag?: string;
  runId?: string;
  remark?: string;
  reason?: string;
  testSuiteName?: string;
  runInfo?: string;
  runType?: string;
  runResult?: string;
  projectType?: string;
}