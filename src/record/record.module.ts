// src/test-record/test-record.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRecord } from './record.entity';
import { TestRecordService } from './record.service';
import { TestRecordController } from './record.controller';

@Module({
  imports: [TypeOrmModule.forFeature([TestRecord])],
  controllers: [TestRecordController],
  providers: [TestRecordService],
  exports: [TestRecordService],
})
export class TestRecordModule {}
