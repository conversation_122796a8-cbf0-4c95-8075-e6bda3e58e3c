// 提升并导出VariableType接口
export interface VariableType {
  name: string;
  realName?: string;
  value: any;
  indexList?: number[];
  prefix?: {
    type: string;
    length: string;
    letters?: boolean;
  };
  suffix?: {
    type: string;
    length: string;
    letters?: boolean;
    number?: boolean;
  };
}

// 提升并导出TreeNode接口
export interface TreeNode {
  data: {
    text: any;
    note: string;
    imageSize: {};
  };
  children: TreeNode[];
}

/**
 * 获取测试用例的起始URL
 * 优先使用环境特定配置中的URL，其次使用默认URL
 * @returns {string} 解析后的起始URL
 */
function getStartUrl (env,config) {
  if (env && config?.envConfigs?.[env]?.url) {
    return config?.envConfigs?.[env].url
  }

  return config.url
}


// 处理变量转换
function genSingleVariable (v, original) {
  const variable: VariableType = {
      name: v.variableName,
      indexList: v.indexList || [],
      value: v.data.find(d => d.env === original.env)?.value || v.data.find(d => d.env === 'global')?.value,
      prefix: undefined,
      suffix: undefined
  };

  if (v.realName) variable.realName = v.realName;

  // 处理前缀规则
  if (v.prefix.type !== 'none') {
      variable.prefix = {
          type: v.prefix.type,
          length: String(v.prefix.length),
          ...(v.prefix.letters && { letters: true })
      };
  }
  // 处理后缀规则
  if (v.suffix.type !== 'none') {
      variable.suffix = {
          type: v.suffix.type,
          length: String(v.suffix.length),
          ...(v.suffix.letters ? { letters: true } : { number: true })
      };
  }
  return variable;
}

function replaceSingleScriptVariables (content, index, scopeVariables)  {
  let result = content
  // 筛选当前索引对应的作用域变量
  const variables = scopeVariables.filter(v => v.indexList?.includes(index))
  variables.forEach(v => {
    result = result.replaceAll('${' + v.realName + '}', '${' + v.name + '}')
  })
  
  return result
}

function convertToJSONGroup(original) {
  const id = original.caseName.replace(/\//g, '-') + '测试用例';
  
  // 处理基础配置
  const baseConfig = {
      url: getStartUrl(original.env,original.config),
      env: original.env || '',
      height: original.config.height,
      width: original.config.width,
      pageLoadTimeout: '',
      assertionTimeout: ''
  };

  // 处理Mock数据
  const mockConfig = {
      cookies: original.config.cookie.map(c => ({
          name: c.key,
          value: c.value,
          domain: c.domain,
          // path: "xxxx",
          // expires: "xxxxx"
      })),
      localStorage: original.config.localstorage,
      sessionStorage: original.config.session,
      request: original.config.request.map(req => {
          // 处理响应内容转义
          let response;
          try {
              response = JSON.parse(req.response);
              if (typeof response === 'string') {
                  try {
                      response = JSON.parse(response);
                  } catch (e) {
                      // 保持为字符串
                  }
              }
          } catch (e) {
              response = req.response;
          }

          return {
              path: req.path,
              type: req.path === "/v1/operational/guide-wecom" ? "regexp" : "include",
              method: req.method,
              response: typeof response === 'object' ? JSON.stringify(response) : response,
              ...(req.path === "/v1/operational/guide-wecom" && { merge: true })
          };
      })
  };

  const baseVariables: VariableType[] = original.config.variables.map((v) => genSingleVariable(v, original));

  const scopeVariables: any[] = []
  original?.variableFormData?.forEach(({ variableValue }, index) => {
    Object.entries(variableValue).forEach(([key, id]) => {
      const variable = original.config.variables.find(v => v.id === id && v.variableName === key)
      if (variable) {
        const newVariable = JSON.parse(JSON.stringify(variable))
        // 只加name和id是因为多个重复
        newVariable.realName = newVariable.variableName
        newVariable.variableName = `${newVariable.variableName}_${id}`
        newVariable.indexList = [index]
        const scopeVariablesIndex = scopeVariables.findIndex(v => v.name === newVariable.variableName)

        if (scopeVariablesIndex === -1) {
          scopeVariables.push(genSingleVariable(newVariable, original))
        } else {
          scopeVariables[scopeVariablesIndex].indexList.push(index)
        }
      }
    })
  })

    const variables = [...baseVariables, ...scopeVariables]

    // 处理测试用例步骤
    const xmindCases = [{
      title: original.caseName,
      steps: original.scripts.flatMap((script, index) => {
        return {
          title: script.scriptName,
          data: JSON.parse(replaceSingleScriptVariables(script.content, index, scopeVariables))
        }
      }
      )
  }];
  // 处理测试用例步骤
  const testCases = [{
    title: original.caseName,
    steps: original.scripts.flatMap((script, index) => 
        JSON.parse(replaceSingleScriptVariables(script.content, index, scopeVariables)).map(step => ({
            ...step
        }))
    )
  }];

  // 组合最终对象
  return {
      id,
      injectId: `${id}-${new Date().getTime()}`,
      app: original.app,
      caseId: original.id,
      product: original.product,
      config: {
          name: id,
          base: baseConfig,
          mock: mockConfig,
          variable: variables,
          preRequest: getStartUrl(original.env,original.config)
      },
      selector: {},
      cases: testCases,
      xmindCases: xmindCases
  };
};
function convertToTargetJSON(original) {
  const id = original.caseName.replace(/\//g, '-') + '测试用例';
  
  // 处理基础配置
  const baseConfig = {
      url: getStartUrl(original.env,original.config),
      env: original.env || '',
      height: original.config.height,
      width: original.config.width,
      pageLoadTimeout: '',
      assertionTimeout: ''
  };

  // 处理Mock数据
  const mockConfig = {
      cookies: original.config.cookie.map(c => ({
          name: c.key,
          value: c.value,
          domain: c.domain,
          // path: "xxxx",
          // expires: "xxxxx"
      })),
      localStorage: original.config.localstorage,
      sessionStorage: original.config.session,
      request: original.config.request.map(req => {
          // 处理响应内容转义
          let response;
          try {
              response = JSON.parse(req.response);
              if (typeof response === 'string') {
                  try {
                      response = JSON.parse(response);
                  } catch (e) {
                      // 保持为字符串
                  }
              }
          } catch (e) {
              response = req.response;
          }

          return {
              path: req.path,
              type: req.path === "/v1/operational/guide-wecom" ? "regexp" : "include",
              method: req.method,
              response: typeof response === 'object' ? JSON.stringify(response) : response,
              ...(req.path === "/v1/operational/guide-wecom" && { merge: true })
          };
      })
  };



// 处理变量转换
const baseVariables: VariableType[] = original.config?.variables?.map(v => genSingleVariable(v, original));

const scopeVariables: any[] = []
original?.variableFormData?.forEach(({ variableValue }, index) => {
  Object.entries(variableValue).forEach(([key, id]) => {
    const variable = original.config.variables.find(v => v.id === id && v.variableName === key)
    if (variable) {
      const newVariable = JSON.parse(JSON.stringify(variable))
      // 只加name和id是因为多个重复
      newVariable.realName = newVariable.variableName
      newVariable.variableName = `${newVariable.variableName}_${id}`
      newVariable.indexList = [index]
      const scopeVariablesIndex = scopeVariables.findIndex(v => v.name === newVariable.variableName)

      if (scopeVariablesIndex === -1) {
        scopeVariables.push(genSingleVariable(newVariable, original))
      } else {
        scopeVariables[scopeVariablesIndex].indexList.push(index)
      }
    }
  })
})

const variables = [...baseVariables, ...scopeVariables]

// 处理测试用例步骤
const xmindCases = [{
  title: original.caseName,
  steps: original.scripts.flatMap((script, index) => {
    return {
      title: script.scriptName,
      data: JSON.parse(replaceSingleScriptVariables(script.content, index, scopeVariables))
    }
  })
}];
  // 处理测试用例步骤
  const testCases = [{
    title: original.caseName,
    steps: original.scripts.flatMap((script, index) => 
        JSON.parse(replaceSingleScriptVariables(script.content, index, scopeVariables)).map(step => ({
            ...step
        }))
    )
  }];

  // 组合最终对象
  return {
      id,
      injectId: `${id}-${new Date().getTime()}`,
      app: original.app,
      caseId: original.id,
      product: original.product,
      config: {
          name: id,
          base: baseConfig,
          mock: mockConfig,
          variable: variables,
          preRequest: getStartUrl(original.env,original.config)
      },
      selector: {},
      cases: testCases,
      xmindCases: xmindCases
  };
}
/**
* 将测试用例JSON转换为树形结构JSON
* @param {Object} inputJson - 输入的测试用例JSON
* @returns {Object} - 转换后的树形结构JSON
*/
function transformJsonToTree(inputJson): { template: string; theme: string; root: { data: { text: any; note: string; imageSize: {} }; children: any[] } } {
  // 创建基础节点的方法
  interface TreeNode {
  data: {
    text: any;
    note: string;
    imageSize: {};
  };
  children: TreeNode[];
}

const createNode = (text: any, children: TreeNode[] = []): TreeNode => ({
    data: {
      text,
      note: '',
      imageSize: {}
    },
    children
  });

  // 创建叶子节点的方法
  const createLeafNode = (text) => createNode(text, []);

  // const configNode = (text) => createNode(text, []);

  const createLeafNodeForStep = (step) => {
    if (step.type === 'upload' && step.filePath) {
      return createNode(step.text, [createNode(`file:${step.filePath}`)]);
    }
    return createLeafNode(step.text);
  }

  // 创建基础树结构
  const tree = {
    template: 'default',
    theme: 'fresh-blue',
    root: createNode(inputJson.config.name)
  };

  // 创建配置节点
  const configNode = createNode('配置');

  // 添加基础配置
  const baseConfig = inputJson.config.base;
  Object.entries(baseConfig).forEach(([key, value]) => {
    if (value) {
      configNode.children.push(createNode(key, [createLeafNode(value)]));
    }
  });

  // 添加mock配置
  const mockNode = createNode('mock');

  // 添加cookies
  if (inputJson.config.mock.cookies && inputJson.config.mock.cookies.length > 0) {
    const cookiesNode = createNode('cookies');
    cookiesNode.children = inputJson.config.mock.cookies.map(cookie => 
      createNode(cookie.name, [createLeafNode(cookie.value)])
    );
    mockNode.children.push(cookiesNode);
  }

  // 添加localStorage
  if (inputJson.config.mock.localStorage && inputJson.config.mock.localStorage.length > 0) {
    const localStorageNode = createNode('localStorage');
    localStorageNode.children = inputJson.config.mock.localStorage.map(item => 
      createNode(item.key, [createLeafNode(item.value)])
    );
    mockNode.children.push(localStorageNode);
  }

  // 添加sessionStorage
  if (inputJson.config.mock.sessionStorage && inputJson.config.mock.sessionStorage.length > 0) {
    const sessionStorageNode = createNode('sessionStorage');
    sessionStorageNode.children = inputJson.config.mock.sessionStorage.map(item => 
      createNode(item.key, [createLeafNode(item.value)])
    );
    mockNode.children.push(sessionStorageNode);
  }

  // 添加request
  if (inputJson.config.mock.request && inputJson.config.mock.request.length > 0) {
    const requestNode = createNode('request');
    requestNode.children = inputJson.config.mock.request.map(req => {
      const requestItemNode = createNode(`${req.type}:${req.path}`);
      requestItemNode.children = [
        createNode('method', [createLeafNode(req.method)]),
        createNode('response', [createLeafNode(req.response)])
      ];
      return requestItemNode;
    });
    mockNode.children.push(requestNode);
  }

  configNode.children.push(mockNode);

  // 添加变量配置
  if (inputJson.config.variable && inputJson.config.variable.length > 0) {
    const variableNode = createNode('variable');
    variableNode.children = inputJson.config.variable.map(variable => {
      const node = createNode(variable.name, [createLeafNode(variable.value)]);

      // 处理前缀和后缀的通用方法
      const processAffix = (affix, type) => {
        if (!affix) return null;
        
        const affixNode = createNode(`${type}:${affix.type}`);

        if (affix.type === 'custom') {
          // 处理custom类型
          affixNode.children.push(createLeafNode(`length:${affix.length}`));

          // 添加类型标识（letters或number）
          if (affix.letters) {
            affixNode.children.push(createLeafNode('letters'));
          }
          if (affix.number) {
            affixNode.children.push(createLeafNode('number'));
          }
        } else if (affix.type === 'timestamp') {
          // timestamp类型不需要额外参数
          affixNode.children = [];
        }

        return affixNode;
      };

      // 添加前缀节点
      const prefixNode = processAffix(variable.prefix, 'prefix');
      if (prefixNode) {
        node.children.push(prefixNode);
      }

      // 添加后缀节点
      const suffixNode = processAffix(variable.suffix, 'suffix');
      if (suffixNode) {
        node.children.push(suffixNode);
      }

      return node;
    });
    configNode.children.push(variableNode);
  }

  tree.root.children.push(configNode);

  // 添加选择器节点
  if (inputJson.selector && Object.keys(inputJson.selector).length > 0) {
    const selectorNode = createNode('自动化标识');
    selectorNode.children = Object.entries(inputJson.selector).map(([key, value]) => 
      createNode(key, [createLeafNode(value)])
    );
    tree.root.children.push(selectorNode);
  }

  // 添加测试用例节点
  if (inputJson.xmindCases && inputJson.xmindCases.length > 0) {
    const casesNode = createNode('功能');
    casesNode.children = inputJson.xmindCases.map(testCase => {
      const caseNode = createNode("TL-用例&"+testCase.title);
      caseNode.children = testCase.steps.map(step => createNode('#脚本&'+step.title, step.data.map(step=>createLeafNodeForStep(step))));
      return caseNode;
    });
    tree.root.children.push(casesNode);
  }

  return tree;
}


export {transformJsonToTree,convertToTargetJSON,convertToJSONGroup} ;