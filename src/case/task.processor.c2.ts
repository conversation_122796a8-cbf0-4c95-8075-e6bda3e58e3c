import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { TestRecordService } from '../record/record.service';
import { BaseTaskProcessor } from './base.task.processor'; // 导入基类
import { Job } from 'bull';

@Injectable()
@Processor('taskRunner_c2') // 更新队列名称
export class TaskProcessorC2 extends BaseTaskProcessor {
  constructor(protected readonly recordService: TestRecordService) {
    super(recordService, TaskProcessorC2.name); // 调用父类构造函数
  }

  @Process({
    name: 'runTask',
    concurrency: 1, // 明确并发数为1
  })
  async handleTask(job: Job) {
    return super.handleTask(job); // 调用父类的方法
  }

  // handleTaskSuccess, handleTaskError, sendMsg, postDingNotice, sendNotification 方法已移至 BaseTaskProcessor
}