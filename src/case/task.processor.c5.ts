import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { TestRecordService } from '../record/record.service';
import { BaseTaskProcessor } from './base.task.processor';
import { Job } from 'bull';

@Injectable()
@Processor('taskRunner_c5')
export class TaskProcessorC5 extends BaseTaskProcessor {
  constructor(protected readonly recordService: TestRecordService) {
    super(recordService, TaskProcessorC5.name);
  }

  @Process({name: 'runTask',concurrency: 2})
  async handleTask(job: Job) {
    return super.handleTask(job);
  }
}