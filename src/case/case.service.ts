import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Code, Repository } from 'typeorm';
import { Case } from './case.entity';
import { Config } from '../config/config.entity';
import { InjectQueue, getQueueToken } from '@nestjs/bull';
import { Queue } from 'bull';
import { Like, IsNull, Not, In } from 'typeorm'; // 导入 Like 函数
import { QueryCaseDto } from './dto/query-case.dto';
import { convertToTargetJSON, convertToJSONGroup } from './dto/transformJson';
import { TestRecordService } from '../record/record.service';
import axios from 'axios';
import * as path from 'path';
import { env } from 'process';
const testService = require('../../core/index.js');
const xmindparser = require('../../core/xmindparserlib');

interface Task {
  id: number;
  operator: string;
  caseName: string;
  browser: string;
  groupName: string;
  notifyUrl: string;
  app: string;
  product: string;
  jsonData: any;
  taskId?: number;
  runId?: string;
  fileTag?: string;
  env?: string;
  runType?: string; // retry 重试
  concurrency?: string; // 添加并发参数
  testSuiteName?: string; // 确保 testSuiteName 存在
  testSuiteEnd?: number; // 确保 testSuiteEnd 存在
  projectType?: 'project' | 'iteration' | 'debug'; // 项目类型
}
// 定义队列映射类型
type QueueMap = {
  c1: Queue;
  c2: Queue;
  c3: Queue;
  c4: Queue;
  c5: Queue;
};
@Injectable()
export class CaseService {
  private readonly logger = new Logger(CaseService.name);
  private readonly queues: QueueMap;
  constructor(
    @InjectQueue('taskRunner_c1') private readonly taskQueueC1: Queue,
    @InjectQueue('taskRunner_c2') private readonly taskQueueC2: Queue,
    @InjectQueue('taskRunner_c3') private readonly taskQueueC3: Queue,
    @InjectQueue('taskRunner_c4') private readonly taskQueueC4: Queue,
    @InjectQueue('taskRunner_c5') private readonly taskQueueC5: Queue,
    @InjectRepository(Case)
    private caseRepository: Repository<Case>,
    @InjectRepository(Config)
    private configRepository: Repository<Config>, private readonly recordService: TestRecordService
  ) {
    this.queues = {
      c1: taskQueueC1,
      c2: taskQueueC2,
      c3: taskQueueC3,
      c4: taskQueueC4,
      c5: taskQueueC5
    };
  }
  /**
    * 获取队列长度（包含等待和正在执行的任务）
    * @returns 队列长度统计
    */
  async getQueueLengths() {
    const queueLengths = {};
    for (const [name, queue] of Object.entries(this.queues)) {
      try {
        const counts = await queue.getJobCounts();
        // 计算等待中的任务 + 正在执行的任务
        queueLengths[name] = counts.waiting + counts.active;
      } catch (error) {
        this.logger.error(`获取队列${name}长度失败: ${error.message}`);
        queueLengths[name] = -1;
      }
    }
    return queueLengths;
  }

  async addTask(taskData: Task): Promise<any> {
    const { caseName, concurrency } = taskData;
    this.logger.log(`添加任务请求: ${caseName}, 执行通道: ${concurrency}`);

    try {
      this.validateTaskData(taskData);

      // 创建任务记录
      const record = await this.createTaskRecord(taskData);
      taskData.taskId = record.id;
      taskData.fileTag = record.fileTag;
      // 选择队列并添加任务
      const { queue, queueName } = this.selectQueue(concurrency || 'c2');
      const job = await this.addJobToQueue(queue, taskData);

      // 更新任务记录
      await this.updateTaskRecordWithJobInfo(record.id, job.id, queueName);

      this.logger.log(`任务 ${caseName} (ID: ${record.id}) 成功添加到 ${queueName} 队列`);

      return {
        status: 'success',
        message: `任务添加成功`,
        taskId: record.id,
        jobId: job.id
      };
    } catch (error) {
      this.logger.error(`添加任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 停止整个组的任务
   * @param runId 运行ID
   */
  async stopGroupTask(runId: string): Promise<void> {
    this.logger.log(`停止组任务: ${runId}`);

    const records = await this.recordService.findByRunId(runId);
    if (!records.length) {
      this.logger.warn(`未找到运行ID为 ${runId} 的记录`);
      return;
    }

    const stopCurrentTest = async (record: any) => {
      try {
        await testService.stop(record.caseName);
        this.logger.log(`测试 ${record.caseName} 已停止`);
      } catch (error) {
        this.logger.error(`停止测试 ${record.caseName} 失败`, error);
      }
    };

    for (const record of records) {
      try {
        // 尝试从队列中移除任务
        if (record.runInfo) {
          const { bullJobId, queueName } = JSON.parse(record.runInfo);
          await this.removeJobFromQueue(bullJobId, queueName);
        }

        // 停止当前执行的测试
        await stopCurrentTest(record);

        // 更新未执行的任务记录
        if (record.status === 'pending') {
          await this.recordService.updateFields(record.id, {
            status: 'stop',
            endTime: new Date()
          });
        }

      } catch (error) {
        this.logger.error(`停止记录ID ${record.id} 失败: ${error.message}`);
      }
    }
  }

  /**
   * 停止单个任务
   * @param caseName 用例名称
   * @param id 记录ID
   */
  async stopTask(caseName: string, id: number): Promise<void> {
    this.logger.log(`停止任务: ${caseName} (ID: ${id})`);

    try {
      const record = await this.recordService.findOne(id);
      if (!record) {
        throw new Error(`找不到ID为 ${id} 的记录`);
      }

      let jobRemoved = false;
      let queueName = '';
      let originalRunInfo = record.runInfo || '';

      // 尝试从队列中移除任务
      if (record.runInfo) {
        try {
          const { bullJobId, queueName: qName } = JSON.parse(record.runInfo);
          if (bullJobId && qName) {
            await this.removeJobFromQueue(bullJobId, qName);
            jobRemoved = true;
            queueName = qName;
          }
        } catch (error) {
          this.logger.warn(`解析runInfo失败: ${error.message}`);
        }
      }

      // 尝试停止当前测试
      try {
        await testService.stop(caseName);
        this.logger.log(`测试 ${caseName} 已停止`);
      } catch (err) {
        this.logger.error(`停止测试失败: ${err.message}`);
      }

      // 更新任务记录
      await this.recordService.updateFields(id, {
        status: 'failed',
        runResult: 'fail',
        endTime: new Date(),
        runInfo: JSON.stringify({
          originalRunInfo,
          terminationReason: '手动终止任务',
          jobRemoved,
          queueName
        })
      });

      this.logger.log(`任务 ${caseName} (ID: ${id}) 已成功终止`);
    } catch (error) {
      this.logger.error(`停止任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 重新测试运行
   * @param id 记录ID
   * @param caseId 用例ID
   * @param operator 操作人
   */
  async retestRun(id: number, caseId: number, operator: string) {
    try {
      const caseData = await this.findOne(caseId);
      const record = await this.recordService.findOne(id);

      if (!record) {
        throw new Error('记录不存在');
      }

      const newRecord = {
        ...caseData,
        browser: record.browser || 'edge',
        app: record.app || 'app',
        product: record.product || 'product',
        env: record.env || 'test',
        groupName: caseData.groupName[0] || '未知组',
        notifyUrl: record.notifyUrl || '',
        operator: operator || record.operator|| '',
        projectType: record.projectType || 'debug',
        taskId: id,
        runId: record.runId,
        runType: 'retry'
      };

      // 执行测试用例
      return await this.runTestCase(newRecord);
    } catch (error) {
      this.logger.error(`重新测试失败: ${error.message}`);
      return {
        code: 500,
        message: `重新测试失败: ${error.message}`
      };
    }
  }

  async retryGroupTest(runId: string): Promise<any> {
    this.logger.log(`开始重试测试组运行ID: ${runId}`);

    // 获取失败记录
    // 分页获取失败记录
    const { data: failedRecords } = await this.recordService.findByRunIdAndStatus(
      runId,
      'fail',
      1, // 页码
      1000 // 每页数量
    );

    if (!failedRecords?.length) {
      this.logger.warn(`运行ID ${runId} 无失败记录可重试`);
      return {
        code: 404,
        message: '无可重试用例',
        details: {
          runId,
          failureReasons: ['NO_FAILED_CASES']
        }
      };
    }

    // 批量重试逻辑
    // 并行处理重试任务
    let results = [] as any;
    results = await Promise.all(failedRecords.map(async (record) => {
      try {
        const retryResult = await this.retestRun(
          record.id,
          record.caseId,
          record.operator || '',
        );
        results.push({
          caseId: record.caseId,
          status: 'retried',
          result: retryResult
        });
      } catch (error) {
        this.logger.error(`用例${record.caseId}重试失败: ${error.message}`);
        results.push({
          caseId: record.caseId,
          status: 'failed',
          error: error.message
        });
      }
    }));

    // 统计结果
    // const successCases = results.filter(r => r.status === 'retried');
    // const failedCases = results.filter(r => r.status === 'failed');

    return {
      code: 200,
      message: `重试完成`,
      data: {
        total: failedRecords.length,
        details: results
      }
    };
  }

  /**
   * 运行单个测试用例
   * @param data 测试数据
   */
  async runTestCase(data: any): Promise<any> {
    try {
      if (!data.caseName) {
        throw new Error('用例名称为空');
      }

      const jsonData = convertToTargetJSON(data);
      if (!jsonData.config.base.url) {
        throw new Error('用例URL不能为空');
      }
      const result = await this.addTask({
        jsonData,
        testSuiteEnd: 2,
        ...data
      });

      return {
        data: result,
        code: 200
      };
    } catch (e) {
      this.logger.error(`运行测试用例失败: ${e.message}`);
      return {
        code: 400,
        message: '执行出错',
        error: e.message
      };
    }
  }

  /**
   * 运行测试组用例
   * @param data 测试组数据
   */
  async runTestGroupCase(data: any): Promise<any> {
    try {
      // 获取测试用例
      const tasks = await this.getGroupTasks(data);
      if (tasks.length === 0) {
        return {
          code: 400,
          message: '用例不存在或用例组为空',
          data: null
        };
      }

      const results: any[] = [];
      for (const task of tasks) {
        try {
          if (!task.jsonData.config.base.url) {
            throw new Error('用例URL不能为空');
          }
          const result = await this.addTask(task);
          results.push(result);
        } catch (e) {
          this.logger.error(`添加任务失败: ${e.message}`);
          results.push({
            error: `任务添加失败: ${e.message}`,
            caseName: task.caseName
          });
        }
      }

      return {
        data: results,
        code: 200
      };
    } catch (e) {
      this.logger.error(`运行测试组失败: ${e.message}`);
      return {
        code: 500,
        message: '处理测试组出错',
        error: e.message
      };
    }
  }

  // ================= 私有辅助方法 ================= //

  /**
   * 验证任务数据有效性
   * @param taskData 任务数据
   */
  private validateTaskData(taskData: Task) {
    if (!taskData || !taskData.caseName) {
      throw new Error('任务数据无效，缺少 caseName');
    }
  }

  /**
   * 创建任务记录
   * @param taskData 任务数据
   */
  private async createTaskRecord(taskData: Task) {
    return this.recordService.create({
      caseId: taskData.id || 0,
      operator: taskData.operator,
      caseName: taskData.caseName,
      browser: taskData.browser,
      app: taskData.app,
      product: taskData.product,
      testSuiteName: taskData.testSuiteName,
      fileTag: taskData.fileTag || `${taskData.caseName}-${Date.now()}`,
      groupName: JSON.stringify(taskData.groupName) || '未设置分组',
      notifyUrl: taskData.notifyUrl,
      status: 'pending',
      startTime: new Date(),
      runId: taskData.runId || '',
      runType: taskData.runType || '',
      userAgent: '',
      env: taskData.env || '',
      runInfo: '',
      projectType: taskData.projectType || 'project',
    });
  }

  /**
   * 选择队列
   * @param concurrency 并发数
   */
  private selectQueue(concurrency: string): { queue: Queue; queueName: string } {
    const queueName = `taskRunner_${concurrency}`;
    return {
      queue: this.queues[concurrency],
      queueName
    };
  }

  /**
   * 添加任务到队列
   * @param queue 队列实例
   * @param taskData 任务数据
   */
  private async addJobToQueue(queue: Queue, taskData: Task) {
    return queue.add('runTask', taskData, {
      attempts: 2,
      backoff: { type: 'fixed', delay: (taskData as any).retryDelay || 1000 },
      timeout: 10 * 60 * 1000,
      removeOnComplete: true,
      removeOnFail: 50,
    });
  }

  /**
   * 更新任务记录
   * @param taskId 任务ID
   * @param jobId 工作ID
   * @param queueName 队列名称
   */
  private async updateTaskRecordWithJobInfo(taskId: number, jobId: any, queueName: string) {
    return this.recordService.updateFields(taskId, {
      runInfo: JSON.stringify({ bullJobId: jobId, queueName }),
      status: 'pending',
    });
  }

  /**
   * 从队列中移除任务
   * @param jobId 任务ID
   * @param queueName 队列名称
   */
  private async removeJobFromQueue(jobId: string, queueName: string): Promise<void> {
    if (!jobId || !queueName) return;

    const queueKey = queueName.replace('taskRunner_', '');
    const queue = this.queues[queueKey as keyof QueueMap];

    if (!queue) {
      this.logger.warn(`未知队列: ${queueName}`);
      return;
    }

    const job = await queue.getJob(jobId);
    if (!job) {
      this.logger.warn(`任务 ${jobId} 在队列 ${queueName} 中不存在`);
      return;
    }

    const state = await job.getState();
    if (['completed', 'failed'].includes(state)) {
      this.logger.log(`任务 ${jobId} 已处于最终状态: ${state}`);
      return;
    }

    await job.remove();
    this.logger.log(`任务 ${jobId} 已从队列 ${queueName} 中移除`);
  }

  /**
   * 获取组任务
   * @param data 任务数据
   */
  private async getGroupTasks(data: any): Promise<Task[]> {
    let taskList: any;

    if (data.strategy === 'group') {
      taskList = await this.findByGroupName(data.groupName, 1, 1000);
    } else if (data.cases?.length) {
      taskList = await this.findByIds(data.cases, 1, 1000);
    } else {
      this.logger.error('缺少任务选择策略或用例列表');
      throw new Error('任务选择策略无效');
    }

    if (!taskList || !taskList.data || taskList.data.length === 0) {
      return [];
    }

    return taskList.data.map((item: any, index: number) => ({
      jsonData: convertToTargetJSON({...item, env: data.env}),
      notifyUrl: data.notifyUrl,
      app: item.app,
      product: item.product,
      groupName: item.groupName,
      runId: data.runId,
      testSuiteName: data.testSuiteName || data.runId,
      testSuiteEnd: index === taskList.data.length - 1 ? 1 : 0,
      browser: data.browser,
      fileTag: data.fileTag,
      env: data.env,
      operator: data.operator,
      taskId: item.taskId,
      caseName: item.caseName,
      id: item.id,
      concurrency: data.concurrency,
      projectType: data.projectType || 'project'
    }));
  }
  async generateXmind(jsonData: object): Promise<any> {
    console.log('jsonData:', jsonData);
    const reportPath = path.join(__dirname, '../core/temp')
    console.log('reportPath:', reportPath);


    // 在文件生成后清除定时器
    let parser = new xmindparser()
    const result = await parser.JSONToXmind(jsonData, reportPath);
    const regex = /[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/;

    const match = result.match(regex);

    if (match) {
      console.log("Extracted UUID:", match[0]);
      return match[0];
    } else {
      console.log("No UUID found.");
      return null;
    }
  }
  async create(caseData: Partial<Case>): Promise<any> {
    // 检查caseName是否已存在
    const existingCase = await this.caseRepository.findOne({
      where: { caseName: caseData.caseName }
    });

    if (existingCase) {
      return { code: 400, message: `Case名称'${caseData.caseName}'已存在` };
    }

    const config = await this.configRepository.findOne({
      where: caseData.config ? { id: caseData.config.id } : {}
    });

    const newCase = this.caseRepository.create({
      ...caseData,
      // 确保 config 不是 null
      config: config || undefined
    });
    return this.caseRepository.save(newCase);
  }

  findAll(): Promise<Case[]> {
    return this.caseRepository.find({
      relations: ['scripts', 'config']
    });
  }
  async findByQuery(query: QueryCaseDto): Promise<{ data: Case[], total: number }> {
    // 使用 QueryBuilder 明确表别名
    const queryBuilder = this.caseRepository
      .createQueryBuilder('case')
      .leftJoinAndSelect('case.scripts', 'scripts')
      .leftJoinAndSelect('case.config', 'config')
      .leftJoinAndSelect('config.variables', 'variables');


    // 动态添加条件（明确指定表别名）
    if (query.caseName) {
      queryBuilder.andWhere('case.caseName LIKE :caseName', {
        caseName: `%${query.caseName}%`
      });
    }
    if (query.creator) {
      queryBuilder.andWhere('case.creator = :creator', {
        creator: query.creator
      });
    }
    if (query.status) {
      queryBuilder.andWhere('case.status = :status', {
        status: query.status
      });
    }
    if (query.groupName) {
      queryBuilder.andWhere('FIND_IN_SET(:groupName, case.groupName) > 0', { groupName: query.groupName });
    }
    if (query.app) {
      queryBuilder.andWhere('case.app = :app', {
        app: query.app
      });
    }
    if (query.product) {
      queryBuilder.andWhere('case.product = :product', {
        product: query.product
      });
    }
    // 获取总数
    const total = await queryBuilder.getCount();

    // 分页查询
    if (query.page && query.limit) {
      queryBuilder.skip((query.page - 1) * query.limit).take(query.limit);
    }

    // 查询数据
    const cases = await queryBuilder.orderBy('case.id', 'DESC').getMany();

    // 按 scriptOrder 手动排序 scripts 数组
    const data = cases.map(testCase => {
      if (testCase.scripts && testCase.scriptOrder) {
        // 创建脚本ID到脚本对象的映射
        const scriptMap = new Map<number, any>();
        testCase.scripts.forEach(script => scriptMap.set(script.id, script));

        // 按照 scriptOrder 的顺序重新构建 scripts 数组，包括重复项
        testCase.scripts = testCase.scriptOrder
          .map(id => scriptMap.get(Number(id)))
          .filter(script => script !== undefined); // 过滤掉不存在的脚本
      }
      return testCase;
    });

    return { data, total };
  }

  async findOne(id: number): Promise<any> {
    if (isNaN(id) || id === 0) {
      return {
        code: 400,
        message: 'id is 0 or id is underfinded'
      }
    }
    const foundCase = await this.caseRepository.findOne({
      where: { id },
      relations: ['scripts', 'config','config.variables']
    });
    if (!foundCase) {
      throw new Error('Case not found');
    }
    if (foundCase.scripts && foundCase.scriptOrder) {
      // 创建脚本ID到脚本对象的映射
      const scriptMap = new Map<number, any>();
      foundCase.scripts.forEach(script => scriptMap.set(script.id, script));

      // 按照 scriptOrder 的顺序重新构建 scripts 数组，包括重复项
      foundCase.scripts = foundCase.scriptOrder
        .map(id => scriptMap.get(Number(id)))
        .filter(script => script !== undefined); // 过滤掉不存在的脚本
      // console.log(testCase.scripts) // 打印排序后的 scripts 数组
    }
    return Promise.resolve(foundCase);
  }

  async update(id: number, updateData: Partial<Case>): Promise<Case> {
    const foundCase = await this.caseRepository.findOne({
      where: { id },
      relations: ['scripts', 'config']
    });
    if (!foundCase) {
      throw new Error('Case not found');
    }

    // 处理config字段更新
    if (updateData.config) {
      const config = await this.configRepository.findOne({
        where: { id: updateData.config.id }
      });
      // 如果config存在则更新，否则保持原有的config不变
      if (config) {
        foundCase.config = config;
      }
    }

    // 更新其他字段
    Object.assign(foundCase, updateData);

    // 如果有scripts字段，单独处理多对多关系
    if (updateData.scripts) {
      // 先清空原有的关联关系
      await this.caseRepository
        .createQueryBuilder()
        .relation(Case, 'scripts')
        .of(foundCase)
        .remove(foundCase.scripts);

      // 添加新的关联关系
      await this.caseRepository
        .createQueryBuilder()
        .relation(Case, 'scripts')
        .of(foundCase)
        .add(updateData.scripts);
    }

    // 保存更新
    await this.caseRepository.save(foundCase);

    // 重新查询获取更新后的完整实体
    const updatedCase = await this.caseRepository.findOne({
      where: { id },
      relations: ['scripts', 'config']
    });

    if (!updatedCase) {
      throw new Error('Case not found after update');
    }
    return updatedCase;
  }
  remove(id: number): Promise<void> {
    return this.caseRepository.delete(id).then(() => { });
  }
  searchByKeyword(keyword: string): Promise<Case[]> {
    return this.caseRepository.find({
      where: { caseName: Like(`%${keyword}%`) }, // 使用 Like 函数进行模糊查询
      relations: ['scripts', 'config', 'config.variables'] // 如果需要关联查询其他表，可以在这里添加
    });
  }
  async updateStatus(id: number, status: string): Promise<any> {
    await this.caseRepository.update(id, { status: String(status) });
    return {
      code: 200,
      message: '更新成功',
      data: {
        id,
        status
      }
    }
  }

  async findAllGroupNames(): Promise<{ groupName: string, count: number }[]> {
    const cases = await this.caseRepository.find({
      select: ['groupName'],
      where: { groupName: Not(IsNull()) }
    });

    const groupNameCounts = new Map<string, number>();

    cases.forEach(testCase => {
      if (Array.isArray(testCase.groupName)) {
        testCase.groupName.forEach(name => {
          groupNameCounts.set(name, (groupNameCounts.get(name) || 0) + 1);
        });
      } else if (testCase.groupName) {
        groupNameCounts.set(testCase.groupName, (groupNameCounts.get(testCase.groupName) || 0) + 1);
      }
    });

    return Array.from(groupNameCounts.entries()).map(([groupName, count]) => ({
      groupName,
      count
    }));
  }
  async findByGroupName(groupName: string | string[], page?: number, limit?: number): Promise<{ data: Case[], total: number }> {
    const queryBuilder = this.caseRepository
      .createQueryBuilder('case')
      .leftJoinAndSelect('case.scripts', 'scripts')
      .leftJoinAndSelect('case.config', 'config')
      .leftJoinAndSelect('config.variables', 'variables');
    // 处理字符串入参，匹配数组中包含该字符串的记录
    queryBuilder.where('FIND_IN_SET(:groupName, case.groupName) > 0', { groupName: groupName });


    const total = await queryBuilder.getCount();

    if (page && limit) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const cases = await queryBuilder.getMany();

    const data = cases.map(testCase => {
      if (testCase.scripts && testCase.scriptOrder) {
        const orderMap = new Map<number, number>();
        testCase.scriptOrder.forEach((id, index) => orderMap.set(Number(id), index));

        testCase.scripts = testCase.scripts.sort((a, b) => {
          const aOrder = orderMap.get(a.id) ?? Infinity;
          const bOrder = orderMap.get(b.id) ?? Infinity;
          return aOrder - bOrder;
        });
      }
      return testCase;
    });

    return { data, total };
  }
  async findAllApps(): Promise<string[]> {
    const cases = await this.caseRepository.find({ select: ['app'], where: { app: Not(IsNull()) } });
    const appSet = new Set<string>();
    cases.forEach(testCase => {
      if (testCase.app) {
        appSet.add(testCase.app);
      }
    });
    return Array.from(appSet);
  }
  async findAllProducts(): Promise<string[]> {
    const cases = await this.caseRepository.find({ select: ['product'], where: { product: Not(IsNull()) } });
    const productSet = new Set<string>();
    cases.forEach(testCase => {
      if (testCase.product) {
        productSet.add(testCase.product);
      }
    });
    return Array.from(productSet);
  }
  async findByIds(ids: number[], page?: number, limit?: number): Promise<{ data: Case[], total: number }> {
    const queryBuilder = this.caseRepository
      .createQueryBuilder('case')
      .leftJoinAndSelect('case.scripts', 'scripts')
      .leftJoinAndSelect('case.config', 'config')
      .leftJoinAndSelect('config.variables', 'variables')
      .where({ id: In(ids) });

    const total = await queryBuilder.getCount();

    if (page && limit) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const cases = await queryBuilder.getMany();

    // 用于构建 id 到 index 的映射
    const orderMap = new Map(ids.map((id, index) => [id, index]))

    const data = cases
      .map(testCase => {
        if (testCase.scripts && testCase.scriptOrder) {
          // 创建脚本ID到脚本对象的映射
          const scriptMap = new Map<number, any>();
          testCase.scripts.forEach(script => scriptMap.set(script.id, script));

          // 按照 scriptOrder 的顺序重新构建 scripts 数组，包括重复项
          testCase.scripts = testCase.scriptOrder
            .map(id => scriptMap.get(Number(id)))
            .filter(script => script !== undefined); // 过滤掉不存在的脚本
        }
        return testCase;
      })
      .sort((a, b) => {
        // 基于 orderMap 对 cases 进行排序
        const aOrder = orderMap.get(a.id) ?? Infinity;
        const bOrder = orderMap.get(b.id) ?? Infinity;
        return aOrder - bOrder;
      })

    return { data, total };
  }

}

