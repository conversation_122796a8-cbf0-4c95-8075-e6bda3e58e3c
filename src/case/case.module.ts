import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Case } from './case.entity';
import { CaseService } from './case.service';
import { CaseController } from './case.controller';
import { Config } from '../config/config.entity';
import { TestRecord } from '../record/record.entity';
import { ScriptModule } from '../script/script.module';
import { TestConfigModule } from '../config/config.module';
import { TestRecordModule } from '../record/record.module';
import { BullModule } from '@nestjs/bull';
@Module({
  imports: [
    TypeOrmModule.forFeature([Case, Config,TestRecord]),
    ScriptModule,
    TestConfigModule,
    TestRecordModule,
    BullModule.registerQueue({ name: 'taskRunner_c1' }),
    BullModule.registerQueue({ name: 'taskRunner_c2' }),
    BullModule.registerQueue({ name: 'taskRunner_c3' }),
    BullModule.registerQueue({ name: 'taskRunner_c4' }),
    BullModule.registerQueue({ name: 'taskRunner_c5' }),
  ],
  providers: [CaseService],
  controllers: [CaseController],
  exports: [CaseService]
})
export class TestCaseModule {}