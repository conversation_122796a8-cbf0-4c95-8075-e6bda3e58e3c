import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { TestRecordService } from '../record/record.service';
const testService = require('../../core/index.js');
import axios from 'axios';

export abstract class BaseTaskProcessor {
  protected readonly logger: Logger;

  constructor(
    protected readonly recordService: TestRecordService,
    processorName: string,
  ) {
    this.logger = new Logger(processorName);
  }

  // 将 handleTask, handleTaskSuccess, handleTaskError, sendMsg, postDingNotice, sendNotification 方法从 TaskProcessor 迁移到这里
  // 注意：@Process 装饰器不能在基类中，它需要在具体的子类中定义，并指定队列名和并发数

  async handleTask(job: Job) {
    const task = job.data;
    let timeoutId: NodeJS.Timeout | null = null;

    try {
      if (task.taskId) {
        await this.recordService.updateFields(task.taskId, {
          status: 'running',
          startTime: new Date(),
          runInfo: JSON.stringify({ jobId: job.id, attempts: job.attemptsMade + 1, queueName: job.queue.name }),
        });
        this.logger.log(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 状态更新为 'running'。`);
      } else {
        this.logger.warn(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} 没有taskId，无法更新状态为 'running'。`);
      }

      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          this.logger.error(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 执行超时...`);
          reject(new Error(`任务执行超时: ${task.caseName}`));
        }, job.opts.timeout || 10 * 60 * 1000);
      });

      const testPromise = testService.startWithJson(task);

      const result = await Promise.race([testPromise, timeoutPromise])
        .then(res => {
          clearTimeout(timeoutId as any);
          return res;
        })
        .catch(err => {
          clearTimeout(timeoutId as any);
          throw err;
        });
      await this.handleTaskSuccess(task, result, job);
      return result;
    } catch (error) {
      this.logger.error(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 执行失败: ${error.message}`, error.stack);

      if (job.attemptsMade < (job.opts.attempts || 1) - 1) {
        this.logger.warn(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 将进行BullMQ会自动处理重试逻辑)。失败原因: ${error.message}`);
        if (task.taskId) {
          if (!error.__TERMINATED && job.attemptsMade < (job.opts.attempts || 1) - 1) {
            this.logger.log(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 状态更新为 'retrying'。`);
            await this.recordService.updateFields(task.taskId, {
              status: 'running',
              runResult: 'fail',
              runInfo: JSON.stringify({
                jobId: job.id,
                attempts: job.attemptsMade + 1,
                nextAttempt: job.attemptsMade + 2,
                error: error.message,
                terminated: error.__TERMINATED ? true : undefined,
                queueName: job.queue.name
              }),
            });
            throw error;
          }
        }
      }

      this.logger.error(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${task.caseName} (taskId: ${task.taskId}) 已达到最大重试次数 (${job.opts.attempts}次)，最终标记为失败。`);
      await this.handleTaskError(task.taskId, error, job);
      throw error;
    } finally {
      if (timeoutId) clearTimeout(timeoutId);
    }
  }

  protected async handleTaskSuccess(task: any, result: any, job: Job): Promise<void> {
    this.logger.log(`[${job.queue.name}] Job ID: ${job.id} - 处理任务成功回调 for taskId: ${task.taskId}`);
    const endTime = new Date();
    let duration = 0;
    if (job.processedOn && job.timestamp) {
      duration = endTime.getTime() - new Date(job.timestamp).getTime();
    } else if (result && result.startTime) {
      try {
        const startTime = new Date(result.startTime);
        duration = endTime.getTime() - startTime.getTime();
      } catch (e) {
        this.logger.warn(`[${job.queue.name}] Job ID: ${job.id} - 无法从result.startTime计算时长: ${e.message}`);
      }
    }
    const updateData: any = {
      status: 'success',
      endTime: endTime,
      runResult: result.runResult || 'fail',
      runInfo: JSON.stringify({ jobId: job.id, finalAttempt: job.attemptsMade + 1, queueName: job.queue.name, ...result }),
      duration: duration,
    };
    const record = await this.recordService.updateFields(task.taskId, updateData);
    if (result) {
      this.logger.log(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${record.caseName} (taskId: ${task.taskId}) 成功结果详情:`, result);
      try {
        const notification = {
          ...result,
          duration: (Date.now() - new Date(result.startTime).getTime()),
          notifyUrl: task.notifyUrl,
          runId: task.runId,
          apiBaseUrl: process.env.API_SERVER_URL,
        };
        if (task.testSuiteEnd === 1) {
          const resData = await this.recordService.findByRunId(task.runId);
          await this.sendNotification(resData, task, job.queue.name);
        } else if (task.testSuiteEnd === 2) {
          await this.sendNotification([notification], task, job.queue.name);
        }
      } catch (notificationError) {
        this.logger.error(`[${job.queue.name}] 发送测试结果通知失败 (任务ID: ${task.taskId}): ${notificationError.message}`, notificationError.stack);
      }
    }
  }

  protected async handleTaskError(taskId: any, error: Error, job: Job): Promise<void> {
    this.logger.error(`[${job.queue.name}] Job ID: ${job.id} - 处理任务失败回调 for taskId: ${taskId}. Error: ${error.message}`);
    const endTime = new Date();
    let duration = 0;
    if (job.timestamp) {
      duration = endTime.getTime() - new Date(job.timestamp).getTime();
    }

    const updateData: any = {
      status: 'failed',
      endTime: endTime,
      runResult: 'fail',
      runInfo: JSON.stringify({
        jobId: job.id,
        finalAttempt: job.attemptsMade + 1,
        error: error.message,
        stack: error.stack?.substring(0, 1000),
        queueName: job.queue.name
      }),
    };
    if (duration > 0) {
      updateData.duration = duration;
    }

    const record = await this.recordService.updateFields(taskId, updateData);
    this.logger.error(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${record.caseName} (taskId: ${taskId}) 状态更新为 'failed'。耗时: ${duration}ms. 错误: ${error.message}`);

    if (record && record.notifyUrl) {
      await this.sendMsg(record, `【${record.caseName}】UI自动测试执行失败 (Job ID: ${job.id}, Queue: ${job.queue.name})尝试次数: ${job.attemptsMade + 1}错误信息: ${error.message}`, job.queue.name);
    } else {
      this.logger.warn(`[${job.queue.name}] Job ID: ${job.id} - 任务 ${taskId} (名称: ${record?.caseName}) 无法发送失败通知，记录信息不完整或无notifyUrl。`);
    }
  }

  async sendMsg(task: any, info: string, queueName: string) {
    try {
      const taskIdentifier = task.caseName ? task.caseName : (task.id ? `ID: ${task.id}` : '未知任务');
      this.logger.log(`[${queueName}] 准备发送通知给任务 ${taskIdentifier}. 内容摘要: ${(info && typeof info === 'string') ? info.substring(0, 100) : 'N/A'}...`);
      if (!task.notifyUrl || typeof task.notifyUrl !== 'string') {
        this.logger.error(`[${queueName}] 任务 ${taskIdentifier} 通知URL无效或未提供: ${task.notifyUrl}`);
        return;
      }

      try {
        new URL(task.notifyUrl);
      } catch (e) {
        this.logger.error(`[${queueName}] 任务 ${task.caseName ? task.caseName : (task.id ? 'ID: ' + task.id : '未知任务')} 无效的通知URL格式: ${task.notifyUrl}: ${e.message}`);
        return;
      }

      await axios.post(
        task.notifyUrl,
        JSON.stringify({
          msgtype: 'markdown',
          markdown: {
            title: '测试结果',
            text: info
          }
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          httpsAgent: null,
          httpAgent: null,
          proxy: false,
        }
      );
    } catch (error) {
      this.logger.error(`[${queueName}] 任务 ${task.caseName ? task.caseName : (task.id ? 'ID: ' + task.id : '未知任务')} 发送通知失败: ${error.message}`, error.stack);
    }
  }

  protected async postDingNotice(record: any, task: any, queueName: string) {
    this.logger.log(`[${queueName}] postDingNotice for task: ${task.caseName}, testSuiteEnd: ${task.testSuiteEnd}`);
    try {
      const testData = record;
      const detailUrl = task.testSuiteEnd === 2 
        ? `${process.env.API_SERVER_URL}/notify/getReports?caseName=${record[0].caseName}&fileTag=${record[0].fileTag}&groupName=${record[0].groupName}` 
        : ` ${process.env.API_SERVER_URL}/record/groupResult?runId=${task.runId}`; 
      const testResult = `### ${task.testSuiteName || '用例'}测试报告 (队列: ${queueName})
  | 用例名称 | 进度 | 结果 | 耗时(秒) |
  |---------|------|------|---------|
  ${testData.map((item: any) =>
        `| ${item.caseName} | ${item.status !== 'fail' ? '完成' : '未完成'} | ${item.runResult === 'success' ? '✅ 通过' : '❌ 未通过'} | ${(item.duration / 1000).toFixed(2)} |`
      ).join('\n')}
  
  **统计摘要**：
  - 测试开始时间：${new Date(testData[0].startTime).toLocaleString()} 
  - 总用例数：${testData.length} 次
  - 用例成功率：${((testData.filter((i: any) => i.runResult === 'success').length / testData.length) * 100).toFixed(1)}%
  - 执行完成率：${((testData.filter((i: any) => i.status !== 'fail').length / testData.length) * 100).toFixed(1)}%
  - 总耗时：${(testData.reduce((sum: number, i: any) => sum + i.duration, 0) / 1000).toFixed(2)}秒
  - [查看报告](${detailUrl})
  `;
      return axios.post(
        task.notifyUrl,
        JSON.stringify({
          msgtype: 'markdown',
          markdown: {
            title: '测试结果',
            text: testResult
          }
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          httpsAgent: null,
          httpAgent: null,
          proxy: false,
        },
      );
    } catch (e) {
      this.logger.error(`[${queueName}] postDingNotice error: ${e.message}`, e.stack);
    }
  }

  async sendNotification(data: any, task: any, queueName: string) {
    this.logger.log(`[${queueName}] sendNotification for task: ${task.caseName}`);
    try {
      if (task.notifyUrl) {
        this.logger.log(`[${queueName}] 发送钉钉通知并保存测试记录`, data);
        await this.postDingNotice(data, task, queueName);
      } else {
        this.logger.log(`[${queueName}] 未配置钉钉通知，仅保存测试记录`, data);
      }
    } catch (error) {
      this.logger.error(`[${queueName}] 记录保存或通知失败: ${error.message}`, error.stack);
    }
  }
}