import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, ManyToMany, JoinTable } from 'typeorm';
import { Script } from '../script/script.entity';
import { Config } from '../config/config.entity';
import { JsonTextTransformer } from '../util';

@Entity()
export class Case {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  caseName: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: 'unknown' })
  creator?: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: 'unknown' })
  updater?: string;

  @ManyToMany(() => Script)
  @JoinTable({ name: 'case_scripts_monitor_uitest_script' })
  scripts: Script[];

  @ManyToOne(() => Config)
  config: Config;

  @Column({ default: 'pending' })
  status: string;

  @Column({ type: 'simple-array'})
  groupName: string[];

  @Column({ default: '' })
  app: string;

  @Column({ default: '' })
  product: string;
  
  @Column("simple-array", { nullable: true })
  scriptOrder: number[];

  @Column({default: ''})
  comment: string;
  
  @Column({ 
    type: 'enum',
    enum: ['project', 'iteration', 'debug'],
    default: 'debug'
  })
  projectType: 'project' | 'iteration' | 'debug';

  @Column({ 
    type: 'text', 
    nullable: true,
    transformer: JsonTextTransformer
  })
  variableFormData: any;
}