import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { TestRecordService } from '../record/record.service';
import { BaseTaskProcessor } from './base.task.processor';
import { Job } from 'bull';

@Injectable()
@Processor('taskRunner_c4')
export class TaskProcessorC4 extends BaseTaskProcessor {
  constructor(protected readonly recordService: TestRecordService) {
    super(recordService, TaskProcessorC4.name);
  }

  @Process({
    name: 'runTask',
    concurrency: 1,
  })
  async handleTask(job: Job) {
    return super.handleTask(job);
  }
}