import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { TestRecordService } from '../record/record.service';
import { BaseTaskProcessor } from './base.task.processor';
import { Job } from 'bull';

@Injectable()
@Processor('taskRunner_c3')
export class TaskProcessorC3 extends BaseTaskProcessor {
  constructor(protected readonly recordService: TestRecordService) {
    super(recordService, TaskProcessorC3.name);
  }

  @Process({
    name: 'runTask',
    concurrency: 1,
  })
  async handleTask(job: Job) {
    return super.handleTask(job);
  }
}