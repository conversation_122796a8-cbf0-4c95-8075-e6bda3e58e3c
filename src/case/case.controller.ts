import { <PERSON>, Get, Post, Body, Param, Patch, Delete, BadRequestException } from '@nestjs/common';
import { CaseService } from './case.service';
import { ScriptService } from '../script/script.service';
import { ConfigService } from '../config/config.service';
import { TestRecordService } from '../record/record.service';
   
import { Case } from './case.entity';
import { CreateCaseDto } from './dto/create-case.dto';
import { Query } from '@nestjs/common'; // 导入 Query 装饰器
import { QueryCaseDto } from './dto/query-case.dto';

@Controller('cases')
export class CaseController {
  constructor(
    private readonly caseService: CaseService,
    private readonly scriptService: ScriptService,
    private readonly configService: ConfigService,
    private readonly testRecordService: TestRecordService,
  ) {}

  @Post('create')
  async create(@Body() caseData: CreateCaseDto) {
    const scriptIds = caseData.scripts;
    const scripts = await this.scriptService.findByIds(scriptIds);
    const configId = caseData.config;
    const config = configId ? await this.configService.findOne(configId) : null;
    if (configId && !config) {
      return { code: 400, message: `无效的配置ID: ${configId}` };
    }

    const partialCaseData: Partial<Case> = {
      ...caseData,
      scripts: scripts,
      scriptOrder: scriptIds,
      config: config ?? undefined,
      variableFormData: caseData.variableFormData,
    };
    return await this.caseService.create(partialCaseData);
  }
  @Post('retest')
  async retest(@Body() body: { id: number, caseId: number, operator: string }) {
    return this.caseService.retestRun(body.id,body.caseId,body.operator);
  }
  @Post('runTest')
  async runTest(@Body() body: CreateCaseDto) {
    const data = {
      ...body
    } 
    const res = await this.caseService.runTestCase(data);
    return res;
  }
  @Post('runTestGroup')
  async runTestGroup(@Body() body: CreateCaseDto & { concurrency?: number }) { // 添加 concurrency 参数
    const data = {
      ...body,
      concurrency: body.concurrency || 'c1' // 默认为1，如果未提供
    } 
    const res = await this.caseService.runTestGroupCase(data);
    return res;
  }
  @Post('/stopTest')
  async stopTest(@Body('caseName') caseName: string,@Body('id') id: number) {
    if (!caseName) {
      throw new BadRequestException('caseName is required');
    }
    if (!id) {
      throw new BadRequestException('id is required');
    }
    const result = await this.caseService.stopTask(caseName,id);
    return {
      message: 200,
      data: result
    };
  }

  @Post('/stopGroupTest')
  async stopGroupTest(@Body('runId') runId: string) {
    if (!runId) {
      throw new BadRequestException('runId is required');
    }
    const result = await this.caseService.stopGroupTask(runId);
    return {
      message: 200,
      data: result
    };
  }

  @Post('retryGroupTest')
  async retryGroupTest(@Body('runId') runId: string) {
    if (!runId) {
      throw new BadRequestException('runId is required');
    }
    const result = await this.caseService.retryGroupTest(runId);
    return {
      code: 200,
      message: '重试操作已提交',
      data: result
    };
  }
  @Get('list')
  findAll(@Query() query: QueryCaseDto) {
    return this.caseService.findByQuery(query);
  }

  @Get('search')
  searchByKeyword(@Query('keyword') keyword: string) {
    return this.caseService.searchByKeyword(keyword);
  }

  @Get('groupNames')
  async getGroupNames() {
    return this.caseService.findAllGroupNames();
  }

  @Get('queueData')
  async getQueueLengths() {
    const result = await this.caseService.getQueueLengths();
    return {
      message: 200,
      data: result
    };
  }

  @Get('apps')
  async getApps() {
    return this.caseService.findAllApps();
  }

  @Get('products')
  async getProducts() {
    return this.caseService.findAllProducts();
  }
  @Get('group')
  findByGroupName(@Query('groupName') groupName: string) {
    return this.caseService.findByGroupName(groupName);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.caseService.findOne(+id);
  }
  @Post('byIds')
  async findByIds(@Body() body: { ids: number[] }) {
    try {
      const cases = await this.caseService.findByIds(body.ids);
      return { code: 200, data: cases };
    } catch (error) {
      return { code: 500, message: '查询失败：' + error.message };
    }
  }
  @Post('status')
  async updateStatus(@Body('id') id: string, @Body('status') status: string) {
    console.log(id,status)
    await  this.caseService.updateStatus(+id, status);
    return { message: '更新成功' };
  }
  @Post(':id')
  async update(@Param('id') id: string, @Body() updateData: any) {
    const scriptIds = updateData.scriptIds;
    const scripts = await this.scriptService.findByIds(scriptIds);
    const configId = updateData.config;
    const config = configId ? await this.configService.findOne(configId) : null;
    if (configId && !config) {
      throw new BadRequestException(`无效的配置ID: ${configId}`);
    }

    const partialCaseData: Partial<Case> = {
      ...updateData,
      scripts: scripts,
      scriptOrder: scriptIds,
      config: configId,
      variableFormData: updateData.variableFormData,
    };
    return this.caseService.update(+id, partialCaseData);
  }

  @Patch(':id')
  partialUpdate(@Param('id') id: string, @Body() updateData: Partial<Case>) {
    return this.caseService.update(+id, updateData);
  }



  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.caseService.remove(+id);
  }


}

  



  