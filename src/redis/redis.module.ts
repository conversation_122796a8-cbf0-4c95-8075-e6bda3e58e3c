import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';

@Module({
  providers: [
    {
      provide: 'REDIS_SERVICE',
      useFactory: (configService: ConfigService) => {
        const client = new Redis({
          host: configService.get<string>('REDIS_HOST'),
          port: configService.get<number>('REDIS_PORT'),
          db: configService.get<number>('REDIS_DB'),
          password: configService.get<string>('REDIS_PASSWORD'),
        });
        console.log('Reids链接成功');
        return client;
      },
      inject: [ConfigService],
    },
  ],
  exports: ['REDIS_SERVICE'],
})
export class RedisModule {}
