import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NotifyModule } from './notify/notify.module'; 
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRecordModule } from 'src/record/record.module';
import { ScriptModule } from 'src/script/script.module';
import { TestCaseModule} from 'src/case/case.module';
import { TestConfigModule} from 'src/config/config.module';
import { VariableModule} from 'src/variable/variable.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { BullModule } from '@nestjs/bull';
import { WorkerModule } from './worker.module';
import { join } from 'path';
import config from './config'
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: config
    }), 
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: 6379,
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DB)
      },
      defaultJobOptions: {
        attempts: 2, // 默认重试次数
        backoff: {
          type: 'exponential',
          delay: 3000 // 10秒，之后指数增长
        },
        timeout: 30 * 60 * 1000, // 30分钟超时
        removeOnComplete: true,
        removeOnFail: false
      }
    }),
    WorkerModule, // 添加 WorkerModule
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'reporter'), // reporter文件目录
      serveRoot: '/reporter', // 访问路径
      exclude: ['/api*'], // 排除 API 路由
      serveStaticOptions: {
        setHeaders: (res, path) => {
          if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
          } else if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css');
          }
        }
      }
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'client'),
      serveRoot: '/',
      exclude: ['/api*', '/screenshots*', '/reporter*'], // 增加排除规则
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'screenshots'), // 前端文件目录（如 dist, public 等）
      serveRoot: '/screenshots', // 访问路径
      exclude: ['/api*'], // 排除 API 路由
    }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DATABASE_HOST,
      port: Number(process.env.DATABASE_PORT),
      username: process.env.DATABASE_username,
      password: process.env.DATABASE_password,
      database: process.env.DATABASE_database,
      // logging: true,
      autoLoadEntities: true,
      synchronize: true, // TypeORM 不支持某些索引选项和定义，禁止生产环境使用，否则索引数据将会丢失
    }), 
    NotifyModule,
    TestRecordModule,
    ScriptModule,
    TestCaseModule,
    TestConfigModule,
    VariableModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
