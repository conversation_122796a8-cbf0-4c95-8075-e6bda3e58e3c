import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { NotifyService } from './notify.service';
@Controller('notify')
export class NotifyController {
  constructor(private readonly notifyService: NotifyService) { }

  @Post('finishTest')
  sendNotification(@Body() notification) {
    this.notifyService.sendNotification(notification);
  }

  @Get('getReports')
  getReports(@Query('caseName') caseName: string, @Query('fileTag') fileTag: string, @Query('groupName') groupName: string) {
    return this.notifyService.getReports(caseName, fileTag , groupName);
  }
}
