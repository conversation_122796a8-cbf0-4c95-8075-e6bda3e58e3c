// notify.module.ts
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRecord } from '../record/record.entity';
import { NotifyService } from './notify.service';
import { NotifyController } from './notify.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([TestRecord]), // 注入实体仓库
    HttpModule // 启用HTTP服务
  ],
  providers: [NotifyService],
  exports: [NotifyService],
  controllers: [NotifyController],
})
export class NotifyModule {}
