import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestRecord } from '../record/record.entity';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
@Injectable()
export class NotifyService {
  constructor(
    @InjectRepository(TestRecord)
    private readonly testRecordRepository: Repository<TestRecord>
  ) { }
  private notifications: string[] = [];
  private async postDingNotice(record: any) {
    console.log(process.env.NODE_ENV)
    try {
      const testData = JSON.parse(record.testInfo)
      const testResult = `### ${record.caseName}测试报告
  | 用例名称 | 状态 | 耗时(秒) |
  |---------|------|---------|
  ${testData.map(item =>
        `| ${item.name} | ${item.status !== 'fail' ? '✅ 成功' : '❌ 失败'} | ${(item.duration / 1000).toFixed(2)} |`
      ).join('\n')}
  
  **统计摘要**：
  - 测试开始时间：${new Date(record.startTime).toLocaleString()} 
  - 总测试次数：${testData.length} 次
  - 通过率：${((testData.filter(i => i.status !== 'fail').length / testData.length) * 100).toFixed(1)}%
  - 总耗时：${(testData.reduce((sum, i) => sum + i.duration, 0) / 1000).toFixed(2)}秒
  - [查看报告](${record.apiBaseUrl}/notify/getReports?caseName=${record.caseName}&fileTag=${record.fileTag}&groupName=${record.groupName})
  `;
      return axios.post(
        record.notifyUrl,
        JSON.stringify({
          msgtype: 'markdown',
          markdown: {
            title: '测试结果',
            text: testResult
          }
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          httpsAgent: null,
          httpAgent: null,
          proxy: false,
        },
      );
    } catch (e) {
      console.log(e)
    }
  }
  // 发送通知并保存测试记录
  async sendNotification(data) {
    // 创建测试记录对象
    try {
      // 钉钉通知逻辑保持不变
      this.notifications.push(data);
      if(data.notifyUrl){
        console.log('发送通知并保存测试记录', data)
        await this.postDingNotice(data);
      }else {
        console.log('未配置钉钉通知，仅保存测试记录', data)
      }
    } catch (error) {
      console.error('记录保存失败:', error.message);
    }


  }
  getReports(caseName: string, fileTag: string, groupName: string): string {
    const reportPath = path.join(__dirname, '../../reports', `report-${caseName}-${fileTag}.html`);
    const reportHTMLPath = path.join(__dirname, '../../reports/html', `report-${caseName}-${fileTag}.html`);
    console.log('reportPath:', reportHTMLPath||reportPath);
    if (fs.existsSync(reportPath)||fs.existsSync(reportHTMLPath)) {
      if(fs.existsSync(reportHTMLPath)){
        return fs.readFileSync(reportHTMLPath, 'utf8');
      }
      return fs.readFileSync(reportPath, 'utf8');
    } else {
      return `Report for ${caseName} not found.`;
    }
  }
}
