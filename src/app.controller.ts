import { Controller, Post, Body, BadRequestException, Get, Query, Res, Req,StreamableFile } from '@nestjs/common';
import { AppService } from './app.service';
import * as path from 'path';
import * as fs from 'fs';
import { Request, Response } from 'express';
import { join } from 'path';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Post('/startTest')
  async startTest(@Body() body: {
    caseId:number;
    groupName: string;
    operator: string;
    caseName: string;
    browser: string;
    notifyUrl: string;
    app: string;
    product: string;
    jsonData: any;
  }) {
    if (!body.caseName) {
      throw new BadRequestException('caseName is required');
    }
    const data = {
      ...body
    } 
    const result = await this.appService.addTask(data);
    return {
      message: 200,
      data: result
    };
  }
  @Post('/stopTest')
  async stopTest(@Body('caseName') caseName: string,@Body('id') id: number) {
    if (!caseName) {
      throw new BadRequestException('caseName is required');
    }
    if (!id) {
      throw new BadRequestException('id is required');
    }
    const result = await this.appService.stopTask(caseName,id);
    return {
      message: 200,
      data: result
    };
  }
  @Post('/batchStartTest')
  async batchStartTest(@Body() body: {
    strategy: 'group' | 'cases';
    groupName?: string;
    operator?: string;
    caseNames?: string[];
    browser?: string;
    notifyUrl: string;
    cases?: any[];
  }) {
    if (!body.strategy) {
      throw new BadRequestException('strategy is required');
    }

    if ( (!body.cases || body.cases.length === 0)) {
      throw new BadRequestException('caseNames array is required for cases strategy');
    }
    if (body.cases && Array.isArray(body.cases)) {
      const results = await Promise.all(body.cases.map(async (item) => {
        try {
          const res = await this.appService.addTask({
            caseId: item.caseId,
            app: item.app,
            product: item.product,
            operator: body.operator||'',
            caseName: item.cases[0].title,
            browser: body.browser || 'chrome:headless',
            notifyUrl: body.notifyUrl,
            groupName: body.groupName || '默认组',
            jsonData: item
          });
          console.log('结果',res);
          return { status: res.status };
        } catch (error) {
          return { status: 'error' };
        }
      }));

      const successAddTask = results.filter(r => r.status === 'success').length;
      const failedAddTask = results.filter(r => r.status === 'error').length;

      return{
        code: 200,
        message: `批量添加成功${successAddTask}条,${failedAddTask}条执行中`,
      }

    } 
    return {
      message: 200,
      data: "任务已经添加到队列请稍后"
    };
  }
  @Post('/generate')
  async generateXmind(@Body() body: any) {
    try {
      const uuid = await this.appService.generateXmind(body);
      return {
        code: 0,
        data: `${process.env.API_SERVER_URL}/download?uuid=${uuid}`
      }
    } catch (error) {
      console.log(error)
    }
  }
  @Get('/download')
  async download(@Query('uuid') uuid: any, @Res({ passthrough: true }) res: Response) {
    const filePath = path.join(__dirname, '../core/temp', uuid + '.xmind');
    try {
      const filename = path.basename(filePath);
      res.set({
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${uuid}.xmind"`
      });

      const fileStream = fs.createReadStream(filePath);
      return new StreamableFile(fileStream);
    } catch (error) {
      console.log(error)
    }
  }
  @Get(['/userCase','/userEdit','/userCase/*','/record', '/record/*','/variable/*', '/script', '/config/*'])
  serveFrontend(@Req() req: Request, @Res() res: Response) {
    // 处理静态资源
    if (req.path.startsWith('/assets/')) {
      return res.sendFile(join(__dirname, '..', 'client', req.path));
    }
    // 默认返回前端入口文件
    return res.sendFile(join(__dirname, '..', 'client', 'index.html'));
  }

}