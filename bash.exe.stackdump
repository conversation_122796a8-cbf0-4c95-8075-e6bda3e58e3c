Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FEBA
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210285FF9, 0007FFFFAAB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAC00  0002100690B4 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEE0  00021006A49D (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA1D080000 ntdll.dll
7FFA1C840000 KERNEL32.DLL
7FFA1A520000 KERNELBASE.dll
7FFA16380000 apphelp.dll
7FFA1B3F0000 USER32.dll
7FFA1A260000 win32u.dll
7FFA1CED0000 GDI32.dll
7FFA1A340000 gdi32full.dll
7FFA1A290000 msvcp_win.dll
7FFA1A8F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA1CF80000 advapi32.dll
7FFA1AEB0000 msvcrt.dll
7FFA1CE10000 sechost.dll
7FFA1C910000 RPCRT4.dll
7FFA19880000 CRYPTBASE.DLL
7FFA1A480000 bcryptPrimitives.dll
7FFA1C6F0000 IMM32.DLL
