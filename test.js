const { request } = require("./core/actions/composite/request");

const t1 = [
    {
        curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
        --header 'X-Tsign-Open-App-Id: **********' \
        --header 'X-Tsign-Service-Group: DEFAULT' \
        --header 'X-Tsign-Open-Auth-Mode: Secret' \
        --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
        --header 'Content-Type: application/json' \
        --data '{
            "docs": [
                {
                    "fileId": "faa68ff796c44e30a17ea87a6343dbe2"
                }
            ],
            "flowInfo": {
                "autoArchive": true,
                "autoInitiate": true,
                "businessScene": "签署详情页个人及企业签（无需审批）",
                "flowConfigInfo": {
                    "buttonConfig": {
                        "propButton": "1,2,16"
                    },
                    "notifyConfig": {
                        "noticeDeveloperUrl": "https://libaohui.com.cn/callback/ding",
                        "noticeType": "1",
                        "sendNotice": true
                    },
                    "signConfig": {
                        "archiveLock": false,
                        "batchDropSeal": true,
                        "countdown": 0,
                        "esignCertSign": false,
                        "esignQrPos": "",
                        "mobileShieldWay": 0,
                        "redirectDelayTime": 0,
                        "redirectUrl": "https://www.baidu.com",
                        "showQrCode": false,
                        "signPlatform": "1,2,3",
                        "signQrSwitch": false
                    }
                },
                "initiatorAccountId": "77248be00a0a43268008b8f9241c236a",
                "initiatorAuthorizedAccountId": "77248be00a0a43268008b8f9241c236a"
            },
            "signers": [
                {
                    "forceReadTime": 0,
                    "platformSign": false,
                    "signOrder": 1,
                    "signerAccount": {
                        "authorizedAccountId": "77248be00a0a43268008b8f9241c236a",
                        "noticeType": "2",
                        "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                    },
                    "signfields": [
                        {
                            "actorIndentityType": 0,
                            "assignedPosbean": false,
                            "autoExecute": false,
                            "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                            "posBean": {
                                "posPage": "1",
                                "posX": 200,
                                "posY": 200
                            },
                            "sealId": "",
                            "sealType": "",
                            "signDateBean": {
                                "fontSize": 20,
                                "posX": 200,
                                "posY": 200
                            },
                            "signDateBeanType": "1",
                            "signType": 1,
                            "width": 0
                        }
                    ],
                    "thirdOrderNo": "111"
                },
                {
                    "forceReadTime": 0,
                    "platformSign": false,
                    "signOrder": 1,
                    "signerAccount": {
                        "authorizedAccountId": "39178d0903174cf8bd124b766263c482",
                        "noticeType": "2",
                        "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                    },
                    "signfields": [
                        {
                            "actorIndentityType": 2,
                            "assignedPosbean": false,
                            "autoExecute": false,
                            "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                            "posBean": {
                                "posPage": "1",
                                "posX": 400,
                                "posY": 200
                            },
                            "sealId": "",
                            "sealType": "",
                            "signDateBeanType": "1",
                            "signType": 2,
                            "width": 0
                        }
                    ],
                    "thirdOrderNo": "111"
                }
            ]
        }'`,
        data: {
            flowId: "data.flowId"
        }
    },
    {
        curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{
        "docs": [
            {
                "fileId": "faa68ff796c44e30a17ea87a6343dbe2"
            }
        ],
        "flowInfo": {
            "autoArchive": true,
            "autoInitiate": true,
            "businessScene": "签署详情页个人及企业签（无需审批）",
            "flowConfigInfo": {
                "buttonConfig": {
                    "propButton": "1,2,16"
                },
                "notifyConfig": {
                    "noticeDeveloperUrl": "https://libaohui.com.cn/callback/ding",
                    "noticeType": "1",
                    "sendNotice": true
                },
                "signConfig": {
                    "archiveLock": false,
                    "batchDropSeal": true,
                    "countdown": 0,
                    "esignCertSign": false,
                    "esignQrPos": "",
                    "mobileShieldWay": 0,
                    "redirectDelayTime": 0,
                    "redirectUrl": "https://www.baidu.com",
                    "showQrCode": false,
                    "signPlatform": "1,2,3",
                    "signQrSwitch": false
                }
            },
            "initiatorAccountId": "77248be00a0a43268008b8f9241c236a",
            "initiatorAuthorizedAccountId": "77248be00a0a43268008b8f9241c236a"
        },
        "signers": [
            {
                "forceReadTime": 0,
                "platformSign": false,
                "signOrder": 1,
                "signerAccount": {
                    "authorizedAccountId": "77248be00a0a43268008b8f9241c236a",
                    "noticeType": "2",
                    "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                },
                "signfields": [
                    {
                        "actorIndentityType": 0,
                        "assignedPosbean": false,
                        "autoExecute": false,
                        "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                        "posBean": {
                            "posPage": "1",
                            "posX": 200,
                            "posY": 200
                        },
                        "sealId": "",
                        "sealType": "",
                        "signDateBean": {
                            "fontSize": 20,
                            "posX": 200,
                            "posY": 200
                        },
                        "signDateBeanType": "1",
                        "signType": 1,
                        "width": 0
                    }
                ],
                "thirdOrderNo": "111"
            },
            {
                "forceReadTime": 0,
                "platformSign": false,
                "signOrder": 1,
                "signerAccount": {
                    "authorizedAccountId": "39178d0903174cf8bd124b766263c482",
                    "noticeType": "2",
                    "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                },
                "signfields": [
                    {
                        "actorIndentityType": 2,
                        "assignedPosbean": false,
                        "autoExecute": false,
                        "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                        "posBean": {
                            "posPage": "1",
                            "posX": 400,
                            "posY": 200
                        },
                        "sealId": "",
                        "sealType": "",
                        "signDateBeanType": "1",
                        "signType": 2,
                        "width": 0
                    }
                ],
                "thirdOrderNo": "111"
            }
        ]
    }'`,
        data: {
            url: "data.shortUrl"
        }
    }
];

const t2 = [
  {
    curl: `curl --location --request POST  'http://in-testopenapi.tsign.cn/v1/accounts/createOverall' \
    --header 'Content-Type: application/json; charset=UTF-8' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'Accept: *' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    --header 'filter-result: false' \
    --data '{"idcards":{"mobile":"***********"}}'`,
    data: {
      accountId: "data.accountId"
    }
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/accounts/{accountId}' \
    --header 'Content-Type: application/json; charset=UTF-8' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'Accept: *' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    --header 'filter-result: false' \
    --data '{"idcards":{"mobile":"***********"}}'`,
  },
  {
    curl: `curl --location --request POST  'http://in-testopenapi.tsign.cn/v1/accounts/createOverall' \
    --header 'Content-Type: application/json; charset=UTF-8' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'Accept: *' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    --header 'filter-result: false' \
    --data '{"idcards":{"mobile":"***********"}}'`,
    data: {
      accountId: "data.accountId"
    }
  },
  {
    curl: `curl --location --request POST  'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"无姓名用户实名签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"identificationConfig":{"personAvailableAuthTypes":["PSN_TELECOM_AUTHCODE"],"willTypes":["CODE_SMS"]},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"{accountId}","noticeType":"2","signerAccountId":"{accountId}"},"signfields":[{"actorIndentityType":0,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"1","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    },
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId={accountId}&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
];

const t3 = [
    {
        curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{
        "docs": [
            {
                "fileId": "faa68ff796c44e30a17ea87a6343dbe2"
            }
        ],
        "flowInfo": {
            "autoArchive": true,
            "autoInitiate": true,
            "businessScene": "签署详情页",
            "flowConfigInfo": {
                "buttonConfig": {
                    "propButton": "1,2,16"
                },
                "notifyConfig": {
                    "noticeDeveloperUrl": "https://libaohui.com.cn/callback/ding",
                    "noticeType": "1",
                    "sendNotice": true
                },
                "signConfig": {
                    "archiveLock": false,
                    "batchDropSeal": true,
                    "countdown": 0,
                    "esignCertSign": false,
                    "esignQrPos": "",
                    "mobileShieldWay": 0,
                    "redirectDelayTime": 3,
                    "redirectUrl": "https://www.baidu.com",
                    "showQrCode": false,
                    "signPlatform": "1,2,3",
                    "signQrSwitch": false
                }
            },
            "initiatorAccountId": "77248be00a0a43268008b8f9241c236a",
            "initiatorAuthorizedAccountId": "77248be00a0a43268008b8f9241c236a"
        },
        "signers": [
            {
                "forceReadTime": 0,
                "platformSign": false,
                "signOrder": 1,
                "signerAccount": {
                    "authorizedAccountId": "77248be00a0a43268008b8f9241c236a",
                    "noticeType": "2",
                    "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                },
                "signfields": [
                    {
                        "actorIndentityType": 0,
                        "assignedPosbean": false,
                        "autoExecute": false,
                        "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                        "posBean": {
                            "posPage": "1",
                            "posX": 200,
                            "posY": 200
                        },
                        "sealId": "",
                        "sealType": "",
                        "signDateBean": {
                            "fontSize": 20,
                            "posX": 200,
                            "posY": 200
                        },
                        "signDateBeanType": "1",
                        "signType": 1,
                        "width": 0
                    }
                ],
                "thirdOrderNo": "111"
            }
        ]
    }'`,
        data: {
            flowId: "data.flowId"
        }
    },
    {
        curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
        data: {
            url: "data.shortUrl"
        }
    }
];

const t4 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署走审批","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"39178d0903174cf8bd124b766263c482","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":400,"posY":200},"sealId":"","sealType":"","signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location  --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
]

const t4_1 = [
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v2/signflows/{flowId}/detail' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
      approvalFlowId: "data.signDocs[0].signfields[0].approvalFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v1/approvalflows/{approvalFlowId}/approvalUrl?queryAccountId=79bc03b0cc5b4417a7ca06bcd00fa822' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
]

const t5 = [
  {
    curl: `curl --location --request POST  'http://in-testopenapi.tsign.cn/v1/accounts/createOverall' \
    --header 'Content-Type: application/json; charset=UTF-8' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'Accept: *' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    --header 'filter-result: false' \
    --data '{"idcards":{"mobile":"***********"}}'`,
    data: {
      accountId: "data.accountId"
    }
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/accounts/{accountId}' \
    --header 'Content-Type: application/json; charset=UTF-8' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'Accept: *' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    --header 'filter-result: false'`,
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"signFlowConfig":{"authConfig":{"audioVideoTemplateId":"","orgAvailableAuthModes":[],"orgEditableFields":["orgName"],"psnAvailableAuthModes":["PSN_TELECOM_AUTHCODE"],"psnEditableFields":["mobile","bankCardNum"],"willingnessAuthModes":[]},"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1"},"notifyUrl":"https://libaohui.com.cn/callback/ding","redirectConfig":{"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com"},"signFlowTitle":"个人实名代替意愿"},"signers":[{"noticeConfig":{"noticeTypes":"1"},"psnSignerInfo":{"psnAccount":"***********","psnInfo":{"psnIDCardNum":"14022619951019616X","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnName":"测试有姓名"}},"signConfig":{},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"1","signFieldPosition":{"acrossPageMode":"ALL","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":0,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":0,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":0}]}'`,
    data: {
        signFlowId: "data.signFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{signFlowId}/executeUrl?signerAccount=***********&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.shortUrl"
    }
  }
];

const t6 = [
    {
        curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{
        "docs": [
            {
                "fileId": "faa68ff796c44e30a17ea87a6343dbe2"
            }
        ],
        "flowInfo": {
            "autoArchive": true,
            "autoInitiate": true,
            "businessScene": "个人签署-需要登录",
            "flowConfigInfo": {
                "buttonConfig": {
                    "propButton": "1,2,16"
                },
                "notifyConfig": {
                    "noticeDeveloperUrl": "https://libaohui.com.cn/callback/ding",
                    "noticeType": "1",
                    "sendNotice": true
                },
                "signConfig": {
                    "archiveLock": false,
                    "batchDropSeal": true,
                    "countdown": 0,
                    "esignCertSign": false,
                    "esignQrPos": "",
                    "mobileShieldWay": 0,
                    "redirectDelayTime": 0,
                    "redirectUrl": "https://www.baidu.com",
                    "showQrCode": false,
                    "signPlatform": "1,2,3",
                    "signQrSwitch": false
                }
            }
        },
        "signers": [
            {
                "platformSign": false,
                "signOrder": 1,
                "signerAccount": {
                    "authorizedAccountId": "77248be00a0a43268008b8f9241c236a",
                    "noticeType": "1,2",
                    "signerAccountId": "77248be00a0a43268008b8f9241c236a"
                },
                "signfields": [
                    {
                        "actorIndentityType": 0,
                        "assignedPosbean": true,
                        "autoExecute": false,
                        "fieldType": 0,
                        "fileId": "faa68ff796c44e30a17ea87a6343dbe2",
                        "posBean": {
                            "posPage": "1",
                            "posX": 200,
                            "posY": 200
                        },
                        "sealId": "",
                        "sealType": "0,1",
                        "signType": 1,
                        "width": 0
                    }
                ],
                "thirdOrderNo": "11111"
            }
        ]
    }'`,
        data: {
            flowId: "data.flowId"
        }
    },
    {
        curl: `curl --location  --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
        data: {
            url: "data.shortUrl"
        }
    }
];

const t7 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":1},"noticeConfig":{"noticeTypes":"1"},"notifyUrl":"https://libaohui.com.cn/callback/ding","signConfig":{"availableSignClientTypes":"","showBatchDropSealButton":true},"signFlowTitle":"法人或签多级审批H5端"},"signers":[{"noticeConfig":{"noticeTypes":""},"orgSignerInfo":{"orgId":"39178d0903174cf8bd124b766263c482","transactorInfo":{"psnId":"77248be00a0a43268008b8f9241c236a"}},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType":1,"signTipsContent":"1、你的辛苦毫无价值；\\n2、你的加班无人认可；\\n3、你的状态心力交瘁。\\n你一切的努力只是上位者的垫脚石。","signTipsTitle":"牛马声明"},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":0,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":2},{"noticeConfig":{"noticeTypes":""},"orgSignerInfo":{"orgId":"39178d0903174cf8bd124b766263c482","transactorInfo":{"psnId":"79bc03b0cc5b4417a7ca06bcd00fa822"}},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType":1,"signTipsContent":"1、你的辛苦毫无价值；\\n2、你的加班无人认可；\\n3、你的状态心力交瘁。\\n你一切的努力只是上位者的垫脚石。","signTipsTitle":"牛马声明"},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"0,1","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldSize":0,"signFieldStyle":1},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":20,"showSignDate":2,"signDatePositionPage":1,"signDatePositionX":200,"signDatePositionY":200},"signFieldType":0}],"signerType":2}]}'`,
    data: {
      signFlowId: "data.signFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{signFlowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: 'data.shortUrl',
    }
  }
]

const t7_1 = [
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v2/signflows/{signFlowId}/detail' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
      approvalFlowId: "data.signDocs[0].signfields[0].approvalFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v1/approvalflows/{approvalFlowId}/approvalUrl?queryAccountId=79bc03b0cc5b4417a7ca06bcd00fa822' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
]

const t7_2 = [
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v2/signflows/{signFlowId}/detail' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
      approvalFlowId: "data.signDocs[0].signfields[0].approvalFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v1/approvalflows/{approvalFlowId}/approvalUrl?queryAccountId=79bc03b0cc5b4417a7ca06bcd00fa822' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
]

const t8 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署详情页个人及企业有序签","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"identificationConfig":{"willTypes":["SIGN_PWD"]},"notifyConfig":{"examineNotice":false,"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"77248be00a0a43268008b8f9241c236a","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":0,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"},{"forceReadTime":0,"platformSign":false,"signOrder":2,"signerAccount":{"authorizedAccountId":"41b9682a30de4ad19a2c82f973e65155","noticeType":"2","signerAccountId":"79bc03b0cc5b4417a7ca06bcd00fa822"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1-2","posX":400,"posY":600},"sealId":"","sealType":"","signDateBeanType":"1","signType":2,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.shortUrl"
    }
  }
]

const t8_1 = [
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=79bc03b0cc5b4417a7ca06bcd00fa822&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.shortUrl"
    }
  }
]

const t9 = [
    {
        curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file' \
        --header 'X-Tsign-Open-App-Id: **********' \
        --header 'X-Tsign-Service-Group: DEFAULT' \
        --header 'X-Tsign-Open-Auth-Mode: Secret' \
        --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
        --header 'Content-Type: application/json' \
        --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"signFlowConfig":{"autoFinish":true,"autoStart":true,"chargeConfig":{"chargeMode":0},"noticeConfig":{"noticeTypes":"1,2"},"notifyUrl":"https://libaohui.com.cn/callback/ding","redirectConfig":{"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com"},"signConfig":{"availableSignClientTypes":"1","showBatchDropSealButton":false},"signFlowTitle":"3.0发起选填备注签署区PC端"},"signers":[{"noticeConfig":{"noticeTypes":""},"psnSignerInfo":{"psnId":"77248be00a0a43268008b8f9241c236a"},"signConfig":{"forcedReadingTime":0,"signOrder":1,"signTaskType":0,"signTipsContent":"","signTipsTitle":""},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","mustSign":false,"remarkSignFieldConfig":{"aiCheck":1,"freeMode":false,"inputType":2,"movableSignField":false,"remarkContent":"牛马声明","remarkFontSize":20,"signFieldHeight":200,"signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":200,"positionY":200},"signFieldWidth":200},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":50,"showSignDate":1,"signDatePositionX":100,"signDatePositionY":100},"signFieldType":1},{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","mustSign":false,"remarkSignFieldConfig":{"aiCheck":0,"freeMode":false,"inputType":2,"movableSignField":false,"remarkContent":"牛马声明","remarkFontSize":20,"signFieldHeight":200,"signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":400,"positionY":200},"signFieldWidth":200},"signDateConfig":{"dateFormat":"yyyy-MM-dd","fontSize":50,"showSignDate":1,"signDatePositionX":100,"signDatePositionY":100},"signFieldType":1}],"signerType":0}]}'`,
        data: {
            signFlowId: "data.signFlowId"
        }
    },
    {
        curl: `curl --location --globoff --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{signFlowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
        data: {
            url: "data.shortUrl"
        }
    }
];

const t10 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v2/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: f056017bab1550d5294a1b300587ae70' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"悟空非实名非意愿签署","contractRemind":1,"flowConfigInfo":{"countdown":0,"examineNotice":false,"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1,2","redirectDelayTime":3,"redirectUrl":"https://www.baidu.com.cn","signPlatform":"1,2,3,4","willTypes":["SIGN_PWD"]},"flowConfigItemBean":{"buttonConfig":{"propButton":"1,2,6,7,11"}}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"19deac73f6864123b007e92519baa872","noticeType":"1,2","signerAccountId":"19deac73f6864123b007e92519baa872"},"signfields":[{"actorIndentityType":0,"assignedPosbean":true,"autoExecute":false,"fieldType":0,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"1","signType":1,"width":0}],"thirdOrderNo":"11111"},{"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"8eed6e80cd6a4f999f0b409e82c10a5c","noticeType":"1,2","signerAccountId":"19deac73f6864123b007e92519baa872"},"signfields":[{"actorIndentityType":2,"assignedPosbean":true,"autoExecute":false,"fieldType":0,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":400,"posY":200},"sealId":"","sealType":"1","signType":1,"width":0}],"thirdOrderNo":"11111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v2/signflows/{flowId}/executeUrl?accountId=19deac73f6864123b007e92519baa872&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: f056017bab1550d5294a1b300587ae70' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.executeUrl.shortUrl"
    }
  }
]

const t11 = [
    {
      curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
      --header 'X-Tsign-Open-App-Id: **********' \
      --header 'X-Tsign-Service-Group: DEFAULT' \
      --header 'X-Tsign-Open-Auth-Mode: Secret' \
      --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
      --header 'Content-Type: application/json' \
      --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署详情页","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":3,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"77248be00a0a43268008b8f9241c236a","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":0,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
      data: {
          flowId: "data.flowId"
      }
    },
    {
      curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/batch-sign-url' \
      --header 'X-Tsign-Open-App-Id: **********' \
      --header 'X-Tsign-Service-Group: DEFAULT' \
      --header 'X-Tsign-Open-Auth-Mode: Secret' \
      --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
      --header 'Content-Type: application/json' \
      --data '{"clientType":"ALL","forcedRead":false,"operatorId":"77248be00a0a43268008b8f9241c236a","redirectUrl":"https://www.baidu.com","signFlowIds":["{flowId}"]}'`,
      data: {
          url: "data.batchSignShortUrlWithoutLogin"
      }
    }
];

const t12 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"28f43e07a8b140a1a0e206540328847c"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"企业批量签PC端","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"39178d0903174cf8bd124b766263c482","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"28f43e07a8b140a1a0e206540328847c","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/batch-sign-url' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"clientType":"ALL","forcedRead":false,"operatorId":"77248be00a0a43268008b8f9241c236a","redirectUrl":"https://www.baidu.com","signFlowIds":["{flowId}"]}'`,
    data: {
        url: "data.batchSignShortUrlWithoutLogin"
    }
  },
  {
    curl: `curl --location --request POST 'http://cert-service.testk8s.tsign.cn/cloudCertDelete/model' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"oid":"39178d0903174cf8bd124b766263c482"}'`,
    data: {}
  }
]

const t13 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署详情页个人及企业签（无需审批）","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"77248be00a0a43268008b8f9241c236a","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":0,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"},{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"39178d0903174cf8bd124b766263c482","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":400,"posY":200},"sealId":"","sealType":"","signDateBeanType":"1","signType":2,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/batch-sign-url' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"clientType":"ALL","forcedRead":false,"operatorId":"77248be00a0a43268008b8f9241c236a","redirectUrl":"https://www.baidu.com","signFlowIds":["{flowId}"]}'`,
    data: {
        url: "data.batchSignShortUrlWithoutLogin"
    }
  }
]

const t14 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署走审批","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"39178d0903174cf8bd124b766263c482","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"faa68ff796c44e30a17ea87a6343dbe2","posBean":{"posPage":"1","posX":400,"posY":200},"sealId":"","sealType":"","signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.shortUrl"
    }
  },
  {
    curl: `curl --location --request POST 'http://cert-service.testk8s.tsign.cn/cloudCertDelete/model' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"oid":"39178d0903174cf8bd124b766263c482"}'`,
    data: {}
  }
]

const t14_1 = [
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v2/signflows/{flowId}/detail' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
      approvalFlowId: "data.signDocs[0].signfields[0].approvalFlowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v1/approvalflows/{approvalFlowId}/approvalUrl?queryAccountId=79bc03b0cc5b4417a7ca06bcd00fa822' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --data ''`,
    data: {
      url: "data.shortUrl"
    }
  }
]

const t15 = [
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/api/v3/signflows/createFlowOneStep' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"28f43e07a8b140a1a0e206540328847c"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"企业批量签PC端","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,16"},"notifyConfig":{"noticeDeveloperUrl":"https://libaohui.com.cn/callback/ding","noticeType":"1","sendNotice":true},"signConfig":{"archiveLock":false,"batchDropSeal":true,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"https://www.baidu.com","showQrCode":false,"signPlatform":"1,2,3","signQrSwitch":false}},"initiatorAccountId":"77248be00a0a43268008b8f9241c236a","initiatorAuthorizedAccountId":"77248be00a0a43268008b8f9241c236a"},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"39178d0903174cf8bd124b766263c482","noticeType":"2","signerAccountId":"77248be00a0a43268008b8f9241c236a"},"signfields":[{"actorIndentityType":2,"assignedPosbean":false,"autoExecute":false,"fileId":"28f43e07a8b140a1a0e206540328847c","posBean":{"posPage":"1","posX":200,"posY":200},"sealId":"","sealType":"","signDateBean":{"fontSize":20,"posX":200,"posY":200},"signDateBeanType":"1","signType":1,"width":0}],"thirdOrderNo":"111"}]}'`,
    data: {
      flowId: "data.flowId"
    }
  },
  {
    curl: `curl --location --request GET 'http://in-testopenapi.tsign.cn/v3/signflows/{flowId}/executeUrl?accountId=77248be00a0a43268008b8f9241c236a&clientType=ALL&urlType=0' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data ''`,
    data: {
        url: "data.shortUrl"
    }
  },
  {
    curl: `curl --location --request POST 'http://cert-service.testk8s.tsign.cn/cloudCertDelete/model' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"oid":"39178d0903174cf8bd124b766263c482"}'`,
    data: {}
  }
]

const t16 = [
  {
    curl: `curl --location --request POST 'https://testmanage.esign.cn/manage-account/account/organAccount' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw '{"activate":1,"createTime":"","deleted":false,"name":"esigntestui自动化企业实名专用","orgRealNameStatus":true,"pageIndex":0,"pageSize":20,"realNameStatus":"ACCEPT","sorts":[{"sortKey":"createTime","sortType":"desc"}]}'`,
    data: {
      orgOuid: 'data.items[0].ouid'
    },
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/organizations/{orgOuid}' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'X-Tsign-Open-App-Id: **********' \
    -H 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    -H 'X-Tsign-Open-Auth-Mode: Secret' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest'`,
  },
  {
    curl: `curl --location --request POST 'https://testmanage.esign.cn/manage-account/account/personalAccount' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw '{"activate":"","createTime":"","deleted":false,"mobile":"***********","orgRealNameStatus":"","pageIndex":0,"pageSize":20,"realNameStatus":"ACCEPT","sorts":[{"sortKey":"data.base.createTime","sortType":"desc"}]}'`,
    data: {
      personOid: 'data.items[0].ouid'
    },
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/accounts/{personOid}' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'X-Tsign-Open-App-Id: **********' \
    -H 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    -H 'X-Tsign-Open-Auth-Mode: Secret' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest'`,
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"signFlowConfig":{"authConfig":{"orgAvailableAuthModes":["ORG_BANK_TRANSFER"],"psnAvailableAuthModes":["PSN_BANKCARD4"],"willingnessAuthModes":["CODE_SMS"]},"autoFinish":true,"autoStart":true,"notifyUrl":"https://libaohui.com.cn/callback/ding","redirectConfig":{"redirectDelayTime":"1","redirectUrl":"https://www.baidu.com"},"signConfig":{"availableSignClientTypes":"1,2,3","showBatchDropSealButton":true,"signMode":"NORMAL"},"signFlowTitle":"个人及企业实名"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"***********","psnInfo":{"bankCardNum":"6231980192722575283","psnIDCardNum":"******************","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnName":"测试印勇安"}},"signFields":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2","normalSignFieldConfig":{"psnSealStyles":"1","signFieldPosition":{"positionPage":"1","positionX":200,"positionY":600},"signFieldSize":0,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgInfo":{"legalRepIDCardNum":"******************","legalRepIDCardType":"CRED_PSN_CH_IDCARD","legalRepName":"测试印勇安","orgIDCardNum":"9100000089283687LA","orgIDCardType":"CRED_ORG_USCC"},"orgName":"esigntestui自动化企业实名专用","transactorInfo":{"psnAccount":"***********","psnInfo":{"bankCardNum":"6231980192722575283","psnIDCardNum":"******************","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnName":"测试印勇安"}}},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","mustSign":true,"normalSignFieldConfig":{"psnSealStyles":"1","signFieldPosition":{"positionPage":"1","positionX":200,"positionY":400},"signFieldSize":0,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}'`,
    data: {
        signFlowId: "data.signFlowId"
    }
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/{signFlowId}/sign-url' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"appScheme":"","clientType":"","needLogin":false,"operator":{"psnAccount":"***********"},"urlType":0}'`,
    data: {
        url: "data.shortUrl"
    }
  },
]

const t16H5 = [
  {
    curl: `curl --location --request POST 'https://testmanage.esign.cn/manage-account/account/organAccount' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw '{"activate":1,"createTime":"","deleted":false,"name":"esigntestui自动化企业H5实名专用","orgRealNameStatus":true,"pageIndex":0,"pageSize":20,"realNameStatus":"ACCEPT","sorts":[{"sortKey":"createTime","sortType":"desc"}]}'`,
    data: {
      orgOuid: 'data.items[0].ouid'
    },
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/organizations/{orgOuid}' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'X-Tsign-Open-App-Id: **********' \
    -H 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    -H 'X-Tsign-Open-Auth-Mode: Secret' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest'`,
  },
  {
    curl: `curl --location --request POST 'https://testmanage.esign.cn/manage-account/account/personalAccount' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw '{"activate":"","createTime":"","deleted":false,"mobile":"***********","orgRealNameStatus":"","pageIndex":0,"pageSize":20,"realNameStatus":"ACCEPT","sorts":[{"sortKey":"data.base.createTime","sortType":"desc"}]}'`,
    data: {
      personOid: 'data.items[0].ouid'
    },
  },
  {
    curl: `curl --location --request DELETE 'http://in-testopenapi.tsign.cn/v1/accounts/{personOid}' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'X-Tsign-Open-App-Id: **********' \
    -H 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    -H 'X-Tsign-Open-Auth-Mode: Secret' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest'`,
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/create-by-file' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"docs":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2"}],"signFlowConfig":{"authConfig":{"orgAvailableAuthModes":["ORG_BANK_TRANSFER"],"psnAvailableAuthModes":["PSN_BANKCARD4"],"willingnessAuthModes":["CODE_SMS"]},"autoFinish":true,"autoStart":true,"notifyUrl":"https://libaohui.com.cn/callback/ding","redirectConfig":{"redirectDelayTime":"1","redirectUrl":"https://www.baidu.com"},"signConfig":{"availableSignClientTypes":"1,2,3","showBatchDropSealButton":true,"signMode":"NORMAL"},"signFlowTitle":"个人及企业实名H5专用"},"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"psnSignerInfo":{"psnAccount":"***********","psnInfo":{"bankCardNum":"****************","psnIDCardNum":"13022317510207537X","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnName":"测试居朋亮"}},"signFields":[{"fileId":"faa68ff796c44e30a17ea87a6343dbe2","normalSignFieldConfig":{"psnSealStyles":"1","signFieldPosition":{"positionPage":"1","positionX":200,"positionY":600},"signFieldSize":0,"signFieldStyle":1},"signFieldType":0}],"signerType":0},{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgInfo":{"legalRepIDCardNum":"13022317510207537X","legalRepIDCardType":"CRED_PSN_CH_IDCARD","legalRepName":"测试居朋亮","orgIDCardNum":"91000000359274826F","orgIDCardType":"CRED_ORG_USCC"},"orgName":"esigntestui自动化企业H5实名专用","transactorInfo":{"psnAccount":"***********","psnInfo":{"bankCardNum":"****************","psnIDCardNum":"13022317510207537X","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnName":"测试居朋亮"}}},"signFields":[{"customBizNum":"","fileId":"faa68ff796c44e30a17ea87a6343dbe2","mustSign":true,"normalSignFieldConfig":{"psnSealStyles":"1","signFieldPosition":{"positionPage":"1","positionX":200,"positionY":400},"signFieldSize":0,"signFieldStyle":1},"signFieldType":0}],"signerType":1}]}'`,
    data: {
        signFlowId: "data.signFlowId"
    }
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v3/sign-flow/{signFlowId}/sign-url' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Service-Group: DEFAULT' \
    --header 'X-Tsign-Open-Auth-Mode: Secret' \
    --header 'X-Tsign-Open-App-Secret: ab6141d0c3a5c2f3965331fa97945ab9' \
    --header 'Content-Type: application/json' \
    --data '{"appScheme":"","clientType":"","needLogin":false,"operator":{"psnAccount":"***********"},"urlType":0}'`,
    data: {
        url: "data.shortUrl"
    }
  },
]

const t3_1 = [
  {
    curl: `curl --location --request POST 'https://testmanage.esign.cn/manage-account/account/personalAccount' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw '{"sorts":[{"sortType":"desc","sortKey":"data.base.createTime"}],"deleted":false,"pageIndex":0,"pageSize":20,"orgRealNameStatus":"","realNameStatus":"","activate":"","createTime":"","mobile":"***********"}'`,
    data: {
      personOid: 'data.items[0].ouid'
    },
  },
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v2/identity/auth/web/{personOid}/indivIdentityUrl' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H 'Cache-Control: no-cache' \
    -H 'Connection: keep-alive' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'X-Tsign-Open-App-Id: **********' \
    -H 'X-Tsign-Open-App-Secret: 8ff718c007aecd156ca0ea78127e6016' \
    -H 'X-Tsign-Open-Auth-Mode: Secret' \
    -b 'access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; redirect_referer=aHR0cDovL3Rlc3RzdXBwb3J0LnRzaWduLmNuL21pY3JvZmUvdXNlcmNlbnRlci9zZWFsUncvbGlzdA==; test_access_token=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
    -H 'Origin: https://testsupport.tsign.cn' \
    -H 'Pragma: no-cache' \
    -H 'Referer: https://testsupport.tsign.cn/microfe/usercenter/list' \
    -H 'Sec-Fetch-Dest: empty' \
    -H 'Sec-Fetch-Mode: cors' \
    -H 'Sec-Fetch-Site: cross-site' \
    -H 'Sec-Fetch-Storage-Access: active' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'x-requested-with: XMLHttpRequest' \
    --data-raw='{"dataCollect":{"clientType":"WEB","bizSource":"saas_proactive_create"},"contextInfo":{"showResultPage":false,"redirectUrl":"https://testfront.tsign.cn:8887/relaNameResult"}}'`,
    data: {
      url: "data.shortLink"
    }
  },
]

const x = [
  {
    curl: `curl --location --request POST 'http://footstone-user-api.testk8s.tsign.cn/v2/accounts/createOverall' \
    --header 'Content-Type: application/json' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Open-Auth-Mode: simple' \
    --data '{
      "credentials": {
        "CRED_PSN_CH_IDCARD": "******************"
      },
      "properties": {
        "name": "测试自动化"
      },
        "idcards":{"mobile":"***********", "password": "16d7a4fca7442dda3ad93c9a726597e4"}
    }'`
  },
]

const a = {}

request("", {}, x, a).then(() => {
  console.log(123123, a)
});
