var fs = require('fs');
const { TEST_RUN_ERRORS, RUNTIME_ERRORS } = require('./errorcode.js'); // 确保路径正确

const moment = require('moment');
const { start } = require('repl');
function convertLocalPathToUrl(localPath, baseUrl = '../screenshots') {
  // 提取本地路径的前缀部分（直到 "screenshots" 目录）
  try {
    const localPrefixMatch = localPath.match(/^(.*?screenshots)[\\/]/i);
    if (!localPrefixMatch) {
      throw new Error("Invalid local path: 'screenshots' directory not found.");
    }
  
    const localPrefix = localPrefixMatch[1];
    
    // 替换本地路径前缀为 URL 前缀
    let urlPath = localPath.replace(
      new RegExp(`^${localPrefix.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'i'),
      baseUrl
    );
    
    // 将反斜杠替换为正斜杠
    urlPath = urlPath.replace(/\\/g, '/');
    return urlPath;
  }catch(e){
    console.log('转换路径失败', e)
    return '';
  }

}
module.exports = function () {
  return {
    noColors: true,
    reportTempData: {
      startTime: null,
      uaList: '',
      testCount: 0,
      passed: 0,
      skipped: 0,
      duration: 0,
      fixtures: [],
      currentFixture: [],
      errors: []
    },

    reportTaskStart(startTime, userAgents, testCount) {
      this.reportTempData.startTime = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
      this.reportTempData.uaList = userAgents.join(', ');
      this.reportTempData.testCount = testCount;
    },

    reportFixtureStart(name) {
    },

    reportTestDone(name, testRunInfo) {
      console.log('测试结果', testRunInfo)
      // 收集quarantine中的所有errors
      const quarantineErrors = [];
      try {
        if (testRunInfo.quarantine) {
          Object.values(testRunInfo.quarantine).forEach((item,index) => {
            if (item.errors && Array.isArray(item.errors)&& item.errors.length > 0) {
              item.errors[0].screenshotPath = convertLocalPathToUrl(item.errors[0].screenshotPath)
              item.errors[0].errorText = item.errors.map((err, idx) => {
                const errString = this.formatError(err, `${idx + 1}) `)
                console.log('错误信息', errString)
                let errText = errString.split('\n');
                errText = errText.map(line => line.trim()).filter(line => line.trim() !== '');
                return errText;
              });
              quarantineErrors.push(...item.errors);
            }
          });
        }
      } catch (error) {
        console.error('Error parsing quarantine errors:', error);
      }

      
      const errorCode = testRunInfo.errs[0]?.code;

      // const hasErr = !!testRunInfo.errs.length;
      // let errText = []
      // if (hasErr) {
      //   testRunInfo.errs.forEach((err, idx) => {
      //     const errString = this.formatError(err, `${idx + 1}) `)
      //     errText = errString.split('\n');
      //     errText = errText.map(line => line.trim()).filter(line => line.trim() !== '');
      //   });
      // }
      let screenshotbase64 = convertLocalPathToUrl(testRunInfo.screenshots?.[0]?.screenshotPath);
      let tempData = []
      if (testRunInfo.reportData) {
        tempData = Object.values(testRunInfo.reportData)
      }
      console.log('测试结果输出')

      const testResult = {
        id: testRunInfo.testId,
        name: name,
        startTime: Date.now() - testRunInfo.durationMs,
        status: testRunInfo.skipped ? 'skipped' : testRunInfo.errs.length ? 'failed' : tempData[0].length === 0 ? 'passed' : 'warning',
        duration: Math.floor(testRunInfo.durationMs / 1000),
        screenshot: screenshotbase64,
        errorApiFnChain: testRunInfo.errs[0]?.apiFnChain || testRunInfo.callsite,
        errorDetail: '',
        errorCode: errorCode,
        assertDetail: tempData && tempData.length ? tempData[0] : [],
        errorType: errorCode ? this._getErrorType(errorCode) : '', // 新增错误类型分类
        quarantineErrors: quarantineErrors, // 新增quarantineErrors
      };

      this.reportTempData.currentFixture.push(testResult);

      if (testResult.status === 'failed' || tempData.length) {
        this.reportTempData.errors.push({
          testId: testResult.id,
          message: testResult.error,
          screenshot: testResult.screenshot
        });
      }
    },

    reportTaskDone(endTime, passed, warnings, result) {
      this.reportTempData.passed = passed;
      this.reportTempData.skipped = this.reportTempData.testCount - passed - this.reportTempData.errors.length;
      this.reportTempData.duration = moment.duration(moment(endTime).diff(this.reportTempData.startTime)).asSeconds();

      const template = this._generateVueReport();
      this.write(template);
    },

    _getErrorType(errorCode) {
      const errorCategoryMap = {
        'E1-E67': '测试执行错误',
        'E68-E84': '窗口操作错误',
        'E85-E102': '参数验证错误',
        'E1000-E1062': '系统运行时错误'
      };

      const codeNumber = parseInt(errorCode.replace('E', ''));
      if (codeNumber <= 67) return errorCategoryMap['E1-E67'];
      if (codeNumber <= 84) return errorCategoryMap['E68-E84'];
      if (codeNumber <= 102) return errorCategoryMap['E85-E102'];
      return errorCategoryMap['E1000-E1062'];
    },

    _generateVueReport() {
      const testCases = this.reportTempData.currentFixture;
      // 转换错误码结构为 { E1: { code: 'E1', message: '...' }, ... }
      const errorCodeMap = Object.assign(
        ...Object.values(TEST_RUN_ERRORS).map(e => ({ [e.code]: e })),
        ...Object.values(RUNTIME_ERRORS).map(e => ({ [e.code]: e }))
      );

      const template = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>E签宝UI测试报告</title>
        <link rel="stylesheet" href="../reporter/element.css">
        <link rel="stylesheet" href="../reporter/index.css">
        <link rel="stylesheet" href="../reporter/all.min.css">
        <script src="../reporter/vue.js"></script>
        <script src="../reporter/element.js"></script>
        <script src="../reporter/echarts.min.js"></script>

      </head>
      <body>
        <img src="../reporter/logo_mini.png" width="100" height="28" style="margin: 8px;">
        <div id="app" class="report-container">
          <h1 class="header-title">
            <i class="el-icon-s-data"></i>
            测试报告详情
          </h1>
      
          <!-- 统计卡片 -->
<el-card class="stat-card">
<el-row :gutter="20">
    <!-- 原有统计列保持不变 -->
    <el-col :span="4">
      <el-statistic title="总耗时" :value="stats.totalDuration" suffix="秒">
        <i slot="prefix" class="el-icon-timer stat-icon"></i>
      </el-statistic>
    </el-col>
    <el-col :span="4">
      <el-statistic title="总用例数" :value="${this.reportTempData.testCount}">
        <i slot="prefix" class="el-icon-document stat-icon"></i>
      </el-statistic>
    </el-col>
    <el-col :span="4">
      <el-statistic title="通过率" :value="stats.passedRate" suffix="%">
        <i slot="prefix" class="el-icon-top-right stat-icon" style="color:var(--success-color)"></i>
      </el-statistic>
    </el-col>
    <el-col :span="4">
      <el-statistic title="平均耗时" :value="stats.averageDuration" suffix="秒">
        <i slot="prefix" class="el-icon-data-line stat-icon"></i>
      </el-statistic>
    </el-col>
    <el-col :span="4">
      <el-statistic title="最长耗时" :value="stats.maxDuration.value" suffix="秒">
        <i slot="prefix" class="el-icon-alarm-clock stat-icon"></i>
      </el-statistic>
    </el-col>
  </el-row>
  <!-- 详细数据列表 -->
  <div style="margin-top:25px; border-top:1px solid #eee; padding-top:15px;">
    <el-row :gutter="15">
      <el-col :span="12">
        <div class="stat-item">
          <i class="el-icon-document-delete"></i>
          <span class="stat-label">失败用例：</span>
          <span class="stat-value">{{ stats.totalCases - passedCases }}</span>
        </div>
        <div class="stat-item">
          <i class="el-icon-document-checked"></i>
          <span class="stat-label">通过用例：</span>
          <span class="stat-value">{{ passedCases }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="stat-item">
          <i class="el-icon-edit"></i>
          <span class="stat-label">用例名称：</span>
          <el-tag 
            :type="stats.passedRate >= 90 ? 'success' : 'warning'" 
            size="mini">
             {{getUrlParams('caseName')}}
          </el-tag> 
        </div>
        <div class="stat-item">
          <i class="el-icon-folder-add"></i>
          <span class="stat-label">项目名称：</span>
          <el-tag type="primary" size="mini">
          <span class="stat-value"> {{getUrlParams('groupName')}} </span>
          </el-tag>
        </div>
      </el-col>
    </el-row>
  </div>
</el-card>
      <!-- 用例表格 -->
          <el-table 
            :data="testCases" 
            stripe 
            style="width: 100%;"
            :row-class-name="tableRowClassName"
            ref="table"
            @row-click="toggleExpand"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <div class="error-detail">
                  <h3 style="margin-bottom: 15px;">
                    <i class="el-icon-warning-outline"></i>
                    错误详情分析
                  </h3>

                  
                  <!-- 隔离错误列表 -->
                  <div v-if="props.row.quarantineErrors && props.row.quarantineErrors.length" style="margin-top: 20px;">
                    <h3><i class="el-icon-warning"></i>错误详情</h3>
                    <div v-for="(error, idx) in props.row.quarantineErrors" :key="idx" style="margin-bottom: 20px;">
                      <el-alert 
                        v-if="errorCodeMap[error.code]"
                        :title="\`第 \${idx+1} 次执行 错误码 \${error.code}: \${errorCodeMap[error.code].message}\`"
                        type="error"
                        :closable="false"
                        show-icon>
                        <div style="margin-top: 10px;">
                          {{ error.errMsg }}
                        </div>
                      </el-alert>
                    <div class="error-stack" v-if="error.errorText">
                    <div v-for="(line, index) in error.errorText[0]" 
                         :key="index"
                         :style="{ color: line.startsWith('>') ? '#ff6666' : '#999999' }">
                      {{ line }}
                    </div>
                    </div>
                    <div v-if="error.screenshotPath" style="margin-top: 10px;">
                      <el-image 
                        :src="error.screenshotPath" 
                        style="margin: 15px; border-radius: 6px;"
                        fit="cover"
                        lazy>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                    </div>
                    </div>
                  </div>
              </template>
            </el-table-column>
      
            <el-table-column prop="id" label="ID" width="120" align="center"></el-table-column>
            <el-table-column prop="name" label="测试用例" align="center">
              <template slot-scope="{row}">
                {{ row.name }}
              </template>
            </el-table-column>
             <el-table-column label="开始时间" width="160" align="center">
              <template slot-scope="{row}">
                <div style="display: flex; align-items: center;">
                  <span>{{ new Date(row.startTime).toLocaleTimeString() }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="150" align="center">
              <template slot-scope="{row}">
                <el-tag 
                  :type="statusType(row.status)" 
                  size="mini"
                  class="status-tag">
                  <i :class="statusIcon(row.status)" style="margin-right: 2px;"></i>
                  {{ row.status.toUpperCase() }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="耗时（秒）" width="120" align="center"></el-table-column>
            <el-table-column label="截图" width="130" align="center">
              <template slot-scope="{row}">
                <el-image 
                  v-if="row.screenshot"
                  class="screenshot-thumb"
                  :src="row.screenshot"
                  :preview-src-list="[row.screenshot]">
                </el-image>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="错误类型"width="180">
              <template slot-scope="{row}">
                <el-tag v-if="row.errorType" >
                  {{ row.errorType }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>

      
          
        </div>
      
        <script>
        const errorCodeMap = ${JSON.stringify(errorCodeMap)};
        new Vue({
          el: '#app',
          data: () => ({
            testCases: ${JSON.stringify(testCases)},
            errorCodeMap: errorCodeMap,
            currentScreenshot: '',
            dialogVisible: false
          }), 
            computed: {
              passedCases() {
                    return this.testCases.filter(tc => tc.status === 'passed').length;
                },
            stats() {
                const totalCases = this.testCases.length;

                // 强制转换 duration 为数值，非数字时视为 0
                const totalDuration = this.testCases.reduce((sum, tc) => {
                const duration = Number(tc.duration) || 0; // 关键点：确保是数字
                return sum + duration;
                }, 0);

                // 查找最大 duration（跳过无效值）
                const maxDuration = this.testCases.reduce((max, tc) => {
                const duration = Number(tc.duration) || 0; // 关键点：确保是数字
                return duration > max.value ? 
                    { value: duration, caseId: tc.id, name: tc.name } : max;
                }, { value: 0, caseId: '', name: '' });

                // 计算 passedRate 和 averageDuration（处理除零）
                const passedCases = this.testCases.filter(tc => tc.status === 'passed').length;
                const passedRate = totalCases > 0 
                ? (passedCases / totalCases * 100) 
                : 0; // 始终返回数值
                const averageDuration = totalCases > 0 
                ? (totalDuration / totalCases) 
                : 0; // 始终返回数值
                // 返回结果（强制保留一位小数）
                return {
                totalDuration: totalDuration,
                totalCases: totalCases,
                passedCases:passedCases,
                passedRate: Number(passedRate.toFixed(1)), // 直接调用 toFixed
                averageDuration: Number(averageDuration.toFixed(1)),
                maxDuration: maxDuration
                };
            }
            },
          mounted() {
              console.log('mounted');
              console.log(this.testCases);
          },
          methods: {
            getUrlParams(query) {
                const params = Object.fromEntries(new URLSearchParams(window.location.search));
                return params[query]
            },
            initPieChart() {
              const chart = echarts.init(document.getElementById('chartPie'));
              const statusStats = this.testCases.reduce((acc, tc) => {
                acc[tc.status] = (acc[tc.status] || 0) + 1;
                return acc;
              }, {});

              const option = {
                tooltip: { trigger: 'item' },
                legend: { top: 'bottom' },
                series: [{
                  type: 'pie',
                  radius: ['40%', '70%'],
                  color: [
                    '#67C23A', // 通过颜色
                    '#F56C6C', // 失败颜色
                    '#E6A23C',  // 跳过颜色
                    '#409EFF',  // 跳过颜色
                  ],
                  data: [
                    { value: statusStats.passed || 0, name: '通过' },
                    { value: statusStats.failed || 0, name: '失败' },
                    { value: statusStats.skipped || 0, name: '跳过' },
                    { value: statusStats.warning || 0, name: '待排查' }
                  ],
                  label: {
                    formatter: '{b}: {d}%',
                    fontSize: 14
                  },
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }]
              };
              chart.setOption(option);
              
              // 窗口变化自适应
              window.addEventListener('resize', () => chart.resize());
            },

            // 初始化柱状图
            initBarChart() {
              const chart = echarts.init(document.getElementById('chartBar'));
              
              // 统计错误类型
              const errorStats = this.testCases
                .filter(tc => tc.errorType)
                .reduce((acc, tc) => {
                  acc[tc.errorType] = (acc[tc.errorType] || 0) + 1;
                  return acc;
                }, {});

              // 转换为数组并排序
              const errorData = Object.entries(errorStats)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10);

              const option = {
                tooltip: { trigger: 'axis' },
                xAxis: {
                  type: 'category',
                  data: errorData.map(d => d[0]),
                  axisLabel: { rotate: 45 }
                },
                yAxis: { type: 'value' },
                grid: { top: 30, bottom: 80 },
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#ff6b6b' }, 
                  { offset: 1, color: '#ff3838' }
                ]),
                series: [{
                  type: 'bar',
                  data: errorData.map(d => d[1]),
                  barWidth: '60%',
                  label: {
                    show: true,
                    position: 'top',
                    color: '#F56C6C'
                  },
                  itemStyle: {
                    borderRadius: [4, 4, 0, 0]
                  }
                }]
              };
              
              // 处理无数据情况
              if (errorData.length === 0) {
                option.graphic = {
                  type: 'text',
                  left: 'center',
                  top: 'middle',
                  style: {
                    text: '暂无错误数据',
                    fontSize: 16,
                    fill: '#999'
                  }
                };
              }

              chart.setOption(option);
              window.addEventListener('resize', () => chart.resize());
            },
            statusType(status) {
              return { passed: 'success', failed: 'danger', skipped: 'warning', warning:'primary' }[status] || 'info'
            },
            statusIcon(status) {
              return {
                passed: 'el-icon-check',
                failed: 'el-icon-close',
                skipped: 'el-icon-minus',
                warning: 'el-icon-minus'
              }[status]
            },
            tableRowClassName({row}) {
              return row.status === 'failed' ? 'warning-row' : ''
            },
            toggleExpand(row) {
              this.$refs.table.toggleRowExpansion(row)
            },
          }
        });
        </script>
      </body>
      </html>`;
      return template;
    }
  };
};