exports.TEST_RUN_ERRORS = {
    uncaughtErrorOnPage: { code: 'E1', message: '页面上未捕获的异常' },
    uncaughtErrorInTestCode: { code: 'E2', message: '测试代码中未捕获的异常' },
    uncaughtNonErrorObjectInTestCode: { code: 'E3', message: '测试代码中抛出了非Error对象' },
    uncaughtErrorInClientFunctionCode: { code: 'E4', message: '客户端函数代码中未捕获的异常' },
    uncaughtErrorInCustomDOMPropertyCode: { code: 'E5', message: '自定义DOM属性代码中未捕获的异常' },
    unhandledPromiseRejection: { code: 'E6', message: '未处理的Promise拒绝' },
    uncaughtException: { code: 'E7', message: '未捕获的全局异常' },
    missingAwaitError: { code: 'E8', message: '缺少await关键字的异步操作错误' },
    actionIntegerOptionError: { code: 'E9', message: '操作整数类型参数错误' },
    actionPositiveIntegerOptionError: { code: 'E10', message: '操作正整数类型参数错误' },
    actionBooleanOptionError: { code: 'E11', message: '操作布尔类型参数错误' },
    actionSpeedOptionError: { code: 'E12', message: '操作速度参数错误' },
    actionOptionsTypeError: { code: 'E14', message: '操作参数类型错误' },
    actionBooleanArgumentError: { code: 'E15', message: '操作布尔类型参数错误' },
    actionStringArgumentError: { code: 'E16', message: '操作字符串类型参数错误' },
    actionNullableStringArgumentError: { code: 'E17', message: '操作可空字符串参数错误' },
    actionStringOrStringArrayArgumentError: { code: 'E18', message: '操作字符串或字符串数组参数错误' },
    actionStringArrayElementError: { code: 'E19', message: '操作字符串数组元素错误' },
    actionIntegerArgumentError: { code: 'E20', message: '操作整数参数错误' },
    actionRoleArgumentError: { code: 'E21', message: '操作角色参数错误' },
    actionPositiveIntegerArgumentError: { code: 'E22', message: '操作正整数参数错误' },
    actionSelectorError: { code: 'E23', message: '操作选择器错误' },
    actionElementNotFoundError: { code: 'E24', message: '操作元素未找到' },
    actionElementIsInvisibleError: { code: 'E26', message: '操作元素不可见错误' },
    actionSelectorMatchesWrongNodeTypeError: { code: 'E27', message: '选择器匹配到错误的节点类型' },
    actionAdditionalElementNotFoundError: { code: 'E28', message: '附加元素未找到' },
    actionAdditionalElementIsInvisibleError: { code: 'E29', message: '附加元素不可见错误' },
    actionAdditionalSelectorMatchesWrongNodeTypeError: { code: 'E30', message: '附加选择器匹配到错误的节点类型' },
    actionElementNonEditableError: { code: 'E31', message: '操作元素不可编辑' },
    actionElementNotTextAreaError: { code: 'E32', message: '操作元素不是文本域错误' },
    actionElementNonContentEditableError: { code: 'E33', message: '操作元素非内容可编辑' },
    actionElementIsNotFileInputError: { code: 'E34', message: '操作元素不是文件上传控件错误' },
    actionRootContainerNotFoundError: { code: 'E35', message: '根容器未找到' },
    actionIncorrectKeysError: { code: 'E36', message: '操作键盘按键错误' },
    actionCannotFindFileToUploadError: { code: 'E37', message: '找不到要上传的文件错误' },
    actionUnsupportedDeviceTypeError: { code: 'E38', message: '不支持的设备类型错误' },
    actionIframeIsNotLoadedError: { code: 'E39', message: 'iframe未加载完成' },
    actionElementNotIframeError: { code: 'E40', message: '操作元素不是iframe' },
    actionInvalidScrollTargetError: { code: 'E41', message: '无效的滚动目标' },
    currentIframeIsNotLoadedError: { code: 'E42', message: '当前iframe未加载' },
    currentIframeNotFoundError: { code: 'E43', message: '当前iframe未找到' },
    currentIframeIsInvisibleError: { code: 'E44', message: '当前iframe不可见' },
    nativeDialogNotHandledError: { code: 'E45', message: '原生对话框未处理错误' },
    uncaughtErrorInNativeDialogHandler: { code: 'E46', message: '原生对话框处理器中的未捕获' },
    setTestSpeedArgumentError: { code: 'E47', message: '设置测试速度参数错误' },
    setNativeDialogHandlerCodeWrongTypeError: { code: 'E48', message: '原生对话框处理器代码类型' },
    clientFunctionExecutionInterruptionError: { code: 'E49', message: '客户端函数执行中断错误' },
    domNodeClientFunctionResultError: { code: 'E50', message: 'DOM节点客户端函数结果错误' },
    invalidSelectorResultError: { code: 'E51', message: '选择器结果无效错误' },
    cannotObtainInfoForElementSpecifiedBySelectorError: { code: 'E52', message: '无法获取选择器指定的元素信息错误' },
    externalAssertionLibraryError: { code: 'E53', message: '外部断言库错误' },
    pageLoadError: { code: 'E54', message: '页面加载错误' },
    windowDimensionsOverflowError: { code: 'E55', message: '窗口尺寸溢出错误' },
    forbiddenCharactersInScreenshotPathError: { code: 'E56', message: '截图路径包含非法字符错误' },
    invalidElementScreenshotDimensionsError: { code: 'E57', message: '元素截图尺寸无效错误' },
    roleSwitchInRoleInitializerError: { code: 'E58', message: '角色初始化中的角色切换错误' },
    assertionExecutableArgumentError: { code: 'E59', message: '断言可执行参数错误' },
    assertionWithoutMethodCallError: { code: 'E60', message: '未调用断言方法的错误' },
    assertionUnawaitedPromiseError: { code: 'E61', message: '断言未等待Promise的错误' },
    requestHookNotImplementedError: { code: 'E62', message: '请求钩子未实现错误' },
    requestHookUnhandledError: { code: 'E63', message: '请求钩子未处理错误' },
    uncaughtErrorInCustomClientScriptCode: { code: 'E64', message: '自定义客户端脚本未捕获错误' },
    uncaughtErrorInCustomClientScriptCodeLoadedFromModule: { code: 'E65', message: '从模块加载的自定义客户端脚本未捕获错误' },
    uncaughtErrorInCustomScript: { code: 'E66', message: '自定义脚本未捕获错误' },
    uncaughtTestCafeErrorInCustomScript: { code: 'E67', message: '自定义脚本中的TestCafe未捕获错误' },
    childWindowIsNotLoadedError: { code: 'E68', message: '子窗口未加载错误' },
    childWindowNotFoundError: { code: 'E69', message: '子窗口未找到' },
    cannotSwitchToWindowError: { code: 'E70', message: '无法切换到指定窗口错误' },
    closeChildWindowError: { code: 'E71', message: '关闭子窗口错误' },
    childWindowClosedBeforeSwitchingError: { code: 'E72', message: '切换前子窗口已关闭错误' },
    cannotCloseWindowWithChildrenError: { code: 'E73', message: '无法关闭包含子窗口的窗口错误' },
    targetWindowNotFoundError: { code: 'E74', message: '目标窗口未找到' },
    parentWindowNotFoundError: { code: 'E76', message: '父窗口未找到' },
    previousWindowNotFoundError: { code: 'E77', message: '前一个窗口未找到' },
    switchToWindowPredicateError: { code: 'E78', message: '切换窗口条件判断错误' },
    actionFunctionArgumentError: { code: 'E79', message: '操作函数参数错误' },
    multipleWindowsModeIsDisabledError: { code: 'E80', message: '多窗口模式已禁用错误' },
    multipleWindowsModeIsNotSupportedInRemoteBrowserError: { code: 'E81', message: '远程浏览器不支持多窗口模式错误' },
    cannotCloseWindowWithoutParent: { code: 'E82', message: '无法关闭无父窗口的窗口错误' },
    cannotRestoreChildWindowError: { code: 'E83', message: '无法恢复子窗口错误' },
    executionTimeoutExceeded: { code: 'E84', message: '执行超时错误' },
    actionRequiredCookieArguments: { code: 'E85', message: '操作缺少Cookie参数错误' },
    actionCookieArgumentError: { code: 'E86', message: '操作Cookie参数错误' },
    actionCookieArgumentsError: { code: 'E87', message: '操作Cookie参数集合错误' },
    actionUrlCookieArgumentError: { code: 'E88', message: '操作URL Cookie参数错误' },
    actionUrlsCookieArgumentError: { code: 'E89', message: '操作多个URL Cookie参数错误' },
    actionStringOptionError: { code: 'E90', message: '操作字符串选项错误' },
    actionDateOptionError: { code: 'E91', message: '操作日期选项错误' },
    actionNumberOptionError: { code: 'E92', message: '操作数字选项错误' },
    actionUrlOptionError: { code: 'E93', message: '操作URL选项错误' },
    actionUrlSearchParamsOptionError: { code: 'E94', message: '操作URL查询参数选项错误' },
    actionObjectOptionError: { code: 'E95', message: '操作对象选项错误' },
    actionUrlArgumentError: { code: 'E96', message: '操作URL参数错误' },
    actionStringOrRegexOptionError: { code: 'E97', message: '操作字符串或正则表达式选项错误' },
    actionSkipJsErrorsArgumentError: { code: 'E98', message: '跳过JS错误参数错误' },
    actionFunctionOptionError: { code: 'E99', message: '操作函数选项错误' },
    actionInvalidObjectPropertyError: { code: 'E100', message: '操作对象属性无效错误' },
    actionElementIsNotTargetError: { code: 'E101', message: '操作元素不是目标元素错误' },
    multipleWindowsModeIsNotSupportedInNativeAutomationError: { code: 'E102', message: '原生自动化不支持多窗口模式错误' }
};

exports.RUNTIME_ERRORS = {
    cannotCreateMultipleLiveModeRunners: { code: 'E1000', message: '无法创建多个实时模式运行器' },
    cannotRunLiveModeRunnerMultipleTimes: { code: 'E1001', message: '无法多次运行实时模式运行器' },
    browserDisconnected: { code: 'E1002', message: '浏览器连接已断开' },
    cannotRunAgainstDisconnectedBrowsers: { code: 'E1003', message: '无法在断开连接的浏览器上运行' },
    cannotEstablishBrowserConnection: { code: 'E1004', message: '无法建立浏览器连接' },
    cannotFindBrowser: { code: 'E1005', message: '找不到指定浏览器' },
    browserProviderNotFound: { code: 'E1006', message: '浏览器驱动未找到' },
    browserNotSet: { code: 'E1007', message: '浏览器未设置' },
    testFilesNotFound: { code: 'E1008', message: '未找到测试文件' },
    noTestsToRun: { code: 'E1009', message: '没有可运行的测试用例' },
    cannotFindReporterForAlias: { code: 'E1010', message: '找不到别名对应的报告器' },
    multipleSameStreamReporters: { code: 'E1011', message: '重复的流式报告器配置' },
    optionValueIsNotValidRegExp: { code: 'E1012', message: '选项值不是有效的正则表达式' },
    optionValueIsNotValidKeyValue: { code: 'E1013', message: '选项值不是有效的键值对' },
    invalidSpeedValue: { code: 'E1014', message: '无效的执行速度值' },
    invalidConcurrencyFactor: { code: 'E1015', message: '无效的并发因子' },
    cannotDivideRemotesCountByConcurrency: { code: 'E1016', message: '无法按并发数分配远程实例' },
    portsOptionRequiresTwoNumbers: { code: 'E1017', message: '端口选项需要两个数字参数' },
    portIsNotFree: { code: 'E1018', message: '端口已被占用' },
    invalidHostname: { code: 'E1019', message: '无效的主机名' },
    cannotFindSpecifiedTestSource: { code: 'E1020', message: '找不到指定的测试源' },
    clientFunctionCodeIsNotAFunction: { code: 'E1021', message: '客户端函数代码不是有效函数' },
    selectorInitializedWithWrongType: { code: 'E1022', message: '选择器初始化类型错误' },
    clientFunctionCannotResolveTestRun: { code: 'E1023', message: '客户端函数无法解析测试运行' },
    regeneratorInClientFunctionCode: { code: 'E1024', message: '客户端函数代码包含regenerator语法' },
    invalidClientFunctionTestRunBinding: { code: 'E1025', message: '客户端函数测试运行绑定无效' },
    invalidValueType: { code: 'E1026', message: '数值类型无效' },
    unsupportedUrlProtocol: { code: 'E1027', message: '不支持的URL协议' },
    testControllerProxyCannotResolveTestRun: { code: 'E1028', message: '测试控制器代理无法解析测试运行' },
    timeLimitedPromiseTimeoutExpired: { code: 'E1029', message: '限时Promise超时' },
    noTestsToRunDueFiltering: { code: 'E1030', message: '因过滤条件没有可运行测试' },
    cannotSetVideoOptionsWithoutBaseVideoPathSpecified: { code: 'E1031', message: '未指定基础视频路径无法设置视频选项' },
    multipleAPIMethodCallForbidden: { code: 'E1032', message: '禁止多次调用API方法' },
    invalidReporterOutput: { code: 'E1033', message: '无效的报告输出配置' },
    cannotReadSSLCertFile: { code: 'E1034', message: '无法读取SSL证书文件' },
    cannotPrepareTestsDueToError: { code: 'E1035', message: '因错误无法准备测试' },
    cannotParseRawFile: { code: 'E1036', message: '无法解析原始文件' },
    testedAppFailedWithError: { code: 'E1037', message: '被测应用程序运行失败' },
    unableToOpenBrowser: { code: 'E1038', message: '无法打开浏览器' },
    requestHookConfigureAPIError: { code: 'E1039', message: '请求钩子配置API错误' },
    forbiddenCharatersInScreenshotPath: { code: 'E1040', message: '截图路径包含禁用字符' },
    cannotFindFFMPEG: { code: 'E1041', message: '找不到FFMPEG程序' },
    compositeArgumentsError: { code: 'E1042', message: '复合参数错误' },
    cannotFindTypescriptConfigurationFile: { code: 'E1043', message: '找不到TypeScript配置文件' },
    clientScriptInitializerIsNotSpecified: { code: 'E1044', message: '未指定客户端脚本初始化器' },
    clientScriptBasePathIsNotSpecified: { code: 'E1045', message: '未指定客户端脚本基础路径' },
    clientScriptInitializerMultipleContentSources: { code: 'E1046', message: '客户端脚本初始化器多内容源错误' },
    cannotLoadClientScriptFromPath: { code: 'E1047', message: '无法从路径加载客户端脚本' },
    clientScriptModuleEntryPointPathCalculationError: { code: 'E1048', message: '客户端脚本模块入口路径计算错误' },
    methodIsNotAvailableForAnIPCHost: { code: 'E1049', message: 'IPC主机不支持该方法' },
    tooLargeIPCPayload: { code: 'E1050', message: 'IPC负载过大' },
    malformedIPCMessage: { code: 'E1051', message: 'IPC消息格式错误' },
    unexpectedIPCHeadPacket: { code: 'E1052', message: '意外的IPC头部数据包' },
    unexpectedIPCBodyPacket: { code: 'E1053', message: '意外的IPC主体数据包' },
    unexpectedIPCTailPacket: { code: 'E1054', message: '意外的IPC尾部数据包' },
    cannotRunLocalNonHeadlessBrowserWithoutDisplay: { code: 'E1057', message: '无显示环境无法运行本地非无头浏览器' },
    uncaughtErrorInReporter: { code: 'E1058', message: '报告器中未捕获的错误' },
    roleInitializedWithRelativeUrl: { code: 'E1059', message: '使用相对URL初始化角色错误' },
    typeScriptCompilerLoadingError: { code: 'E1060', message: 'TypeScript编译器加载错误' },
    cannotCustomizeSpecifiedCompilers: { code: 'E1061', message: '无法自定义指定编译器' },
    cannotEnableRetryTestPagesOption: { code: 'E1062', message: '无法启用测试页面重试选项' }
}