// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'nest-app-api',
      script: 'dist/main.js',
      instances: 1, // 保持API实例为多个，或者根据您的需求调整
      autorestart: true,
      env_production: {
        "NODE_ENV": "production",
        "APP_ROLE": "API_WORKER" // API工作进程的角色
      },
      env_development: {
        "NODE_ENV": "development",
        "APP_ROLE": "API_WORKER"
      }
    },
    {
      name: 'nest-app-task-runner',
      script: 'dist/main.js', // Task runner也使用相同的入口点
      instances: 1,  // 关键配置：task runner仅单实例
      autorestart: true,
      env_production: {
        "NODE_ENV": "production",
        "APP_ROLE": "TASK_RUNNER_WORKER" // Task Runner工作进程的角色
      },
      env_development: {
        "NODE_ENV": "development",
        "APP_ROLE": "TASK_RUNNER_WORKER"
      }
    },
    // {
    //   name: 'scheduler',
    //   script: 'dist/scheduler.js', // 新入口文件
    //   instances: 1,  // 关键配置：仅单实例
    //   autorestart: true,
    //   env_production: {
    //     "NODE_ENV": "production",
    //     "API_SERVER_URL": "https://ui-test.tsign.cn",
    //     "APP_ROLE": "SCHEDULER_WORKER" // 调度器工作进程的角色
    //   },
    //   env_development: {
    //     "NODE_ENV": "development",
    //     "APP_ROLE": "SCHEDULER_WORKER"
    //   }
    // }
  ]
}