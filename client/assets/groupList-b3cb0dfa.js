import{n as i,p as l}from"./index-8c8571d2.js";var c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"list-common-table"},[a("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:t.formData,"label-width":80,colon:""},on:{reset:t.onR<PERSON>t,submit:t.onSubmit}},[a("t-row",{attrs:{gutter:[16,24]}},[a("t-col",{attrs:{span:4}},[a("t-form-item",{attrs:{label:"测试集名称",name:"testSuiteName"}},[a("t-input",{staticClass:"form-item-content",attrs:{clearable:"",type:"search",placeholder:"请输入项目名称"},model:{value:t.formData.testSuiteName,callback:function(r){t.$set(t.formData,"testSuiteName",r)},expression:"formData.testSuiteName"}})],1)],1),a("t-col",{attrs:{span:4}},[a("t-form-item",{attrs:{label:"执行人",name:"operator"}},[a("t-input",{staticClass:"form-item-content",attrs:{clearable:"",type:"search",placeholder:"请输入执行人"},model:{value:t.formData.operator,callback:function(r){t.$set(t.formData,"operator",r)},expression:"formData.operator"}})],1)],1),a("t-col",{attrs:{span:4}},[a("t-form-item",{attrs:{label:"测试集类型",name:"projectType"}},[a("t-select",{staticClass:"form-item-content",attrs:{clearable:"",placeholder:"请选择测试集类型"},model:{value:t.formData.projectType,callback:function(r){t.$set(t.formData,"projectType",r)},expression:"formData.projectType"}},t._l(t.projectTypeOptions,function(r){return a("t-option",{key:r.value,attrs:{value:r.value,label:r.label}})}),1)],1)],1),a("t-col",{attrs:{span:4}},[a("t-form-item",{attrs:{label:"执行时间",name:"startTime"}},[a("t-date-range-picker",{staticClass:"form-item-content",attrs:{clearable:"",placeholder:"请选择执行时间范围"},model:{value:t.formData.startTime,callback:function(r){t.$set(t.formData,"startTime",r)},expression:"formData.startTime"}})],1)],1),a("t-col",{attrs:{span:4}},[a("t-form-item",{attrs:{label:"执行环境",name:"env"}},[a("t-select",{staticClass:"form-item-content",attrs:{clearable:"",placeholder:"请选择执行环境"},model:{value:t.formData.env,callback:function(r){t.$set(t.formData,"env",r)},expression:"formData.env"}},t._l(t.envOptions,function(r){return a("t-option",{key:r.value,attrs:{value:r.value,label:r.label}})}),1)],1)],1)],1),a("t-row",{staticStyle:{"margin-top":"8px"}},[a("t-col",{staticClass:"operation-container",attrs:{span:24}},[a("t-button",{attrs:{theme:"primary",type:"submit"}},[t._v(" 查询 ")]),a("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[t._v(" 重置 ")])],1)],1)],1),a("div",{staticClass:"table-container"},[a("t-table",{attrs:{data:t.data,columns:t.columns,"row-key":"id",verticalAlign:t.verticalAlign,hover:t.hover,pagination:t.pagination,loading:t.dataLoading},on:{"page-change":t.onPageChange},scopedSlots:t._u([{key:"op",fn:function(r){var s=r.row;return[a("t-button",{attrs:{theme:"primary",variant:"text",disabled:s.status==="pending"},on:{click:function(o){return t.handle(s)}}},[t._v("查看")]),a("t-button",{attrs:{theme:"primary",variant:"text",disabled:s.status==="pending"},on:{click:function(o){return t.handleStop(s)}}},[t._v("终止")]),a("t-button",{attrs:{theme:"primary",variant:"text",disabled:s.status==="pending"},on:{click:function(o){return t.handleRetry(s)}}},[t._v("重试")])]}}])})],1)],1)},p=[];const u={name:"test-record",data(){return{currentRow:null,prefix:l,formData:{testSuiteName:"",operator:"",projectType:"",product:"",startTime:[],env:""},envOptions:[{label:"全部环境",value:""},{label:"测试环境",value:"test"},{label:"模拟环境",value:"pre"},{label:"生产环境",value:"prod"}],groupNameOptions:[],groupNameLoading:!1,projectTypeOptions:[{label:"项目",value:"project"},{label:"迭代",value:"iteration"},{label:"调试",value:"debug"}],data:[],dataLoading:!1,verticalAlign:"top",hover:!0,pagination:{current:1,pageSize:10,total:1},columns:[{title:"测试集名称",colKey:"testSuiteName",width:120,fixed:"left"},{title:"用例组",colKey:"runId",width:100,cell:(t,{row:e})=>e.runId?e.runId.split("_")[0]:""},{title:"项目类型",colKey:"projectType",width:100,cell:(t,{row:e})=>{const a=this.projectTypeOptions.find(r=>r.value===e.projectType);return a?a.label:e.projectType}},{title:"执行进度",colKey:"progress",width:180,cell:(t,{row:e})=>t("div",{class:"progress-cell"},[t("t-progress",{props:{percentage:e.caseCount?Math.round(e.finishCount/e.caseCount*100):0,label:!0,theme:"plump",color:"#0052D9",trackColor:"#E0E0E0"}})])},{title:"通过/未通过",colKey:"caseCount",align:"center",width:100,cell:(t,{row:e})=>t("div",{class:"progress-cell"},[t("span",{class:"progress-text"},`${e.successCount}/${e.failedCount}`)])},{title:"执行时间",colKey:"startTime",width:180,cell:(t,{row:e})=>new Date(e.startTime).toLocaleString()},{title:"执行环境",colKey:"env",width:100,cell:(t,{row:e})=>{const r={test:"测试环境",pre:"模拟环境",prod:"生产环境"}[e.env]||e.env||"未知";return t("t-tag",{props:{theme:e.env==="prod"?"danger":e.env==="pre"?"warning":"primary",variant:"outline",size:"small"}},r)}},{title:"执行人",colKey:"operator",width:100},{title:"操作",colKey:"op",width:220,fixed:"right",align:"center"}]}},computed:{offsetTop(){return this.$store.state.setting.isUseTabsRouter?48:0}},mounted(){this.fetchData()},beforeDestroy(){this.pollingTimer&&(clearInterval(this.pollingTimer),this.pollingTimer=null)},methods:{async handleRetry(t){try{const e=await this.$dialog.confirm({header:"确认重试",body:"确定要重试此测试集吗？这将重新执行所有用例。",confirmBtn:"确认",cancelBtn:"取消",onConfirm:()=>{e.destroy();const{runId:a}=t;a?this.$request.post("/cases/retryGroupTest",{runId:t.runId}).then(()=>{this.$message.success("重试操作已提交");const r=this.$route.query.id;r&&this.fetchRunData(r)}).catch(r=>{this.$message.error(`重试失败: ${(response==null?void 0:response.message)||"服务器返回未知错误"}`)}):this.$message.error("重试失败")}})}catch(e){console.error("重试操作异常:",e),this.$message.error(`重试操作异常: ${e.message||"网络错误或服务器无响应"}`)}},async handleStop(t){const e=await this.$dialog.confirm({header:"确认终止操作",body:"请确认是否终止，会终止剩余未执行的所有用例",confirmBtn:"确定",cancelBtn:"取消",onConfirm:()=>{e.destroy();const{runId:a}=t;a?this.$request.post("/cases/stopGroupTest",{runId:t.runId}).then(()=>{this.$message.success("已终止执行");const r=this.$route.query.id;r&&this.fetchRunData(r)}).catch(r=>{this.$message.error("终止执行失败")}):this.$message.error("用例名称不存在")}})},onPageChange(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.fetchData({...this.formData})},handle(t){window.open(`/record/groupResult?runId=${t.runId}`)},fetchData(t={page:1,limit:10}){this.dataLoading=!0,this.$request.get("/records/groupByRunId",{params:{page:this.pagination.current,limit:this.pagination.pageSize,...this.formData,startDate:this.formData.startTime?this.formData.startTime[0]:null,endDate:this.formData.startTime?this.formData.startTime[1]:null,...t}}).then(e=>{e&&(e.data&&e.data.data?(this.data=e.data.data,this.pagination.total=e.data.total||this.data.length):(this.data=[],this.pagination.total=0))}).catch(e=>{console.log(e)}).finally(()=>{this.dataLoading=!1})},onReset(){this.formData={testSuiteName:"",operator:"",projectType:"",product:"",startTime:[],env:""}},onSubmit(){this.pagination.current=1,this.fetchData(this.formData)},getContainer(){return document.querySelector(".tdesign-starter-layout")},async fetchGroupNameOptions(){this.groupNameLoading=!0;try{const t=await this.$request.get("/cases/groupNames");t.data&&Array.isArray(t.data)&&(this.groupNameOptions=t.data.map(e=>({label:`${e.groupName}`,value:e.groupName})))}catch(t){console.error(t),this.$message.error("获取用例组数据失败")}finally{this.groupNameLoading=!1}}}},n={};var m=i(u,c,p,!1,d,"2ffe6a31",null,null);function d(t){for(let e in n)this[e]=n[e]}const f=function(){return m.exports}();export{f as default};
