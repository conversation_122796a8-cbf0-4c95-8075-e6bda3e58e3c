import{f as Be,e as ea,n as ta}from"./index-8c8571d2.js";var Mi={exports:{}},Pr,_i;function Ri(){if(_i)return Pr;_i=1;var c;return typeof window<"u"?c=window:typeof Be<"u"?c=Be:typeof self<"u"?c=self:c={},Pr=c,Pr}const ra={},na=Object.freeze(Object.defineProperty({__proto__:null,default:ra},Symbol.toStringTag,{value:"Module"})),ia=ea(na);var Or,bi;function sa(){if(bi)return Or;bi=1;var c=typeof Be<"u"?Be:typeof window<"u"?window:{},d=ia,u;return typeof document<"u"?u=document:(u=c["__GLOBAL_DOCUMENT_CACHE@4"],u||(u=c["__GLOBAL_DOCUMENT_CACHE@4"]=d)),Or=u,Or}var Ir,Ti;function aa(){if(Ti)return Ir;Ti=1;function c(d){return d.replace(/\n\r?\s*/g,"")}return Ir=function(u){for(var T="",C=0;C<arguments.length;C++)T+=c(u[C])+(arguments[C+1]||"");return T},Ir}var jr,Ci;function oa(){if(Ci)return jr;Ci=1,jr=c;function c(d,u){var T,C=null;try{T=JSON.parse(d,u)}catch(P){C=P}return[C,T]}return jr}var Dr,Si;function la(){if(Si)return Dr;Si=1;var c;return typeof window<"u"?c=window:typeof Be<"u"?c=Be:typeof self<"u"?c=self:c={},Dr=c,Dr}var Mr,Ei;function ua(){if(Ei)return Mr;Ei=1,Mr=d;var c=Object.prototype.toString;function d(u){if(!u)return!1;var T=c.call(u);return T==="[object Function]"||typeof u=="function"&&T!=="[object RegExp]"||typeof window<"u"&&(u===window.setTimeout||u===window.alert||u===window.confirm||u===window.prompt)}return Mr}var Rr,ki;function ca(){if(ki)return Rr;ki=1;var c=function(u){return u.replace(/^\s+|\s+$/g,"")},d=function(u){return Object.prototype.toString.call(u)==="[object Array]"};return Rr=function(u){if(!u)return{};for(var T=Object.create(null),C=c(u).split(`
`),P=0;P<C.length;P++){var F=C[P],D=F.indexOf(":"),M=c(F.slice(0,D)).toLowerCase(),G=c(F.slice(D+1));typeof T[M]>"u"?T[M]=G:d(T[M])?T[M].push(G):T[M]=[T[M],G]}return T},Rr}var Lr,xi;function ha(){if(xi)return Lr;xi=1,Lr=d;var c=Object.prototype.hasOwnProperty;function d(){for(var u={},T=0;T<arguments.length;T++){var C=arguments[T];for(var P in C)c.call(C,P)&&(u[P]=C[P])}return u}return Lr}var Nr,wi;function da(){if(wi)return Nr;wi=1;var c=la(),d=ua(),u=ca(),T=ha();Nr=D,D.XMLHttpRequest=c.XMLHttpRequest||H,D.XDomainRequest="withCredentials"in new D.XMLHttpRequest?D.XMLHttpRequest:c.XDomainRequest,C(["get","put","post","patch","head","delete"],function(_){D[_==="delete"?"del":_]=function(I,W,B){return W=F(I,W,B),W.method=_.toUpperCase(),M(W)}});function C(_,I){for(var W=0;W<_.length;W++)I(_[W])}function P(_){for(var I in _)if(_.hasOwnProperty(I))return!1;return!0}function F(_,I,W){var B=_;return d(I)?(W=I,typeof _=="string"&&(B={uri:_})):B=T(I,{uri:_}),B.callback=W,B}function D(_,I,W){return I=F(_,I,W),M(I)}function M(_){if(typeof _.callback>"u")throw new Error("callback argument missing");var I=!1,W=function(V,j,b){I||(I=!0,_.callback(V,j,b))};function B(){R.readyState===4&&setTimeout(Y,0)}function q(){var k=void 0;if(R.response?k=R.response:k=R.responseText||G(R),L)try{k=JSON.parse(k)}catch{}return k}function X(k){return clearTimeout(A),k instanceof Error||(k=new Error(""+(k||"Unknown XMLHttpRequest Error"))),k.statusCode=0,W(k,$)}function Y(){if(!ve){var k;clearTimeout(A),_.useXDR&&R.status===void 0?k=200:k=R.status===1223?204:R.status;var V=$,j=null;return k!==0?(V={body:q(),statusCode:k,method:oe,headers:{},url:ye,rawRequest:R},R.getAllResponseHeaders&&(V.headers=u(R.getAllResponseHeaders()))):j=new Error("Internal XMLHttpRequest Error"),W(j,V,V.body)}}var R=_.xhr||null;R||(_.cors||_.useXDR?R=new D.XDomainRequest:R=new D.XMLHttpRequest);var ne,ve,ye=R.url=_.uri||_.url,oe=R.method=_.method||"GET",p=_.body||_.data,h=R.headers=_.headers||{},S=!!_.sync,L=!1,A,$={body:void 0,headers:{},statusCode:0,method:oe,url:ye,rawRequest:R};if("json"in _&&_.json!==!1&&(L=!0,h.accept||h.Accept||(h.Accept="application/json"),oe!=="GET"&&oe!=="HEAD"&&(h["content-type"]||h["Content-Type"]||(h["Content-Type"]="application/json"),p=JSON.stringify(_.json===!0?p:_.json))),R.onreadystatechange=B,R.onload=Y,R.onerror=X,R.onprogress=function(){},R.onabort=function(){ve=!0},R.ontimeout=X,R.open(oe,ye,!S,_.username,_.password),S||(R.withCredentials=!!_.withCredentials),!S&&_.timeout>0&&(A=setTimeout(function(){if(!ve){ve=!0,R.abort("timeout");var k=new Error("XMLHttpRequest timeout");k.code="ETIMEDOUT",X(k)}},_.timeout)),R.setRequestHeader)for(ne in h)h.hasOwnProperty(ne)&&R.setRequestHeader(ne,h[ne]);else if(_.headers&&!P(_.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in _&&(R.responseType=_.responseType),"beforeSend"in _&&typeof _.beforeSend=="function"&&_.beforeSend(R),R.send(p||null),R}function G(_){if(_.responseType==="document")return _.responseXML;var I=_.responseXML&&_.responseXML.documentElement.nodeName==="parsererror";return _.responseType===""&&!I?_.responseXML:null}function H(){}return Nr}var Fr={exports:{}},Br,Ai;function fa(){if(Ai)return Br;Ai=1;var c=Object.create||function(){function p(){}return function(h){if(arguments.length!==1)throw new Error("Object.create shim only accepts one parameter.");return p.prototype=h,new p}}();function d(p,h){this.name="ParsingError",this.code=p.code,this.message=h||p.message}d.prototype=c(Error.prototype),d.prototype.constructor=d,d.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}};function u(p){function h(L,A,$,k){return(L|0)*3600+(A|0)*60+($|0)+(k|0)/1e3}var S=p.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return S?S[3]?h(S[1],S[2],S[3].replace(":",""),S[4]):S[1]>59?h(S[1],S[2],0,S[4]):h(0,S[1],S[2],S[4]):null}function T(){this.values=c(null)}T.prototype={set:function(p,h){!this.get(p)&&h!==""&&(this.values[p]=h)},get:function(p,h,S){return S?this.has(p)?this.values[p]:h[S]:this.has(p)?this.values[p]:h},has:function(p){return p in this.values},alt:function(p,h,S){for(var L=0;L<S.length;++L)if(h===S[L]){this.set(p,h);break}},integer:function(p,h){/^-?\d+$/.test(h)&&this.set(p,parseInt(h,10))},percent:function(p,h){return h.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(h=parseFloat(h),h>=0&&h<=100)?(this.set(p,h),!0):!1}};function C(p,h,S,L){var A=L?p.split(L):[p];for(var $ in A)if(typeof A[$]=="string"){var k=A[$].split(S);if(k.length===2){var V=k[0],j=k[1];h(V,j)}}}function P(p,h,S){var L=p;function A(){var V=u(p);if(V===null)throw new d(d.Errors.BadTimeStamp,"Malformed timestamp: "+L);return p=p.replace(/^[^\sa-zA-Z-]+/,""),V}function $(V,j){var b=new T;C(V,function(m,v){switch(m){case"region":for(var z=S.length-1;z>=0;z--)if(S[z].id===v){b.set(m,S[z].region);break}break;case"vertical":b.alt(m,v,["rl","lr"]);break;case"line":var Q=v.split(","),ee=Q[0];b.integer(m,ee),b.percent(m,ee)&&b.set("snapToLines",!1),b.alt(m,ee,["auto"]),Q.length===2&&b.alt("lineAlign",Q[1],["start","middle","end"]);break;case"position":Q=v.split(","),b.percent(m,Q[0]),Q.length===2&&b.alt("positionAlign",Q[1],["start","middle","end"]);break;case"size":b.percent(m,v);break;case"align":b.alt(m,v,["start","middle","end","left","right"]);break}},/:/,/\s/),j.region=b.get("region",null),j.vertical=b.get("vertical",""),j.line=b.get("line","auto"),j.lineAlign=b.get("lineAlign","start"),j.snapToLines=b.get("snapToLines",!0),j.size=b.get("size",100),j.align=b.get("align","middle"),j.position=b.get("position",{start:0,left:0,middle:50,end:100,right:100},j.align),j.positionAlign=b.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},j.align)}function k(){p=p.replace(/^\s+/,"")}if(k(),h.startTime=A(),k(),p.substr(0,3)!=="-->")throw new d(d.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '-->'): "+L);p=p.substr(3),k(),h.endTime=A(),k(),$(p,h)}var F={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},D={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},M={v:"title",lang:"lang"},G={rt:"ruby"};function H(p,h){function S(){if(!h)return null;function ee(te){return h=h.substr(te.length),te}var se=h.match(/^([^<]*)(<[^>]*>?)?/);return ee(se[1]?se[1]:se[2])}function L(ee){return F[ee]}function A(ee){for(;Q=ee.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)ee=ee.replace(Q[0],L);return ee}function $(ee,se){return!G[se.localName]||G[se.localName]===ee.localName}function k(ee,se){var te=D[ee];if(!te)return null;var J=p.document.createElement(te);J.localName=te;var he=M[ee];return he&&se&&(J[he]=se.trim()),J}for(var V=p.document.createElement("div"),j=V,b,m=[];(b=S())!==null;){if(b[0]==="<"){if(b[1]==="/"){m.length&&m[m.length-1]===b.substr(2).replace(">","")&&(m.pop(),j=j.parentNode);continue}var v=u(b.substr(1,b.length-2)),z;if(v){z=p.document.createProcessingInstruction("timestamp",v),j.appendChild(z);continue}var Q=b.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!Q||(z=k(Q[1],Q[3]),!z)||!$(j,z))continue;Q[2]&&(z.className=Q[2].substr(1).replace("."," ")),m.push(Q[1]),j.appendChild(z),j=z;continue}j.appendChild(p.document.createTextNode(A(b)))}return V}var _=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function I(p){for(var h=0;h<_.length;h++){var S=_[h];if(p>=S[0]&&p<=S[1])return!0}return!1}function W(p){var h=[],S="",L;if(!p||!p.childNodes)return"ltr";function A(V,j){for(var b=j.childNodes.length-1;b>=0;b--)V.push(j.childNodes[b])}function $(V){if(!V||!V.length)return null;var j=V.pop(),b=j.textContent||j.innerText;if(b){var m=b.match(/^.*(\n|\r)/);return m?(V.length=0,m[0]):b}if(j.tagName==="ruby")return $(V);if(j.childNodes)return A(V,j),$(V)}for(A(h,p);S=$(h);)for(var k=0;k<S.length;k++)if(L=S.charCodeAt(k),I(L))return"rtl";return"ltr"}function B(p){if(typeof p.line=="number"&&(p.snapToLines||p.line>=0&&p.line<=100))return p.line;if(!p.track||!p.track.textTrackList||!p.track.textTrackList.mediaElement)return-1;for(var h=p.track,S=h.textTrackList,L=0,A=0;A<S.length&&S[A]!==h;A++)S[A].mode==="showing"&&L++;return++L*-1}function q(){}q.prototype.applyStyles=function(p,h){h=h||this.div;for(var S in p)p.hasOwnProperty(S)&&(h.style[S]=p[S])},q.prototype.formatStyle=function(p,h){return p===0?0:p+h};function X(p,h,S){var L=/MSIE\s8\.0/.test(navigator.userAgent),A="rgba(255, 255, 255, 1)",$="rgba(0, 0, 0, 0.8)";L&&(A="rgb(255, 255, 255)",$="rgb(0, 0, 0)"),q.call(this),this.cue=h,this.cueDiv=H(p,h.text);var k={color:A,backgroundColor:$,position:"relative",left:0,right:0,top:0,bottom:0,display:"inline"};L||(k.writingMode=h.vertical===""?"horizontal-tb":h.vertical==="lr"?"vertical-lr":"vertical-rl",k.unicodeBidi="plaintext"),this.applyStyles(k,this.cueDiv),this.div=p.document.createElement("div"),k={textAlign:h.align==="middle"?"center":h.align,font:S.font,whiteSpace:"pre-line",position:"absolute"},L||(k.direction=W(this.cueDiv),k.writingMode=h.vertical===""?"horizontal-tb":h.vertical==="lr"?"vertical-lr":"vertical-rl".stylesunicodeBidi="plaintext"),this.applyStyles(k),this.div.appendChild(this.cueDiv);var V=0;switch(h.positionAlign){case"start":V=h.position;break;case"middle":V=h.position-h.size/2;break;case"end":V=h.position-h.size;break}h.vertical===""?this.applyStyles({left:this.formatStyle(V,"%"),width:this.formatStyle(h.size,"%")}):this.applyStyles({top:this.formatStyle(V,"%"),height:this.formatStyle(h.size,"%")}),this.move=function(j){this.applyStyles({top:this.formatStyle(j.top,"px"),bottom:this.formatStyle(j.bottom,"px"),left:this.formatStyle(j.left,"px"),right:this.formatStyle(j.right,"px"),height:this.formatStyle(j.height,"px"),width:this.formatStyle(j.width,"px")})}}X.prototype=c(q.prototype),X.prototype.constructor=X;function Y(p){var h=/MSIE\s8\.0/.test(navigator.userAgent),S,L,A,$;if(p.div){L=p.div.offsetHeight,A=p.div.offsetWidth,$=p.div.offsetTop;var k=(k=p.div.childNodes)&&(k=k[0])&&k.getClientRects&&k.getClientRects();p=p.div.getBoundingClientRect(),S=k?Math.max(k[0]&&k[0].height||0,p.height/k.length):0}this.left=p.left,this.right=p.right,this.top=p.top||$,this.height=p.height||L,this.bottom=p.bottom||$+(p.height||L),this.width=p.width||A,this.lineHeight=S!==void 0?S:p.lineHeight,h&&!this.lineHeight&&(this.lineHeight=13)}Y.prototype.move=function(p,h){switch(h=h!==void 0?h:this.lineHeight,p){case"+x":this.left+=h,this.right+=h;break;case"-x":this.left-=h,this.right-=h;break;case"+y":this.top+=h,this.bottom+=h;break;case"-y":this.top-=h,this.bottom-=h;break}},Y.prototype.overlaps=function(p){return this.left<p.right&&this.right>p.left&&this.top<p.bottom&&this.bottom>p.top},Y.prototype.overlapsAny=function(p){for(var h=0;h<p.length;h++)if(this.overlaps(p[h]))return!0;return!1},Y.prototype.within=function(p){return this.top>=p.top&&this.bottom<=p.bottom&&this.left>=p.left&&this.right<=p.right},Y.prototype.overlapsOppositeAxis=function(p,h){switch(h){case"+x":return this.left<p.left;case"-x":return this.right>p.right;case"+y":return this.top<p.top;case"-y":return this.bottom>p.bottom}},Y.prototype.intersectPercentage=function(p){var h=Math.max(0,Math.min(this.right,p.right)-Math.max(this.left,p.left)),S=Math.max(0,Math.min(this.bottom,p.bottom)-Math.max(this.top,p.top)),L=h*S;return L/(this.height*this.width)},Y.prototype.toCSSCompatValues=function(p){return{top:this.top-p.top,bottom:p.bottom-this.bottom,left:this.left-p.left,right:p.right-this.right,height:this.height,width:this.width}},Y.getSimpleBoxPosition=function(p){var h=p.div?p.div.offsetHeight:p.tagName?p.offsetHeight:0,S=p.div?p.div.offsetWidth:p.tagName?p.offsetWidth:0,L=p.div?p.div.offsetTop:p.tagName?p.offsetTop:0;p=p.div?p.div.getBoundingClientRect():p.tagName?p.getBoundingClientRect():p;var A={left:p.left,right:p.right,top:p.top||L,height:p.height||h,bottom:p.bottom||L+(p.height||h),width:p.width||S};return A};function R(p,h,S,L){function A(te,J){for(var he,we=new Y(te),de=1,$e=0;$e<J.length;$e++){for(;te.overlapsOppositeAxis(S,J[$e])||te.within(S)&&te.overlapsAny(L);)te.move(J[$e]);if(te.within(S))return te;var Ze=te.intersectPercentage(S);de>Ze&&(he=new Y(te),de=Ze),te=new Y(we)}return he||we}var $=new Y(h),k=h.cue,V=B(k),j=[];if(k.snapToLines){var b;switch(k.vertical){case"":j=["+y","-y"],b="height";break;case"rl":j=["+x","-x"],b="width";break;case"lr":j=["-x","+x"],b="width";break}var m=$.lineHeight,v=m*Math.round(V),z=S[b]+m,Q=j[0];Math.abs(v)>z&&(v=v<0?-1:1,v*=Math.ceil(z/m)*m),V<0&&(v+=k.vertical===""?S.height:S.width,j=j.reverse()),$.move(Q,v)}else{var ee=$.lineHeight/S.height*100;switch(k.lineAlign){case"middle":V-=ee/2;break;case"end":V-=ee;break}switch(k.vertical){case"":h.applyStyles({top:h.formatStyle(V,"%")});break;case"rl":h.applyStyles({left:h.formatStyle(V,"%")});break;case"lr":h.applyStyles({right:h.formatStyle(V,"%")});break}j=["+y","-x","+x","-y"],$=new Y(h)}var se=A($,j);h.move(se.toCSSCompatValues(S))}function ne(){}ne.StringDecoder=function(){return{decode:function(p){if(!p)return"";if(typeof p!="string")throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(p))}}},ne.convertCueToDOMTree=function(p,h){return!p||!h?null:H(p,h)};var ve=.05,ye="sans-serif",oe="1.5%";return ne.processCues=function(p,h,S){if(!p||!h||!S)return null;for(;S.firstChild;)S.removeChild(S.firstChild);var L=p.document.createElement("div");L.style.position="absolute",L.style.left="0",L.style.right="0",L.style.top="0",L.style.bottom="0",L.style.margin=oe,S.appendChild(L);function A(m){for(var v=0;v<m.length;v++)if(m[v].hasBeenReset||!m[v].displayState)return!0;return!1}if(!A(h)){for(var $=0;$<h.length;$++)L.appendChild(h[$].displayState);return}var k=[],V=Y.getSimpleBoxPosition(L),j=Math.round(V.height*ve*100)/100,b={font:j+"px "+ye};(function(){for(var m,v,z=0;z<h.length;z++)v=h[z],m=new X(p,v,b),L.appendChild(m.div),R(p,m,V,k),v.displayState=m.div,k.push(Y.getSimpleBoxPosition(m))})()},ne.Parser=function(p,h,S){S||(S=h,h={}),h||(h={}),this.window=p,this.vttjs=h,this.state="INITIAL",this.buffer="",this.decoder=S||new TextDecoder("utf8"),this.regionList=[]},ne.Parser.prototype={reportOrThrowError:function(p){if(p instanceof d)this.onparsingerror&&this.onparsingerror(p);else throw p},parse:function(p){var h=this;p&&(h.buffer+=h.decoder.decode(p,{stream:!0}));function S(){for(var m=h.buffer,v=0;v<m.length&&m[v]!=="\r"&&m[v]!==`
`;)++v;var z=m.substr(0,v);return m[v]==="\r"&&++v,m[v]===`
`&&++v,h.buffer=m.substr(v),z}function L(m){var v=new T;if(C(m,function(Q,ee){switch(Q){case"id":v.set(Q,ee);break;case"width":v.percent(Q,ee);break;case"lines":v.integer(Q,ee);break;case"regionanchor":case"viewportanchor":var se=ee.split(",");if(se.length!==2)break;var te=new T;if(te.percent("x",se[0]),te.percent("y",se[1]),!te.has("x")||!te.has("y"))break;v.set(Q+"X",te.get("x")),v.set(Q+"Y",te.get("y"));break;case"scroll":v.alt(Q,ee,["up"]);break}},/=/,/\s/),v.has("id")){var z=new(h.vttjs.VTTRegion||h.window.VTTRegion);z.width=v.get("width",100),z.lines=v.get("lines",3),z.regionAnchorX=v.get("regionanchorX",0),z.regionAnchorY=v.get("regionanchorY",100),z.viewportAnchorX=v.get("viewportanchorX",0),z.viewportAnchorY=v.get("viewportanchorY",100),z.scroll=v.get("scroll",""),h.onregion&&h.onregion(z),h.regionList.push({id:v.get("id"),region:z})}}function A(m){var v=new T;C(m,function(z,Q){switch(z){case"MPEGT":v.integer(z+"S",Q);break;case"LOCA":v.set(z+"L",u(Q));break}},/[^\d]:/,/,/),h.ontimestampmap&&h.ontimestampmap({MPEGTS:v.get("MPEGTS"),LOCAL:v.get("LOCAL")})}function $(m){m.match(/X-TIMESTAMP-MAP/)?C(m,function(v,z){switch(v){case"X-TIMESTAMP-MAP":A(z);break}},/=/):C(m,function(v,z){switch(v){case"Region":L(z);break}},/:/)}try{var k;if(h.state==="INITIAL"){if(!/\r\n|\n/.test(h.buffer))return this;k=S();var V=k.match(/^WEBVTT([ \t].*)?$/);if(!V||!V[0])throw new d(d.Errors.BadSignature);h.state="HEADER"}for(var j=!1;h.buffer;){if(!/\r\n|\n/.test(h.buffer))return this;switch(j?j=!1:k=S(),h.state){case"HEADER":/:/.test(k)?$(k):k||(h.state="ID");continue;case"NOTE":k||(h.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(k)){h.state="NOTE";break}if(!k)continue;if(h.cue=new(h.vttjs.VTTCue||h.window.VTTCue)(0,0,""),h.state="CUE",k.indexOf("-->")===-1){h.cue.id=k;continue}case"CUE":try{P(k,h.cue,h.regionList)}catch(m){h.reportOrThrowError(m),h.cue=null,h.state="BADCUE";continue}h.state="CUETEXT";continue;case"CUETEXT":var b=k.indexOf("-->")!==-1;if(!k||b&&(j=!0)){h.oncue&&h.oncue(h.cue),h.cue=null,h.state="ID";continue}h.cue.text&&(h.cue.text+=`
`),h.cue.text+=k;continue;case"BADCUE":k||(h.state="ID");continue}}}catch(m){h.reportOrThrowError(m),h.state==="CUETEXT"&&h.cue&&h.oncue&&h.oncue(h.cue),h.cue=null,h.state=h.state==="INITIAL"?"BADWEBVTT":"BADCUE"}return this},flush:function(){var p=this;try{if(p.buffer+=p.decoder.decode(),(p.cue||p.state==="HEADER")&&(p.buffer+=`

`,p.parse()),p.state==="INITIAL")throw new d(d.Errors.BadSignature)}catch(h){p.reportOrThrowError(h)}return p.onflush&&p.onflush(),this}},Br=ne,Br}var $r,Pi;function pa(){if(Pi)return $r;Pi=1;var c="auto",d={"":!0,lr:!0,rl:!0},u={start:!0,middle:!0,end:!0,left:!0,right:!0};function T(D){if(typeof D!="string")return!1;var M=d[D.toLowerCase()];return M?D.toLowerCase():!1}function C(D){if(typeof D!="string")return!1;var M=u[D.toLowerCase()];return M?D.toLowerCase():!1}function P(D){for(var M=1;M<arguments.length;M++){var G=arguments[M];for(var H in G)D[H]=G[H]}return D}function F(D,M,G){var H=this,_=/MSIE\s8\.0/.test(navigator.userAgent),I={};_?H=document.createElement("custom"):I.enumerable=!0,H.hasBeenReset=!1;var W="",B=!1,q=D,X=M,Y=G,R=null,ne="",ve=!0,ye="auto",oe="start",p=50,h="middle",S=50,L="middle";if(Object.defineProperty(H,"id",P({},I,{get:function(){return W},set:function(A){W=""+A}})),Object.defineProperty(H,"pauseOnExit",P({},I,{get:function(){return B},set:function(A){B=!!A}})),Object.defineProperty(H,"startTime",P({},I,{get:function(){return q},set:function(A){if(typeof A!="number")throw new TypeError("Start time must be set to a number.");q=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"endTime",P({},I,{get:function(){return X},set:function(A){if(typeof A!="number")throw new TypeError("End time must be set to a number.");X=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"text",P({},I,{get:function(){return Y},set:function(A){Y=""+A,this.hasBeenReset=!0}})),Object.defineProperty(H,"region",P({},I,{get:function(){return R},set:function(A){R=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"vertical",P({},I,{get:function(){return ne},set:function(A){var $=T(A);if($===!1)throw new SyntaxError("An invalid or illegal string was specified.");ne=$,this.hasBeenReset=!0}})),Object.defineProperty(H,"snapToLines",P({},I,{get:function(){return ve},set:function(A){ve=!!A,this.hasBeenReset=!0}})),Object.defineProperty(H,"line",P({},I,{get:function(){return ye},set:function(A){if(typeof A!="number"&&A!==c)throw new SyntaxError("An invalid number or illegal string was specified.");ye=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"lineAlign",P({},I,{get:function(){return oe},set:function(A){var $=C(A);if(!$)throw new SyntaxError("An invalid or illegal string was specified.");oe=$,this.hasBeenReset=!0}})),Object.defineProperty(H,"position",P({},I,{get:function(){return p},set:function(A){if(A<0||A>100)throw new Error("Position must be between 0 and 100.");p=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"positionAlign",P({},I,{get:function(){return h},set:function(A){var $=C(A);if(!$)throw new SyntaxError("An invalid or illegal string was specified.");h=$,this.hasBeenReset=!0}})),Object.defineProperty(H,"size",P({},I,{get:function(){return S},set:function(A){if(A<0||A>100)throw new Error("Size must be between 0 and 100.");S=A,this.hasBeenReset=!0}})),Object.defineProperty(H,"align",P({},I,{get:function(){return L},set:function(A){var $=C(A);if(!$)throw new SyntaxError("An invalid or illegal string was specified.");L=$,this.hasBeenReset=!0}})),H.displayState=void 0,_)return H}return F.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)},$r=F,$r}var Vr,Oi;function ga(){if(Oi)return Vr;Oi=1;var c={"":!0,up:!0};function d(C){if(typeof C!="string")return!1;var P=c[C.toLowerCase()];return P?C.toLowerCase():!1}function u(C){return typeof C=="number"&&C>=0&&C<=100}function T(){var C=100,P=3,F=0,D=100,M=0,G=100,H="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return C},set:function(_){if(!u(_))throw new Error("Width must be between 0 and 100.");C=_}},lines:{enumerable:!0,get:function(){return P},set:function(_){if(typeof _!="number")throw new TypeError("Lines must be set to a number.");P=_}},regionAnchorY:{enumerable:!0,get:function(){return D},set:function(_){if(!u(_))throw new Error("RegionAnchorX must be between 0 and 100.");D=_}},regionAnchorX:{enumerable:!0,get:function(){return F},set:function(_){if(!u(_))throw new Error("RegionAnchorY must be between 0 and 100.");F=_}},viewportAnchorY:{enumerable:!0,get:function(){return G},set:function(_){if(!u(_))throw new Error("ViewportAnchorY must be between 0 and 100.");G=_}},viewportAnchorX:{enumerable:!0,get:function(){return M},set:function(_){if(!u(_))throw new Error("ViewportAnchorX must be between 0 and 100.");M=_}},scroll:{enumerable:!0,get:function(){return H},set:function(_){var I=d(_);if(I===!1)throw new SyntaxError("An invalid or illegal string was specified.");H=I}}})}return Vr=T,Vr}var Ii;function va(){if(Ii)return Fr.exports;Ii=1;var c=Ri(),d=Fr.exports={WebVTT:fa(),VTTCue:pa(),VTTRegion:ga()};c.vttjs=d,c.WebVTT=d.WebVTT;var u=d.VTTCue,T=d.VTTRegion,C=c.VTTCue,P=c.VTTRegion;return d.shim=function(){c.VTTCue=u,c.VTTRegion=T},d.restore=function(){c.VTTCue=C,c.VTTRegion=P},c.VTTCue||d.shim(),Fr.exports}/**
 * @license
 * Video.js 6.13.0 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/master/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/master/LICENSE>
 */var Hr,ji;function ya(){if(ji)return Hr;ji=1;function c(i){return i&&typeof i=="object"&&"default"in i?i.default:i}var d=c(Ri()),u=c(sa()),T=c(aa()),C=c(oa()),P=c(da()),F=c(va()),D="6.13.0",M=d.navigator&&d.navigator.userAgent||"",G=/AppleWebKit\/([\d.]+)/i.exec(M),H=G?parseFloat(G.pop()):null,_=/iPad/i.test(M),I=/iPhone/i.test(M)&&!_,W=/iPod/i.test(M),B=I||_||W,q=function(){var i=M.match(/OS (\d+)_/i);return i&&i[1]?i[1]:null}(),X=/Android/i.test(M),Y=function(){var i=M.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!i)return null;var t=i[1]&&parseFloat(i[1]),r=i[2]&&parseFloat(i[2]);return t&&r?parseFloat(i[1]+"."+i[2]):t||null}(),R=X&&/webkit/i.test(M)&&Y<2.3,ne=X&&Y<5&&H<537,ve=/Firefox/i.test(M),ye=/Edge/i.test(M),oe=!ye&&(/Chrome/i.test(M)||/CriOS/i.test(M)),p=function(){var i=M.match(/(Chrome|CriOS)\/(\d+)/);return i&&i[2]?parseFloat(i[2]):null}(),h=/MSIE\s8\.0/.test(M),S=function(){var i=/MSIE\s(\d+)\.\d/.exec(M),t=i&&parseFloat(i[1]);return!t&&/Trident\/7.0/i.test(M)&&/rv:11.0/.test(M)&&(t=11),t}(),L=/Safari/i.test(M)&&!oe&&!X&&!ye,A=(L||B)&&!oe,$=Ae()&&("ontouchstart"in d||d.navigator.maxTouchPoints||d.DocumentTouch&&d.document instanceof d.DocumentTouch),k=Ae()&&"backgroundSize"in d.document.createElement("video").style,V=(Object.freeze||Object)({IS_IPAD:_,IS_IPHONE:I,IS_IPOD:W,IS_IOS:B,IOS_VERSION:q,IS_ANDROID:X,ANDROID_VERSION:Y,IS_OLD_ANDROID:R,IS_NATIVE_ANDROID:ne,IS_FIREFOX:ve,IS_EDGE:ye,IS_CHROME:oe,CHROME_VERSION:p,IS_IE8:h,IE_VERSION:S,IS_SAFARI:L,IS_ANY_SAFARI:A,TOUCH_ENABLED:$,BACKGROUND_SIZE_SUPPORTED:k}),j=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},b=function(i,t){if(!(i instanceof t))throw new TypeError("Cannot call a class as a function")},m=function(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(i,t):i.__proto__=t)},v=function(i,t){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:i},z=function(i,t){return i.raw=t,i},Q=Object.prototype.toString,ee=function(t){return he(t)?Object.keys(t):[]};function se(i,t){ee(i).forEach(function(r){return t(i[r],r)})}function te(i,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;return ee(i).reduce(function(e,n){return t(e,i[n],n)},r)}function J(i){for(var t=arguments.length,r=Array(t>1?t-1:0),e=1;e<t;e++)r[e-1]=arguments[e];return Object.assign?Object.assign.apply(Object,[i].concat(r)):(r.forEach(function(n){n&&se(n,function(s,a){i[a]=s})}),i)}function he(i){return!!i&&(typeof i>"u"?"undefined":j(i))==="object"}function we(i){return he(i)&&Q.call(i)==="[object Object]"&&i.constructor===Object}var de=[],$e=function(t,r){return function(e,n,s,a){var o=r.levels[n],l=new RegExp("^("+o+")$");if(e!=="log"&&s.unshift(e.toUpperCase()+":"),s.unshift(t+":"),de&&de.push([].concat(s)),!!d.console){var f=d.console[e];!f&&e==="debug"&&(f=d.console.info||d.console.log),!(!f||!o||!l.test(e))&&(a&&(s=s.map(function(g){if(he(g)||Array.isArray(g))try{return JSON.stringify(g)}catch{return String(g)}return String(g)}).join(" ")),f.apply?f[Array.isArray(s)?"apply":"call"](d.console,s):f(s))}}};function Ze(i){var t="info",r=void 0,e=function n(){for(var s=n.stringify||S&&S<11,a=arguments.length,o=Array(a),l=0;l<a;l++)o[l]=arguments[l];r("log",t,o,s)};return r=$e(i,e),e.createLogger=function(n){return Ze(i+": "+n)},e.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:t},e.level=function(n){if(typeof n=="string"){if(!e.levels.hasOwnProperty(n))throw new Error('"'+n+'" in not a valid log level');t=n}return t},e.history=function(){return de?[].concat(de):[]},e.history.filter=function(n){return(de||[]).filter(function(s){return new RegExp(".*"+n+".*").test(s[0])})},e.history.clear=function(){de&&(de.length=0)},e.history.disable=function(){de!==null&&(de.length=0,de=null)},e.history.enable=function(){de===null&&(de=[])},e.error=function(){for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return r("error",t,s)},e.warn=function(){for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return r("warn",t,s)},e.debug=function(){for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return r("debug",t,s)},e}var K=Ze("VIDEOJS"),zr=K.createLogger;function Rt(i,t){if(!i||!t)return"";if(typeof d.getComputedStyle=="function"){var r=d.getComputedStyle(i);return r?r[t]:""}return i.currentStyle[t]||""}var Li=z([`Setting attributes in the second argument of createEl()
                has been deprecated. Use the third argument instead.
                createEl(type, properties, attributes). Attempting to set `," to ","."],[`Setting attributes in the second argument of createEl()
                has been deprecated. Use the third argument instead.
                createEl(type, properties, attributes). Attempting to set `," to ","."]);function Wr(i){return typeof i=="string"&&/\S/.test(i)}function Ur(i){if(/\s/.test(i))throw new Error("class has illegal whitespace characters")}function Ni(i){return new RegExp("(^|\\s)"+i+"($|\\s)")}function Ae(){return u===d.document&&typeof u.createElement<"u"}function et(i){return he(i)&&i.nodeType===1}function qr(){try{return d.parent!==d.self}catch{return!0}}function Kr(i){return function(t,r){if(!Wr(t))return u[i](null);Wr(r)&&(r=u.querySelector(r));var e=et(r)?r:u;return e[i]&&e[i](t)}}function le(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"div",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},e=arguments[3],n=u.createElement(i);return Object.getOwnPropertyNames(t).forEach(function(s){var a=t[s];s.indexOf("aria-")!==-1||s==="role"||s==="type"?(K.warn(T(Li,s,a)),n.setAttribute(s,a)):s==="textContent"?tt(n,a):n[s]=a}),Object.getOwnPropertyNames(r).forEach(function(s){n.setAttribute(s,r[s])}),e&&Ft(n,e),n}function tt(i,t){return typeof i.textContent>"u"?i.innerText=t:i.textContent=t,i}function vt(i,t){t.firstChild?t.insertBefore(i,t.firstChild):t.appendChild(i)}function Ie(i,t){return Ur(t),i.classList?i.classList.contains(t):Ni(t).test(i.className)}function je(i,t){return i.classList?i.classList.add(t):Ie(i,t)||(i.className=(i.className+" "+t).trim()),i}function rt(i,t){return i.classList?i.classList.remove(t):(Ur(t),i.className=i.className.split(/\s+/).filter(function(r){return r!==t}).join(" ")),i}function Xr(i,t,r){var e=Ie(i,t);if(typeof r=="function"&&(r=r(i,t)),typeof r!="boolean"&&(r=!e),r!==e)return r?je(i,t):rt(i,t),i}function Gr(i,t){Object.getOwnPropertyNames(t).forEach(function(r){var e=t[r];e===null||typeof e>"u"||e===!1?i.removeAttribute(r):i.setAttribute(r,e===!0?"":e)})}function ke(i){var t={},r=",autoplay,controls,playsinline,loop,muted,default,defaultMuted,";if(i&&i.attributes&&i.attributes.length>0)for(var e=i.attributes,n=e.length-1;n>=0;n--){var s=e[n].name,a=e[n].value;(typeof i[s]=="boolean"||r.indexOf(","+s+",")!==-1)&&(a=a!==null),t[s]=a}return t}function Yr(i,t){return i.getAttribute(t)}function nt(i,t,r){i.setAttribute(t,r)}function yt(i,t){i.removeAttribute(t)}function Jr(){u.body.focus(),u.onselectstart=function(){return!1}}function Qr(){u.onselectstart=function(){return!0}}function it(i){if(i&&i.getBoundingClientRect&&i.parentNode){var t=i.getBoundingClientRect(),r={};return["bottom","height","left","right","top","width"].forEach(function(e){t[e]!==void 0&&(r[e]=t[e])}),r.height||(r.height=parseFloat(Rt(i,"height"))),r.width||(r.width=parseFloat(Rt(i,"width"))),r}}function Zr(i){var t=void 0;if(i.getBoundingClientRect&&i.parentNode&&(t=i.getBoundingClientRect()),!t)return{left:0,top:0};var r=u.documentElement,e=u.body,n=r.clientLeft||e.clientLeft||0,s=d.pageXOffset||e.scrollLeft,a=t.left+s-n,o=r.clientTop||e.clientTop||0,l=d.pageYOffset||e.scrollTop,f=t.top+l-o;return{left:Math.round(a),top:Math.round(f)}}function Lt(i,t){var r={},e=Zr(i),n=i.offsetWidth,s=i.offsetHeight,a=e.top,o=e.left,l=t.pageY,f=t.pageX;return t.changedTouches&&(f=t.changedTouches[0].pageX,l=t.changedTouches[0].pageY),r.y=Math.max(0,Math.min(1,(a-l+s)/s)),r.x=Math.max(0,Math.min(1,(f-o)/n)),r}function en(i){return he(i)&&i.nodeType===3}function Nt(i){for(;i.firstChild;)i.removeChild(i.firstChild);return i}function tn(i){return typeof i=="function"&&(i=i()),(Array.isArray(i)?i:[i]).map(function(t){if(typeof t=="function"&&(t=t()),et(t)||en(t))return t;if(typeof t=="string"&&/\S/.test(t))return u.createTextNode(t)}).filter(function(t){return t})}function Ft(i,t){return tn(t).forEach(function(r){return i.appendChild(r)}),i}function rn(i,t){return Ft(Nt(i),t)}function Ve(i){return i.button===void 0&&i.buttons===void 0||i.button===0&&i.buttons===void 0||S===9?!0:!(i.button!==0||i.buttons!==1)}var Pe=Kr("querySelector"),nn=Kr("querySelectorAll"),sn=(Object.freeze||Object)({isReal:Ae,isEl:et,isInFrame:qr,createEl:le,textContent:tt,prependTo:vt,hasClass:Ie,addClass:je,removeClass:rt,toggleClass:Xr,setAttributes:Gr,getAttributes:ke,getAttribute:Yr,setAttribute:nt,removeAttribute:yt,blockTextSelection:Jr,unblockTextSelection:Qr,getBoundingClientRect:it,findPosition:Zr,getPointerPosition:Lt,isTextNode:en,emptyEl:Nt,normalizeContent:tn,appendContent:Ft,insertContent:rn,isSingleLeftClick:Ve,$:Pe,$$:nn}),Fi=1;function De(){return Fi++}var st={},Me="vdata"+new Date().getTime();function at(i){var t=i[Me];return t||(t=i[Me]=De()),st[t]||(st[t]={}),st[t]}function an(i){var t=i[Me];return t?!!Object.getOwnPropertyNames(st[t]).length:!1}function on(i){var t=i[Me];if(t){delete st[t];try{delete i[Me]}catch{i.removeAttribute?i.removeAttribute(Me):i[Me]=null}}}function ln(i,t){var r=at(i);r.handlers[t].length===0&&(delete r.handlers[t],i.removeEventListener?i.removeEventListener(t,r.dispatcher,!1):i.detachEvent&&i.detachEvent("on"+t,r.dispatcher)),Object.getOwnPropertyNames(r.handlers).length<=0&&(delete r.handlers,delete r.dispatcher,delete r.disabled),Object.getOwnPropertyNames(r).length===0&&on(i)}function Bt(i,t,r,e){r.forEach(function(n){i(t,n,e)})}function mt(i){function t(){return!0}function r(){return!1}if(!i||!i.isPropagationStopped){var e=i||d.event;i={};for(var n in e)n!=="layerX"&&n!=="layerY"&&n!=="keyLocation"&&n!=="webkitMovementX"&&n!=="webkitMovementY"&&(n==="returnValue"&&e.preventDefault||(i[n]=e[n]));if(i.target||(i.target=i.srcElement||u),i.relatedTarget||(i.relatedTarget=i.fromElement===i.target?i.toElement:i.fromElement),i.preventDefault=function(){e.preventDefault&&e.preventDefault(),i.returnValue=!1,e.returnValue=!1,i.defaultPrevented=!0},i.defaultPrevented=!1,i.stopPropagation=function(){e.stopPropagation&&e.stopPropagation(),i.cancelBubble=!0,e.cancelBubble=!0,i.isPropagationStopped=t},i.isPropagationStopped=r,i.stopImmediatePropagation=function(){e.stopImmediatePropagation&&e.stopImmediatePropagation(),i.isImmediatePropagationStopped=t,i.stopPropagation()},i.isImmediatePropagationStopped=r,i.clientX!==null&&i.clientX!==void 0){var s=u.documentElement,a=u.body;i.pageX=i.clientX+(s&&s.scrollLeft||a&&a.scrollLeft||0)-(s&&s.clientLeft||a&&a.clientLeft||0),i.pageY=i.clientY+(s&&s.scrollTop||a&&a.scrollTop||0)-(s&&s.clientTop||a&&a.clientTop||0)}i.which=i.charCode||i.keyCode,i.button!==null&&i.button!==void 0&&(i.button=i.button&1?0:i.button&4?1:i.button&2?2:0)}return i}var un=!1;(function(){try{var i=Object.defineProperty({},"passive",{get:function(){un=!0}});d.addEventListener("test",null,i),d.removeEventListener("test",null,i)}catch{}})();var Bi=["touchstart","touchmove"];function Ce(i,t,r){if(Array.isArray(t))return Bt(Ce,i,t,r);var e=at(i);if(e.handlers||(e.handlers={}),e.handlers[t]||(e.handlers[t]=[]),r.guid||(r.guid=De()),e.handlers[t].push(r),e.dispatcher||(e.disabled=!1,e.dispatcher=function(s,a){if(!e.disabled){s=mt(s);var o=e.handlers[s.type];if(o)for(var l=o.slice(0),f=0,g=l.length;f<g&&!s.isImmediatePropagationStopped();f++)try{l[f].call(i,s,a)}catch(y){K.error(y)}}}),e.handlers[t].length===1)if(i.addEventListener){var n=!1;un&&Bi.indexOf(t)>-1&&(n={passive:!0}),i.addEventListener(t,e.dispatcher,n)}else i.attachEvent&&i.attachEvent("on"+t,e.dispatcher)}function pe(i,t,r){if(an(i)){var e=at(i);if(e.handlers){if(Array.isArray(t))return Bt(pe,i,t,r);var n=function(f,g){e.handlers[g]=[],ln(f,g)};if(t===void 0){for(var s in e.handlers)Object.prototype.hasOwnProperty.call(e.handlers||{},s)&&n(i,s);return}var a=e.handlers[t];if(a){if(!r){n(i,t);return}if(r.guid)for(var o=0;o<a.length;o++)a[o].guid===r.guid&&a.splice(o--,1);ln(i,t)}}}}function He(i,t,r){var e=an(i)?at(i):{},n=i.parentNode||i.ownerDocument;if(typeof t=="string"?t={type:t,target:i}:t.target||(t.target=i),t=mt(t),e.dispatcher&&e.dispatcher.call(i,t,r),n&&!t.isPropagationStopped()&&t.bubbles===!0)He.call(null,n,t,r);else if(!n&&!t.defaultPrevented){var s=at(t.target);t.target[t.type]&&(s.disabled=!0,typeof t.target[t.type]=="function"&&t.target[t.type](),s.disabled=!1)}return!t.defaultPrevented}function ot(i,t,r){if(Array.isArray(t))return Bt(ot,i,t,r);var e=function n(){pe(i,t,n),r.apply(this,arguments)};e.guid=r.guid=r.guid||De(),Ce(i,t,e)}var $i=(Object.freeze||Object)({fixEvent:mt,on:Ce,off:pe,trigger:He,one:ot}),$t=!1,Vt=void 0,Vi=function(){if(!(!Ae()||Vt.options.autoSetup===!1)){var t=u.getElementsByTagName("video"),r=u.getElementsByTagName("audio"),e=u.getElementsByTagName("video-js"),n=[];if(t&&t.length>0)for(var s=0,a=t.length;s<a;s++)n.push(t[s]);if(r&&r.length>0)for(var o=0,l=r.length;o<l;o++)n.push(r[o]);if(e&&e.length>0)for(var f=0,g=e.length;f<g;f++)n.push(e[f]);if(n&&n.length>0)for(var y=0,x=n.length;y<x;y++){var N=n[y];if(N&&N.getAttribute){if(N.player===void 0){var Z=N.getAttribute("data-setup");Z!==null&&Vt(N)}}else{Ht(1);break}}else $t||Ht(1)}};function Ht(i,t){t&&(Vt=t),d.setTimeout(Vi,i)}Ae()&&u.readyState==="complete"?$t=!0:ot(d,"load",function(){$t=!0});var cn=function(t){var r=u.createElement("style");return r.className=t,r},hn=function(t,r){t.styleSheet?t.styleSheet.cssText=r:t.textContent=r},U=function(t,r,e){r.guid||(r.guid=De());var n=function(){return r.apply(t,arguments)};return n.guid=e?e+"_"+r.guid:r.guid,n},ze=function(t,r){var e=Date.now(),n=function(){var a=Date.now();a-e>=r&&(t.apply(void 0,arguments),e=a)};return n},Hi=function(t,r,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:d,s=void 0,a=function(){n.clearTimeout(s),s=null},o=function(){var f=this,g=arguments,y=function(){s=null,y=null,e||t.apply(f,g)};!s&&e&&t.apply(f,g),n.clearTimeout(s),s=n.setTimeout(y,r)};return o.cancel=a,o},ce=function(){};ce.prototype.allowedEvents_={},ce.prototype.on=function(i,t){var r=this.addEventListener;this.addEventListener=function(){},Ce(this,i,t),this.addEventListener=r},ce.prototype.addEventListener=ce.prototype.on,ce.prototype.off=function(i,t){pe(this,i,t)},ce.prototype.removeEventListener=ce.prototype.off,ce.prototype.one=function(i,t){var r=this.addEventListener;this.addEventListener=function(){},ot(this,i,t),this.addEventListener=r},ce.prototype.trigger=function(i){var t=i.type||i;typeof i=="string"&&(i={type:t}),i=mt(i),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](i),He(this,i)},ce.prototype.dispatchEvent=ce.prototype.trigger;var _t=function(t){return t instanceof ce||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(r){return typeof t[r]=="function"})},dn=function(t){return typeof t=="string"&&/\S/.test(t)||Array.isArray(t)&&!!t.length},zt=function(t){if(!t.nodeName&&!_t(t))throw new Error("Invalid target; must be a DOM node or evented object.")},fn=function(t){if(!dn(t))throw new Error("Invalid event type; must be a non-empty string or array.")},pn=function(t){if(typeof t!="function")throw new Error("Invalid listener; must be a function.")},gn=function(t,r){var e=r.length<3||r[0]===t||r[0]===t.eventBusEl_,n=void 0,s=void 0,a=void 0;return e?(n=t.eventBusEl_,r.length>=3&&r.shift(),s=r[0],a=r[1]):(n=r[0],s=r[1],a=r[2]),zt(n),fn(s),pn(a),a=U(t,a),{isTargetingSelf:e,target:n,type:s,listener:a}},lt=function(t,r,e,n){zt(t),t.nodeName?$i[r](t,e,n):t[r](e,n)},zi={on:function(){for(var t=this,r=arguments.length,e=Array(r),n=0;n<r;n++)e[n]=arguments[n];var s=gn(this,e),a=s.isTargetingSelf,o=s.target,l=s.type,f=s.listener;if(lt(o,"on",l,f),!a){var g=function(){return t.off(o,l,f)};g.guid=f.guid;var y=function(){return t.off("dispose",g)};y.guid=f.guid,lt(this,"on","dispose",g),lt(o,"on","dispose",y)}},one:function(){for(var t=this,r=arguments.length,e=Array(r),n=0;n<r;n++)e[n]=arguments[n];var s=gn(this,e),a=s.isTargetingSelf,o=s.target,l=s.type,f=s.listener;if(a)lt(o,"one",l,f);else{var g=function y(){for(var x=arguments.length,N=Array(x),Z=0;Z<x;Z++)N[Z]=arguments[Z];t.off(o,l,y),f.apply(null,N)};g.guid=f.guid,lt(o,"one",l,g)}},off:function(t,r,e){if(!t||dn(t))pe(this.eventBusEl_,t,r);else{var n=t,s=r;zt(n),fn(s),pn(e),e=U(this,e),this.off("dispose",e),n.nodeName?(pe(n,s,e),pe(n,"dispose",e)):_t(n)&&(n.off(s,e),n.off("dispose",e))}},trigger:function(t,r){return He(this.eventBusEl_,t,r)}};function Wt(i){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.eventBusKey;if(r){if(!i[r].nodeName)throw new Error('The eventBusKey "'+r+'" does not refer to an element.');i.eventBusEl_=i[r]}else i.eventBusEl_=le("span",{className:"vjs-event-bus"});return J(i,zi),i.on("dispose",function(){i.off(),d.setTimeout(function(){i.eventBusEl_=null},0)}),i}var Wi={state:{},setState:function(t){var r=this;typeof t=="function"&&(t=t());var e=void 0;return se(t,function(n,s){r.state[s]!==n&&(e=e||{},e[s]={from:r.state[s],to:n}),r.state[s]=n}),e&&_t(this)&&this.trigger({changes:e,type:"statechanged"}),e}};function vn(i,t){return J(i,Wi),i.state=J({},i.state,t),typeof i.handleStateChanged=="function"&&_t(i)&&i.on("statechanged",i.handleStateChanged),i}function ae(i){return typeof i!="string"?i:i.charAt(0).toUpperCase()+i.slice(1)}function Ui(i,t){return ae(i)===ae(t)}function ie(){for(var i={},t=arguments.length,r=Array(t),e=0;e<t;e++)r[e]=arguments[e];return r.forEach(function(n){n&&se(n,function(s,a){if(!we(s)){i[a]=s;return}we(i[a])||(i[a]={}),i[a]=ie(i[a],s)})}),i}var E=function(){function i(t,r,e){if(b(this,i),!t&&this.play?this.player_=t=this:this.player_=t,this.options_=ie({},this.options_),r=this.options_=ie(this.options_,r),this.id_=r.id||r.el&&r.el.id,!this.id_){var n=t&&t.id&&t.id()||"no_player";this.id_=n+"_component_"+De()}this.name_=r.name||null,r.el?this.el_=r.el:r.createEl!==!1&&(this.el_=this.createEl()),r.evented!==!1&&Wt(this,{eventBusKey:this.el_?"el_":null}),vn(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},r.initChildren!==!1&&this.initChildren(),this.ready(e),r.reportTouchActivity!==!1&&this.enableTouchActivity()}return i.prototype.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var r=this.children_.length-1;r>=0;r--)this.children_[r].dispose&&this.children_[r].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),on(this.el_),this.el_=null),this.player_=null},i.prototype.player=function(){return this.player_},i.prototype.options=function(r){return K.warn("this.options() has been deprecated and will be moved to the constructor in 6.0"),r?(this.options_=ie(this.options_,r),this.options_):this.options_},i.prototype.el=function(){return this.el_},i.prototype.createEl=function(r,e,n){return le(r,e,n)},i.prototype.localize=function(r,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r,s=this.player_.language&&this.player_.language(),a=this.player_.languages&&this.player_.languages(),o=a&&a[s],l=s&&s.split("-")[0],f=a&&a[l],g=n;return o&&o[r]?g=o[r]:f&&f[r]&&(g=f[r]),e&&(g=g.replace(/\{(\d+)\}/g,function(y,x){var N=e[x-1],Z=N;return typeof N>"u"&&(Z=y),Z})),g},i.prototype.contentEl=function(){return this.contentEl_||this.el_},i.prototype.id=function(){return this.id_},i.prototype.name=function(){return this.name_},i.prototype.children=function(){return this.children_},i.prototype.getChildById=function(r){return this.childIndex_[r]},i.prototype.getChild=function(r){if(r)return r=ae(r),this.childNameIndex_[r]},i.prototype.addChild=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.children_.length,s=void 0,a=void 0;if(typeof r=="string"){a=ae(r);var o=e.componentClass||a;e.name=a;var l=i.getComponent(o);if(!l)throw new Error("Component "+o+" does not exist");if(typeof l!="function")return null;s=new l(this.player_||this,e)}else s=r;if(this.children_.splice(n,0,s),typeof s.id=="function"&&(this.childIndex_[s.id()]=s),a=a||s.name&&ae(s.name()),a&&(this.childNameIndex_[a]=s),typeof s.el=="function"&&s.el()){var f=this.contentEl().children,g=f[n]||null;this.contentEl().insertBefore(s.el(),g)}return s},i.prototype.removeChild=function(r){if(typeof r=="string"&&(r=this.getChild(r)),!(!r||!this.children_)){for(var e=!1,n=this.children_.length-1;n>=0;n--)if(this.children_[n]===r){e=!0,this.children_.splice(n,1);break}if(e){this.childIndex_[r.id()]=null,this.childNameIndex_[r.name()]=null;var s=r.el();s&&s.parentNode===this.contentEl()&&this.contentEl().removeChild(r.el())}}},i.prototype.initChildren=function(){var r=this,e=this.options_.children;if(e){var n=this.options_,s=function(f){var g=f.name,y=f.opts;if(n[g]!==void 0&&(y=n[g]),y!==!1){y===!0&&(y={}),y.playerOptions=r.options_.playerOptions;var x=r.addChild(g,y);x&&(r[g]=x)}},a=void 0,o=i.getComponent("Tech");Array.isArray(e)?a=e:a=Object.keys(e),a.concat(Object.keys(this.options_).filter(function(l){return!a.some(function(f){return typeof f=="string"?l===f:l===f.name})})).map(function(l){var f=void 0,g=void 0;return typeof l=="string"?(f=l,g=e[f]||r.options_[f]||{}):(f=l.name,g=l),{name:f,opts:g}}).filter(function(l){var f=i.getComponent(l.opts.componentClass||ae(l.name));return f&&!o.isTech(f)}).forEach(s)}},i.prototype.buildCSSClass=function(){return""},i.prototype.ready=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r){if(!this.isReady_){this.readyQueue_=this.readyQueue_||[],this.readyQueue_.push(r);return}e?r.call(this):this.setTimeout(r,1)}},i.prototype.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var r=this.readyQueue_;this.readyQueue_=[],r&&r.length>0&&r.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},i.prototype.$=function(r,e){return Pe(r,e||this.contentEl())},i.prototype.$$=function(r,e){return nn(r,e||this.contentEl())},i.prototype.hasClass=function(r){return Ie(this.el_,r)},i.prototype.addClass=function(r){je(this.el_,r)},i.prototype.removeClass=function(r){rt(this.el_,r)},i.prototype.toggleClass=function(r,e){Xr(this.el_,r,e)},i.prototype.show=function(){this.removeClass("vjs-hidden")},i.prototype.hide=function(){this.addClass("vjs-hidden")},i.prototype.lockShowing=function(){this.addClass("vjs-lock-showing")},i.prototype.unlockShowing=function(){this.removeClass("vjs-lock-showing")},i.prototype.getAttribute=function(r){return Yr(this.el_,r)},i.prototype.setAttribute=function(r,e){nt(this.el_,r,e)},i.prototype.removeAttribute=function(r){yt(this.el_,r)},i.prototype.width=function(r,e){return this.dimension("width",r,e)},i.prototype.height=function(r,e){return this.dimension("height",r,e)},i.prototype.dimensions=function(r,e){this.width(r,!0),this.height(e)},i.prototype.dimension=function(r,e,n){if(e!==void 0){(e===null||e!==e)&&(e=0),(""+e).indexOf("%")!==-1||(""+e).indexOf("px")!==-1?this.el_.style[r]=e:e==="auto"?this.el_.style[r]="":this.el_.style[r]=e+"px",n||this.trigger("componentresize");return}if(!this.el_)return 0;var s=this.el_.style[r],a=s.indexOf("px");return parseInt(a!==-1?s.slice(0,a):this.el_["offset"+ae(r)],10)},i.prototype.currentDimension=function(r){var e=0;if(r!=="width"&&r!=="height")throw new Error("currentDimension only accepts width or height value");if(typeof d.getComputedStyle=="function"){var n=d.getComputedStyle(this.el_);e=n.getPropertyValue(r)||n[r]}if(e=parseFloat(e),e===0){var s="offset"+ae(r);e=this.el_[s]}return e},i.prototype.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},i.prototype.currentWidth=function(){return this.currentDimension("width")},i.prototype.currentHeight=function(){return this.currentDimension("height")},i.prototype.focus=function(){this.el_.focus()},i.prototype.blur=function(){this.el_.blur()},i.prototype.emitTapEvents=function(){var r=0,e=null,n=10,s=200,a=void 0;this.on("touchstart",function(l){l.touches.length===1&&(e={pageX:l.touches[0].pageX,pageY:l.touches[0].pageY},r=new Date().getTime(),a=!0)}),this.on("touchmove",function(l){if(l.touches.length>1)a=!1;else if(e){var f=l.touches[0].pageX-e.pageX,g=l.touches[0].pageY-e.pageY,y=Math.sqrt(f*f+g*g);y>n&&(a=!1)}});var o=function(){a=!1};this.on("touchleave",o),this.on("touchcancel",o),this.on("touchend",function(l){if(e=null,a===!0){var f=new Date().getTime()-r;f<s&&(l.preventDefault(),this.trigger("tap"))}})},i.prototype.enableTouchActivity=function(){if(!(!this.player()||!this.player().reportUserActivity)){var r=U(this.player(),this.player().reportUserActivity),e=void 0;this.on("touchstart",function(){r(),this.clearInterval(e),e=this.setInterval(r,250)});var n=function(a){r(),this.clearInterval(e)};this.on("touchmove",r),this.on("touchend",n),this.on("touchcancel",n)}},i.prototype.setTimeout=function(r,e){var n=this,s,a;return r=U(this,r),s=d.setTimeout(function(){n.off("dispose",a),r()},e),a=function(){return n.clearTimeout(s)},a.guid="vjs-timeout-"+s,this.on("dispose",a),s},i.prototype.clearTimeout=function(r){d.clearTimeout(r);var e=function(){};return e.guid="vjs-timeout-"+r,this.off("dispose",e),r},i.prototype.setInterval=function(r,e){var n=this;r=U(this,r);var s=d.setInterval(r,e),a=function(){return n.clearInterval(s)};return a.guid="vjs-interval-"+s,this.on("dispose",a),s},i.prototype.clearInterval=function(r){d.clearInterval(r);var e=function(){};return e.guid="vjs-interval-"+r,this.off("dispose",e),r},i.prototype.requestAnimationFrame=function(r){var e=this,n,s;return this.supportsRaf_?(r=U(this,r),n=d.requestAnimationFrame(function(){e.off("dispose",s),r()}),s=function(){return e.cancelAnimationFrame(n)},s.guid="vjs-raf-"+n,this.on("dispose",s),n):this.setTimeout(r,1e3/60)},i.prototype.cancelAnimationFrame=function(r){if(this.supportsRaf_){d.cancelAnimationFrame(r);var e=function(){};return e.guid="vjs-raf-"+r,this.off("dispose",e),r}return this.clearTimeout(r)},i.registerComponent=function(r,e){if(typeof r!="string"||!r)throw new Error('Illegal component name, "'+r+'"; must be a non-empty string.');var n=i.getComponent("Tech"),s=n&&n.isTech(e),a=i===e||i.prototype.isPrototypeOf(e.prototype);if(s||!a){var o=void 0;throw s?o="techs must be registered using Tech.registerTech()":o="must be a Component subclass",new Error('Illegal component, "'+r+'"; '+o+".")}r=ae(r),i.components_||(i.components_={});var l=i.getComponent("Player");if(r==="Player"&&l&&l.players){var f=l.players,g=Object.keys(f);if(f&&g.length>0&&g.map(function(y){return f[y]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return i.components_[r]=e,e},i.getComponent=function(r){if(r&&(r=ae(r),i.components_&&i.components_[r]))return i.components_[r]},i}();E.prototype.supportsRaf_=typeof d.requestAnimationFrame=="function"&&typeof d.cancelAnimationFrame=="function",E.registerComponent("Component",E);function qi(i,t,r){if(typeof t!="number"||t<0||t>r)throw new Error("Failed to execute '"+i+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+r+").")}function yn(i,t,r,e){return qi(i,e,r.length-1),r[e][t]}function Ut(i){return i===void 0||i.length===0?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:i.length,start:yn.bind(null,"start",0,i),end:yn.bind(null,"end",1,i)}}function Re(i,t){return Array.isArray(i)?Ut(i):i===void 0||t===void 0?Ut():Ut([[i,t]])}function mn(i,t){var r=0,e=void 0,n=void 0;if(!t)return 0;(!i||!i.length)&&(i=Re(0,0));for(var s=0;s<i.length;s++)e=i.start(s),n=i.end(s),n>t&&(n=t),r+=n-e;return r/t}for(var bt={},Tt=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Ki=Tt[0],Ct=void 0,St=0;St<Tt.length;St++)if(Tt[St][1]in u){Ct=Tt[St];break}if(Ct)for(var Et=0;Et<Ct.length;Et++)bt[Ki[Et]]=Ct[Et];function fe(i){if(i instanceof fe)return i;typeof i=="number"?this.code=i:typeof i=="string"?this.message=i:he(i)&&(typeof i.code=="number"&&(this.code=i.code),J(this,i)),this.message||(this.message=fe.defaultMessages[this.code]||"")}fe.prototype.code=0,fe.prototype.message="",fe.prototype.status=null,fe.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],fe.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var We=0;We<fe.errorTypes.length;We++)fe[fe.errorTypes[We]]=We,fe.prototype[fe.errorTypes[We]]=We;function _n(i){return i!=null&&typeof i.then=="function"}function ut(i){_n(i)&&i.then(null,function(t){})}var qt=function(t){var r=["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,n,s){return t[n]&&(e[n]=t[n]),e},{cues:t.cues&&Array.prototype.map.call(t.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})});return r},Xi=function(t){var r=t.$$("track"),e=Array.prototype.map.call(r,function(s){return s.track}),n=Array.prototype.map.call(r,function(s){var a=qt(s.track);return s.src&&(a.src=s.src),a});return n.concat(Array.prototype.filter.call(t.textTracks(),function(s){return e.indexOf(s)===-1}).map(qt))},Gi=function(t,r){return t.forEach(function(e){var n=r.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(s){return n.addCue(s)})}),r.textTracks()},bn={textTracksToJson:Xi,jsonToTextTracks:Gi,trackToJson_:qt},Kt="vjs-modal-dialog",Yi=27,Ue=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.opened_=n.hasBeenOpened_=n.hasBeenFilled_=!1,n.closeable(!n.options_.uncloseable),n.content(n.options_.content),n.contentEl_=le("div",{className:Kt+"-content"},{role:"document"}),n.descEl_=le("p",{className:Kt+"-description vjs-control-text",id:n.el().getAttribute("aria-describedby")}),tt(n.descEl_,n.description()),n.el_.appendChild(n.descEl_),n.el_.appendChild(n.contentEl_),n}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},t.prototype.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,i.prototype.dispose.call(this)},t.prototype.buildCSSClass=function(){return Kt+" vjs-hidden "+i.prototype.buildCSSClass.call(this)},t.prototype.handleKeyPress=function(e){e.which===Yi&&this.closeable()&&this.close()},t.prototype.label=function(){return this.localize(this.options_.label||"Modal Window")},t.prototype.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},t.prototype.open=function(){if(!this.opened_){var e=this.player();this.trigger("beforemodalopen"),this.opened_=!0,(this.options_.fillAlways||!this.hasBeenOpened_&&!this.hasBeenFilled_)&&this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.closeable()&&this.on(this.el_.ownerDocument,"keydown",U(this,this.handleKeyPress)),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},t.prototype.opened=function(e){return typeof e=="boolean"&&this[e?"open":"close"](),this.opened_},t.prototype.close=function(){if(this.opened_){var e=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.closeable()&&this.off(this.el_.ownerDocument,"keydown",U(this,this.handleKeyPress)),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},t.prototype.closeable=function(e){if(typeof e=="boolean"){var n=this.closeable_=!!e,s=this.getChild("closeButton");if(n&&!s){var a=this.contentEl_;this.contentEl_=this.el_,s=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=a,this.on(s,"close",this.close)}!n&&s&&(this.off(s,"close",this.close),this.removeChild(s),s.dispose())}return this.closeable_},t.prototype.fill=function(){this.fillWith(this.content())},t.prototype.fillWith=function(e){var n=this.contentEl(),s=n.parentNode,a=n.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,s.removeChild(n),this.empty(),rn(n,e),this.trigger("modalfill"),a?s.insertBefore(n,a):s.appendChild(n);var o=this.getChild("closeButton");o&&s.appendChild(o.el_)},t.prototype.empty=function(){this.trigger("beforemodalempty"),Nt(this.contentEl()),this.trigger("modalempty")},t.prototype.content=function(e){return typeof e<"u"&&(this.content_=e),this.content_},t.prototype.conditionalFocus_=function(){var e=u.activeElement,n=this.player_.el_;this.previouslyActiveEl_=null,(n.contains(e)||n===e)&&(this.previouslyActiveEl_=e,this.focus(),this.on(u,"keydown",this.handleKeyDown))},t.prototype.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null),this.off(u,"keydown",this.handleKeyDown)},t.prototype.handleKeyDown=function(e){if(e.which===9){for(var n=this.focusableEls_(),s=this.el_.querySelector(":focus"),a=void 0,o=0;o<n.length;o++)if(s===n[o]){a=o;break}u.activeElement===this.el_&&(a=0),e.shiftKey&&a===0?(n[n.length-1].focus(),e.preventDefault()):!e.shiftKey&&a===n.length-1&&(n[0].focus(),e.preventDefault())}},t.prototype.focusableEls_=function(){var e=this.el_.querySelectorAll("*");return Array.prototype.filter.call(e,function(n){return(n instanceof d.HTMLAnchorElement||n instanceof d.HTMLAreaElement)&&n.hasAttribute("href")||(n instanceof d.HTMLInputElement||n instanceof d.HTMLSelectElement||n instanceof d.HTMLTextAreaElement||n instanceof d.HTMLButtonElement)&&!n.hasAttribute("disabled")||n instanceof d.HTMLIFrameElement||n instanceof d.HTMLObjectElement||n instanceof d.HTMLEmbedElement||n.hasAttribute("tabindex")&&n.getAttribute("tabindex")!==-1||n.hasAttribute("contenteditable")})},t}(E);Ue.prototype.options_={pauseOnOpen:!0,temporary:!0},E.registerComponent("ModalDialog",Ue);var me=function(i){m(t,i);function t(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;b(this,t);var s=v(this,i.call(this));if(!n&&(n=s,h)){n=u.createElement("custom");for(var a in t.prototype)a!=="constructor"&&(n[a]=t.prototype[a])}n.tracks_=[],Object.defineProperty(n,"length",{get:function(){return this.tracks_.length}});for(var o=0;o<r.length;o++)n.addTrack(r[o]);return e=n,v(s,e)}return t.prototype.addTrack=function(e){var n=this.tracks_.length;""+n in this||Object.defineProperty(this,n,{get:function(){return this.tracks_[n]}}),this.tracks_.indexOf(e)===-1&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack"}))},t.prototype.removeTrack=function(e){for(var n=void 0,s=0,a=this.length;s<a;s++)if(this[s]===e){n=this[s],n.off&&n.off(),this.tracks_.splice(s,1);break}n&&this.trigger({track:n,type:"removetrack"})},t.prototype.getTrackById=function(e){for(var n=null,s=0,a=this.length;s<a;s++){var o=this[s];if(o.id===e){n=o;break}}return n},t}(ce);me.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"};for(var Ji in me.prototype.allowedEvents_)me.prototype["on"+Ji]=null;var Xt=function(t,r){for(var e=0;e<t.length;e++)!Object.keys(t[e]).length||r.id===t[e].id||(t[e].enabled=!1)},Qi=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];b(this,t);for(var s=void 0,a=n.length-1;a>=0;a--)if(n[a].enabled){Xt(n,n[a]);break}if(h){s=u.createElement("custom");for(var o in me.prototype)o!=="constructor"&&(s[o]=me.prototype[o]);for(var l in t.prototype)l!=="constructor"&&(s[l]=t.prototype[l])}return s=(r=v(this,i.call(this,n,s)),r),s.changing_=!1,e=s,v(r,e)}return t.prototype.addTrack=function(e){var n=this;e.enabled&&Xt(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("enabledchange",function(){n.changing_||(n.changing_=!0,Xt(n,e),n.changing_=!1,n.trigger("change"))})},t}(me),Gt=function(t,r){for(var e=0;e<t.length;e++)!Object.keys(t[e]).length||r.id===t[e].id||(t[e].selected=!1)},Zi=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];b(this,t);for(var s=void 0,a=n.length-1;a>=0;a--)if(n[a].selected){Gt(n,n[a]);break}if(h){s=u.createElement("custom");for(var o in me.prototype)o!=="constructor"&&(s[o]=me.prototype[o]);for(var l in t.prototype)l!=="constructor"&&(s[l]=t.prototype[l])}return s=(r=v(this,i.call(this,n,s)),r),s.changing_=!1,Object.defineProperty(s,"selectedIndex",{get:function(){for(var g=0;g<this.length;g++)if(this[g].selected)return g;return-1},set:function(){}}),e=s,v(r,e)}return t.prototype.addTrack=function(e){var n=this;e.selected&&Gt(this,e),i.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("selectedchange",function(){n.changing_||(n.changing_=!0,Gt(n,e),n.changing_=!1,n.trigger("change"))})},t}(me),Tn=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];b(this,t);var s=void 0;if(h){s=u.createElement("custom");for(var a in me.prototype)a!=="constructor"&&(s[a]=me.prototype[a]);for(var o in t.prototype)o!=="constructor"&&(s[o]=t.prototype[o])}return s=(r=v(this,i.call(this,n,s)),r),e=s,v(r,e)}return t.prototype.addTrack=function(e){i.prototype.addTrack.call(this,e),e.addEventListener("modechange",U(this,function(){this.trigger("change")}));var n=["metadata","chapters"];n.indexOf(e.kind)===-1&&e.addEventListener("modechange",U(this,function(){this.trigger("selectedlanguagechange")}))},t}(me),es=function(){function i(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];b(this,i);var r=this;if(h){r=u.createElement("custom");for(var e in i.prototype)e!=="constructor"&&(r[e]=i.prototype[e])}r.trackElements_=[],Object.defineProperty(r,"length",{get:function(){return this.trackElements_.length}});for(var n=0,s=t.length;n<s;n++)r.addTrackElement_(t[n]);if(h)return r}return i.prototype.addTrackElement_=function(r){var e=this.trackElements_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.trackElements_[e]}}),this.trackElements_.indexOf(r)===-1&&this.trackElements_.push(r)},i.prototype.getTrackElementByTrack_=function(r){for(var e=void 0,n=0,s=this.trackElements_.length;n<s;n++)if(r===this.trackElements_[n].track){e=this.trackElements_[n];break}return e},i.prototype.removeTrackElement_=function(r){for(var e=0,n=this.trackElements_.length;e<n;e++)if(r===this.trackElements_[e]){this.trackElements_.splice(e,1);break}},i}(),Cn=function(){function i(t){b(this,i);var r=this;if(h){r=u.createElement("custom");for(var e in i.prototype)e!=="constructor"&&(r[e]=i.prototype[e])}if(i.prototype.setCues_.call(r,t),Object.defineProperty(r,"length",{get:function(){return this.length_}}),h)return r}return i.prototype.setCues_=function(r){var e=this.length||0,n=0,s=r.length;this.cues_=r,this.length_=r.length;var a=function(l){""+l in this||Object.defineProperty(this,""+l,{get:function(){return this.cues_[l]}})};if(e<s)for(n=e;n<s;n++)a.call(this,n)},i.prototype.getCueById=function(r){for(var e=null,n=0,s=this.length;n<s;n++){var a=this[n];if(a.id===r){e=a;break}}return e},i}(),ts={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},rs={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},ns={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},Sn={disabled:"disabled",hidden:"hidden",showing:"showing"},Yt=function(i){m(t,i);function t(){var r,e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};b(this,t);var n=v(this,i.call(this)),s=n;if(h){s=u.createElement("custom");for(var a in t.prototype)a!=="constructor"&&(s[a]=t.prototype[a])}var o={id:e.id||"vjs_track_"+De(),kind:e.kind||"",label:e.label||"",language:e.language||""},l=function(y){Object.defineProperty(s,y,{get:function(){return o[y]},set:function(){}})};for(var f in o)l(f);return r=s,v(n,r)}return t}(ce),Jt=function(t){var r=["protocol","hostname","port","pathname","search","hash","host"],e=u.createElement("a");e.href=t;var n=e.host===""&&e.protocol!=="file:",s=void 0;n&&(s=u.createElement("div"),s.innerHTML='<a href="'+t+'"></a>',e=s.firstChild,s.setAttribute("style","display:none; position:absolute;"),u.body.appendChild(s));for(var a={},o=0;o<r.length;o++)a[r[o]]=e[r[o]];return a.protocol==="http:"&&(a.host=a.host.replace(/:80$/,"")),a.protocol==="https:"&&(a.host=a.host.replace(/:443$/,"")),a.protocol||(a.protocol=d.location.protocol),n&&u.body.removeChild(s),a},En=function(t){if(!t.match(/^https?:\/\//)){var r=u.createElement("div");r.innerHTML='<a href="'+t+'">x</a>',t=r.firstChild.href}return t},Qt=function(t){if(typeof t=="string"){var r=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/i,e=r.exec(t);if(e)return e.pop().toLowerCase()}return""},kt=function(t){var r=d.location,e=Jt(t),n=e.protocol===":"?r.protocol:e.protocol,s=n+e.host!==r.protocol+r.host;return s},is=(Object.freeze||Object)({parseUrl:Jt,getAbsoluteURL:En,getFileExtension:Qt,isCrossOrigin:kt}),kn=function(t,r){var e=new d.WebVTT.Parser(d,d.vttjs,d.WebVTT.StringDecoder()),n=[];e.oncue=function(s){r.addCue(s)},e.onparsingerror=function(s){n.push(s)},e.onflush=function(){r.trigger({type:"loadeddata",target:r})},e.parse(t),n.length>0&&(d.console&&d.console.groupCollapsed&&d.console.groupCollapsed("Text Track parsing errors for "+r.src),n.forEach(function(s){return K.error(s)}),d.console&&d.console.groupEnd&&d.console.groupEnd()),e.flush()},ss=function(t,r){var e={uri:t},n=kt(t);n&&(e.cors=n),P(e,U(this,function(s,a,o){if(s)return K.error(s,a);if(r.loaded_=!0,typeof d.WebVTT!="function"){if(r.tech_){var l=function(){return kn(o,r)};r.tech_.on("vttjsloaded",l),r.tech_.on("vttjserror",function(){K.error("vttjs failed to load, stopping trying to process "+r.src),r.tech_.off("vttjsloaded",l)})}}else kn(o,r)}))},ct=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(b(this,t),!n.tech)throw new Error("A tech was not provided.");var s=ie(n,{kind:ns[n.kind]||"subtitles",language:n.language||n.srclang||""}),a=Sn[s.mode]||"disabled",o=s.default;(s.kind==="metadata"||s.kind==="chapters")&&(a="hidden");var l=(r=v(this,i.call(this,s)),r);if(l.tech_=s.tech,h)for(var f in t.prototype)f!=="constructor"&&(l[f]=t.prototype[f]);l.cues_=[],l.activeCues_=[];var g=new Cn(l.cues_),y=new Cn(l.activeCues_),x=!1,N=U(l,function(){this.activeCues=this.activeCues,x&&(this.trigger("cuechange"),x=!1)});return a!=="disabled"&&l.tech_.ready(function(){l.tech_.on("timeupdate",N)},!0),Object.defineProperty(l,"default",{get:function(){return o},set:function(){}}),Object.defineProperty(l,"mode",{get:function(){return a},set:function(Se){var Te=this;Sn[Se]&&(a=Se,a!=="disabled"?this.tech_.ready(function(){Te.tech_.on("timeupdate",N)},!0):this.tech_.off("timeupdate",N),this.trigger("modechange"))}}),Object.defineProperty(l,"cues",{get:function(){return this.loaded_?g:null},set:function(){}}),Object.defineProperty(l,"activeCues",{get:function(){if(!this.loaded_)return null;if(this.cues.length===0)return y;for(var Se=this.tech_.currentTime(),Te=[],Qe=0,Zs=this.cues.length;Qe<Zs;Qe++){var Oe=this.cues[Qe];(Oe.startTime<=Se&&Oe.endTime>=Se||Oe.startTime===Oe.endTime&&Oe.startTime<=Se&&Oe.startTime+.5>=Se)&&Te.push(Oe)}if(x=!1,Te.length!==this.activeCues_.length)x=!0;else for(var Ar=0;Ar<Te.length;Ar++)this.activeCues_.indexOf(Te[Ar])===-1&&(x=!0);return this.activeCues_=Te,y.setCues_(this.activeCues_),y},set:function(){}}),s.src?(l.src=s.src,ss(s.src,l)):l.loaded_=!0,e=l,v(r,e)}return t.prototype.addCue=function(e){var n=e;if(d.vttjs&&!(e instanceof d.vttjs.VTTCue)){n=new d.vttjs.VTTCue(e.startTime,e.endTime,e.text);for(var s in e)s in n||(n[s]=e[s]);n.id=e.id,n.originalCue_=e}for(var a=this.tech_.textTracks(),o=0;o<a.length;o++)a[o]!==this&&a[o].removeCue(n);this.cues_.push(n),this.cues.setCues_(this.cues_)},t.prototype.removeCue=function(e){for(var n=this.cues_.length;n--;){var s=this.cues_[n];if(s===e||s.originalCue_&&s.originalCue_===e){this.cues_.splice(n,1),this.cues.setCues_(this.cues_);break}}},t}(Yt);ct.prototype.allowedEvents_={cuechange:"cuechange"};var xn=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};b(this,t);var s=ie(n,{kind:rs[n.kind]||""}),a=(r=v(this,i.call(this,s)),r),o=!1;if(h)for(var l in t.prototype)l!=="constructor"&&(a[l]=t.prototype[l]);return Object.defineProperty(a,"enabled",{get:function(){return o},set:function(g){typeof g!="boolean"||g===o||(o=g,this.trigger("enabledchange"))}}),s.enabled&&(a.enabled=s.enabled),a.loaded_=!0,e=a,v(r,e)}return t}(Yt),wn=function(i){m(t,i);function t(){var r,e,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};b(this,t);var s=ie(n,{kind:ts[n.kind]||""}),a=(r=v(this,i.call(this,s)),r),o=!1;if(h)for(var l in t.prototype)l!=="constructor"&&(a[l]=t.prototype[l]);return Object.defineProperty(a,"selected",{get:function(){return o},set:function(g){typeof g!="boolean"||g===o||(o=g,this.trigger("selectedchange"))}}),s.selected&&(a.selected=s.selected),e=a,v(r,e)}return t}(Yt),An=0,as=1,Pn=2,os=3,qe=function(i){m(t,i);function t(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};b(this,t);var e=v(this,i.call(this)),n=void 0,s=e;if(h){s=u.createElement("custom");for(var a in t.prototype)a!=="constructor"&&(s[a]=t.prototype[a])}var o=new ct(r);if(s.kind=o.kind,s.src=o.src,s.srclang=o.language,s.label=o.label,s.default=o.default,Object.defineProperty(s,"readyState",{get:function(){return n}}),Object.defineProperty(s,"track",{get:function(){return o}}),n=An,o.addEventListener("loadeddata",function(){n=Pn,s.trigger({type:"load",target:s})}),h){var l;return l=s,v(e,l)}return e}return t}(ce);qe.prototype.allowedEvents_={load:"load"},qe.NONE=An,qe.LOADING=as,qe.LOADED=Pn,qe.ERROR=os;var _e={audio:{ListClass:Qi,TrackClass:xn,capitalName:"Audio"},video:{ListClass:Zi,TrackClass:wn,capitalName:"Video"},text:{ListClass:Tn,TrackClass:ct,capitalName:"Text"}};Object.keys(_e).forEach(function(i){_e[i].getterName=i+"Tracks",_e[i].privateName=i+"Tracks_"});var ht={remoteText:{ListClass:Tn,TrackClass:ct,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:es,TrackClass:qe,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},ge=ie(_e,ht);ht.names=Object.keys(ht),_e.names=Object.keys(_e),ge.names=[].concat(ht.names).concat(_e.names);function ls(i,t,r,e){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{},s=i.textTracks();n.kind=t,r&&(n.label=r),e&&(n.language=e),n.tech=i;var a=new ge.text.TrackClass(n);return s.addTrack(a),a}var re=function(i){m(t,i);function t(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){};b(this,t),r.reportTouchActivity=!1;var n=v(this,i.call(this,null,r,e));return n.hasStarted_=!1,n.on("playing",function(){this.hasStarted_=!0}),n.on("loadstart",function(){this.hasStarted_=!1}),ge.names.forEach(function(s){var a=ge[s];r&&r[a.getterName]&&(n[a.privateName]=r[a.getterName])}),n.featuresProgressEvents||n.manualProgressOn(),n.featuresTimeupdateEvents||n.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(s){r["native"+s+"Tracks"]===!1&&(n["featuresNative"+s+"Tracks"]=!1)}),r.nativeCaptions===!1||r.nativeTextTracks===!1?n.featuresNativeTextTracks=!1:(r.nativeCaptions===!0||r.nativeTextTracks===!0)&&(n.featuresNativeTextTracks=!0),n.featuresNativeTextTracks||n.emulateTextTracks(),n.autoRemoteTextTracks_=new ge.text.ListClass,n.initTrackListeners(),r.nativeControlsForTouch||n.emitTapEvents(),n.constructor&&(n.name_=n.constructor.name||"Unknown Tech"),n}return t.prototype.triggerSourceset=function(e){var n=this;this.isReady_||this.one("ready",function(){return n.setTimeout(function(){return n.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},t.prototype.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},t.prototype.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},t.prototype.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval(U(this,function(){var n=this.bufferedPercent();this.bufferedPercent_!==n&&this.trigger("progress"),this.bufferedPercent_=n,n===1&&this.stopTrackingProgress()}),500)},t.prototype.onDurationChange=function(e){this.duration_=this.duration()},t.prototype.buffered=function(){return Re(0,0)},t.prototype.bufferedPercent=function(){return mn(this.buffered(),this.duration_)},t.prototype.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},t.prototype.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},t.prototype.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},t.prototype.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},t.prototype.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},t.prototype.dispose=function(){this.clearTracks(_e.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),i.prototype.dispose.call(this)},t.prototype.clearTracks=function(e){var n=this;e=[].concat(e),e.forEach(function(s){for(var a=n[s+"Tracks"]()||[],o=a.length;o--;){var l=a[o];s==="text"&&n.removeRemoteTextTrack(l),a.removeTrack(l)}})},t.prototype.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],n=e.length;n--;){var s=e[n];this.removeRemoteTextTrack(s)}},t.prototype.reset=function(){},t.prototype.error=function(e){return e!==void 0&&(this.error_=new fe(e),this.trigger("error")),this.error_},t.prototype.played=function(){return this.hasStarted_?Re(0,0):Re()},t.prototype.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},t.prototype.initTrackListeners=function(){var e=this;_e.names.forEach(function(n){var s=_e[n],a=function(){e.trigger(n+"trackchange")},o=e[s.getterName]();o.addEventListener("removetrack",a),o.addEventListener("addtrack",a),e.on("dispose",function(){o.removeEventListener("removetrack",a),o.removeEventListener("addtrack",a)})})},t.prototype.addWebVttScript_=function(){var e=this;if(!d.WebVTT)if(u.body.contains(this.el())){if(!this.options_["vtt.js"]&&we(F)&&Object.keys(F).length>0){this.trigger("vttjsloaded");return}var n=u.createElement("script");n.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.12.4/vtt.min.js",n.onload=function(){e.trigger("vttjsloaded")},n.onerror=function(){e.trigger("vttjserror")},this.on("dispose",function(){n.onload=null,n.onerror=null}),d.WebVTT=!0,this.el().parentNode.appendChild(n)}else this.ready(this.addWebVttScript_)},t.prototype.emulateTextTracks=function(){var e=this,n=this.textTracks(),s=this.remoteTextTracks(),a=function(y){return n.addTrack(y.track)},o=function(y){return n.removeTrack(y.track)};s.on("addtrack",a),s.on("removetrack",o),this.addWebVttScript_();var l=function(){return e.trigger("texttrackchange")},f=function(){l();for(var y=0;y<n.length;y++){var x=n[y];x.removeEventListener("cuechange",l),x.mode==="showing"&&x.addEventListener("cuechange",l)}};f(),n.addEventListener("change",f),n.addEventListener("addtrack",f),n.addEventListener("removetrack",f),this.on("dispose",function(){s.off("addtrack",a),s.off("removetrack",o),n.removeEventListener("change",f),n.removeEventListener("addtrack",f),n.removeEventListener("removetrack",f);for(var g=0;g<n.length;g++){var y=n[g];y.removeEventListener("cuechange",l)}})},t.prototype.addTextTrack=function(e,n,s){if(!e)throw new Error("TextTrack kind is required but was not provided");return ls(this,e,n,s)},t.prototype.createRemoteTextTrack=function(e){var n=ie(e,{tech:this});return new ht.remoteTextEl.TrackClass(n)},t.prototype.addRemoteTextTrack=function(){var e=this,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},s=arguments[1],a=this.createRemoteTextTrack(n);return s!==!0&&s!==!1&&(K.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),s=!0),this.remoteTextTrackEls().addTrackElement_(a),this.remoteTextTracks().addTrack(a.track),s!==!0&&this.ready(function(){return e.autoRemoteTextTracks_.addTrack(a.track)}),a},t.prototype.removeRemoteTextTrack=function(e){var n=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(n),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},t.prototype.getVideoPlaybackQuality=function(){return{}},t.prototype.setPoster=function(){},t.prototype.playsinline=function(){},t.prototype.setPlaysinline=function(){},t.prototype.canPlayType=function(){return""},t.canPlayType=function(){return""},t.canPlaySource=function(e,n){return t.canPlayType(e.type)},t.isTech=function(e){return e.prototype instanceof t||e instanceof t||e===t},t.registerTech=function(e,n){if(t.techs_||(t.techs_={}),!t.isTech(n))throw new Error("Tech "+e+" must be a Tech");if(!t.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!t.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return e=ae(e),t.techs_[e]=n,e!=="Tech"&&t.defaultTechOrder_.push(e),n},t.getTech=function(e){if(e){if(e=ae(e),t.techs_&&t.techs_[e])return t.techs_[e];if(d&&d.videojs&&d.videojs[e])return K.warn("The "+e+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),d.videojs[e]}},t}(E);ge.names.forEach(function(i){var t=ge[i];re.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),re.prototype.featuresVolumeControl=!0,re.prototype.featuresMuteControl=!0,re.prototype.featuresFullscreenResize=!1,re.prototype.featuresPlaybackRate=!1,re.prototype.featuresProgressEvents=!1,re.prototype.featuresSourceset=!1,re.prototype.featuresTimeupdateEvents=!1,re.prototype.featuresNativeTextTracks=!1,re.withSourceHandlers=function(i){i.registerSourceHandler=function(r,e){var n=i.sourceHandlers;n||(n=i.sourceHandlers=[]),e===void 0&&(e=n.length),n.splice(e,0,r)},i.canPlayType=function(r){for(var e=i.sourceHandlers||[],n=void 0,s=0;s<e.length;s++)if(n=e[s].canPlayType(r),n)return n;return""},i.selectSourceHandler=function(r,e){for(var n=i.sourceHandlers||[],s=void 0,a=0;a<n.length;a++)if(s=n[a].canHandleSource(r,e),s)return n[a];return null},i.canPlaySource=function(r,e){var n=i.selectSourceHandler(r,e);return n?n.canHandleSource(r,e):""};var t=["seekable","seeking","duration"];t.forEach(function(r){var e=this[r];typeof e=="function"&&(this[r]=function(){return this.sourceHandler_&&this.sourceHandler_[r]?this.sourceHandler_[r].apply(this.sourceHandler_,arguments):e.apply(this,arguments)})},i.prototype),i.prototype.setSource=function(r){var e=i.selectSourceHandler(r,this.options_);e||(i.nativeSourceHandler?e=i.nativeSourceHandler:K.error("No source hander found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),e!==i.nativeSourceHandler&&(this.currentSource_=r),this.sourceHandler_=e.handleSource(r,this,this.options_),this.on("dispose",this.disposeSourceHandler)},i.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},E.registerComponent("Tech",re),re.registerTech("Tech",re),re.defaultTechOrder_=[];var Le={},Zt={},dt={};function us(i,t){Le[i]=Le[i]||[],Le[i].push(t)}function cs(i,t,r){i.setTimeout(function(){return Ne(t,Le[t.type],r,i)},1)}function hs(i,t){i.forEach(function(r){return r.setTech&&r.setTech(t)})}function ds(i,t,r){return i.reduceRight(er(r),t[r]())}function fs(i,t,r,e){return t[r](i.reduce(er(r),e))}function On(i,t,r){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,n="call"+ae(r),s=i.reduce(er(n),e),a=s===dt,o=a?null:t[r](s);return vs(i,r,o,a),o}var ps={buffered:1,currentTime:1,duration:1,seekable:1,played:1,paused:1},gs={setCurrentTime:1},In={play:1,pause:1};function er(i){return function(t,r){return t===dt?dt:r[i]?r[i](t):t}}function vs(i,t,r,e){for(var n=i.length-1;n>=0;n--){var s=i[n];s[t]&&s[t](e,r)}}function ys(i){Zt[i.id()]=null}function ms(i,t){var r=Zt[i.id()],e=null;if(r==null)return e=t(i),Zt[i.id()]=[[t,e]],e;for(var n=0;n<r.length;n++){var s=r[n],a=s[0],o=s[1];a===t&&(e=o)}return e===null&&(e=t(i),r.push([t,e])),e}function Ne(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments[2],e=arguments[3],n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:[],s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,a=t[0],o=t.slice(1);if(typeof a=="string")Ne(i,Le[a],r,e,n,s);else if(a){var l=ms(e,a);if(!l.setSource)return n.push(l),Ne(i,o,r,e,n,s);l.setSource(J({},i),function(f,g){if(f)return Ne(i,o,r,e,n,s);n.push(l),Ne(g,i.type===g.type?o:Le[g.type],r,e,n,s)})}else o.length?Ne(i,o,r,e,n,s):s?r(i,n):Ne(i,Le["*"],r,e,n,!0)}var _s={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL"},jn=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=Qt(t),e=_s[r.toLowerCase()];return e||""},bs=function(t,r){if(!r)return"";if(t.cache_.source.src===r&&t.cache_.source.type)return t.cache_.source.type;var e=t.cache_.sources.filter(function(o){return o.src===r});if(e.length)return e[0].type;for(var n=t.$$("source"),s=0;s<n.length;s++){var a=n[s];if(a.type&&a.src&&a.src===r)return a.type}return jn(r)},Ts=function i(t){if(Array.isArray(t)){var r=[];t.forEach(function(e){e=i(e),Array.isArray(e)?r=r.concat(e):he(e)&&r.push(e)}),t=r}else typeof t=="string"&&t.trim()?t=[Dn({src:t})]:he(t)&&typeof t.src=="string"&&t.src&&t.src.trim()?t=[Dn(t)]:t=[];return t};function Dn(i){var t=jn(i.src);return!i.type&&t&&(i.type=t),i}var Cs=function(i){m(t,i);function t(r,e,n){b(this,t);var s=ie({createEl:!1},e),a=v(this,i.call(this,r,s,n));if(!e.playerOptions.sources||e.playerOptions.sources.length===0)for(var o=0,l=e.playerOptions.techOrder;o<l.length;o++){var f=ae(l[o]),g=re.getTech(f);if(f||(g=E.getComponent(f)),g&&g.isSupported()){r.loadTech_(f);break}}else r.src(e.playerOptions.sources);return a}return t}(E);E.registerComponent("MediaLoader",Cs);var xt=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.emitTapEvents(),n.enable(),n}return t.prototype.createEl=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"div",n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};n=J({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},n),e==="button"&&K.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),s=J({role:"button"},s),this.tabIndex_=n.tabIndex;var a=i.prototype.createEl.call(this,e,n,s);return this.createControlTextEl(a),a},t.prototype.dispose=function(){this.controlTextEl_=null,i.prototype.dispose.call(this)},t.prototype.createControlTextEl=function(e){return this.controlTextEl_=le("span",{className:"vjs-control-text"},{"aria-live":"polite"}),e&&e.appendChild(this.controlTextEl_),this.controlText(this.controlText_,e),this.controlTextEl_},t.prototype.controlText=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.el();if(e===void 0)return this.controlText_||"Need Text";var s=this.localize(e);this.controlText_=e,tt(this.controlTextEl_,s),this.nonIconControl||n.setAttribute("title",s)},t.prototype.buildCSSClass=function(){return"vjs-control vjs-button "+i.prototype.buildCSSClass.call(this)},t.prototype.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),typeof this.tabIndex_<"u"&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur))},t.prototype.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),typeof this.tabIndex_<"u"&&this.el_.removeAttribute("tabIndex"),this.off(["tap","click"],this.handleClick),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur)},t.prototype.handleClick=function(e){},t.prototype.handleFocus=function(e){Ce(u,"keydown",U(this,this.handleKeyPress))},t.prototype.handleKeyPress=function(e){e.which===32||e.which===13?(e.preventDefault(),this.trigger("click")):i.prototype.handleKeyPress&&i.prototype.handleKeyPress.call(this,e)},t.prototype.handleBlur=function(e){pe(u,"keydown",U(this,this.handleKeyPress))},t}(E);E.registerComponent("ClickableComponent",xt);var Ss=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.update(),r.on("posterchange",U(n,n.update)),n}return t.prototype.dispose=function(){this.player().off("posterchange",this.update),i.prototype.dispose.call(this)},t.prototype.createEl=function(){var e=le("div",{className:"vjs-poster",tabIndex:-1});return k||(this.fallbackImg_=le("img"),e.appendChild(this.fallbackImg_)),e},t.prototype.update=function(e){var n=this.player().poster();this.setSrc(n),n?this.show():this.hide()},t.prototype.setSrc=function(e){if(this.fallbackImg_)this.fallbackImg_.src=e;else{var n="";e&&(n='url("'+e+'")'),this.el_.style.backgroundImage=n}},t.prototype.handleClick=function(e){this.player_.controls()&&(this.player_.paused()?ut(this.player_.play()):this.player_.pause())},t}(xt);E.registerComponent("PosterImage",Ss);var be="#222",Mn="#ccc",Es={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function tr(i,t){var r=void 0;if(i.length===4)r=i[1]+i[1]+i[2]+i[2]+i[3]+i[3];else if(i.length===7)r=i.slice(1);else throw new Error("Invalid color code provided, "+i+"; must be formatted as e.g. #f0e or #f604e2.");return"rgba("+parseInt(r.slice(0,2),16)+","+parseInt(r.slice(2,4),16)+","+parseInt(r.slice(4,6),16)+","+t+")"}function rr(i,t,r){try{i.style[t]=r}catch{return}}var ks=function(i){m(t,i);function t(r,e,n){b(this,t);var s=v(this,i.call(this,r,e,n)),a=U(s,s.updateDisplay);return r.on("loadstart",U(s,s.toggleDisplay)),r.on("texttrackchange",a),r.on("loadstart",U(s,s.preselectTrack)),r.ready(U(s,function(){if(r.tech_&&r.tech_.featuresNativeTextTracks){this.hide();return}r.on("fullscreenchange",a),r.on("playerresize",a),d.addEventListener&&d.addEventListener("orientationchange",a),r.on("dispose",function(){return d.removeEventListener("orientationchange",a)});for(var o=this.options_.playerOptions.tracks||[],l=0;l<o.length;l++)this.player_.addRemoteTextTrack(o[l],!0);this.preselectTrack()})),s}return t.prototype.preselectTrack=function(){for(var e={captions:1,subtitles:1},n=this.player_.textTracks(),s=this.player_.cache_.selectedLanguage,a=void 0,o=void 0,l=void 0,f=0;f<n.length;f++){var g=n[f];s&&s.enabled&&s.language===g.language?g.kind===s.kind?l=g:l||(l=g):s&&!s.enabled?(l=null,a=null,o=null):g.default&&(g.kind==="descriptions"&&!a?a=g:g.kind in e&&!o&&(o=g))}l?l.mode="showing":o?o.mode="showing":a&&(a.mode="showing")},t.prototype.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},t.prototype.clearDisplay=function(){typeof d.WebVTT=="function"&&d.WebVTT.processCues(d,[],this.el_)},t.prototype.updateDisplay=function(){var e=this.player_.textTracks();this.clearDisplay();for(var n=null,s=null,a=e.length;a--;){var o=e[a];o.mode==="showing"&&(o.kind==="descriptions"?n=o:s=o)}s?(this.getAttribute("aria-live")!=="off"&&this.setAttribute("aria-live","off"),this.updateForTrack(s)):n&&(this.getAttribute("aria-live")!=="assertive"&&this.setAttribute("aria-live","assertive"),this.updateForTrack(n))},t.prototype.updateForTrack=function(e){if(!(typeof d.WebVTT!="function"||!e.activeCues)){for(var n=[],s=0;s<e.activeCues.length;s++)n.push(e.activeCues[s]);if(d.WebVTT.processCues(d,n,this.el_),!!this.player_.textTrackSettings)for(var a=this.player_.textTrackSettings.getValues(),o=n.length;o--;){var l=n[o];if(l){var f=l.displayState;if(a.color&&(f.firstChild.style.color=a.color),a.textOpacity&&rr(f.firstChild,"color",tr(a.color||"#fff",a.textOpacity)),a.backgroundColor&&(f.firstChild.style.backgroundColor=a.backgroundColor),a.backgroundOpacity&&rr(f.firstChild,"backgroundColor",tr(a.backgroundColor||"#000",a.backgroundOpacity)),a.windowColor&&(a.windowOpacity?rr(f,"backgroundColor",tr(a.windowColor,a.windowOpacity)):f.style.backgroundColor=a.windowColor),a.edgeStyle&&(a.edgeStyle==="dropshadow"?f.firstChild.style.textShadow="2px 2px 3px "+be+", 2px 2px 4px "+be+", 2px 2px 5px "+be:a.edgeStyle==="raised"?f.firstChild.style.textShadow="1px 1px "+be+", 2px 2px "+be+", 3px 3px "+be:a.edgeStyle==="depressed"?f.firstChild.style.textShadow="1px 1px "+Mn+", 0 1px "+Mn+", -1px -1px "+be+", 0 -1px "+be:a.edgeStyle==="uniform"&&(f.firstChild.style.textShadow="0 0 4px "+be+", 0 0 4px "+be+", 0 0 4px "+be+", 0 0 4px "+be)),a.fontPercent&&a.fontPercent!==1){var g=d.parseFloat(f.style.fontSize);f.style.fontSize=g*a.fontPercent+"px",f.style.height="auto",f.style.top="auto",f.style.bottom="2px"}a.fontFamily&&a.fontFamily!=="default"&&(a.fontFamily==="small-caps"?f.firstChild.style.fontVariant="small-caps":f.firstChild.style.fontFamily=Es[a.fontFamily])}}}},t}(E);E.registerComponent("TextTrackDisplay",ks);var xs=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){var e=this.player_.isAudio(),n=this.localize(e?"Audio Player":"Video Player"),s=le("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[n])}),a=i.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return a.appendChild(s),a},t}(E);E.registerComponent("LoadingSpinner",xs);var xe=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};e="button",n=J({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},n),s=J({type:"button"},s);var a=E.prototype.createEl.call(this,e,n,s);return this.createControlTextEl(a),a},t.prototype.addChild=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=this.constructor.name;return K.warn("Adding an actionable (user controllable) child to a Button ("+s+") is not supported; use a ClickableComponent instead."),E.prototype.addChild.call(this,e,n)},t.prototype.enable=function(){i.prototype.enable.call(this),this.el_.removeAttribute("disabled")},t.prototype.disable=function(){i.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},t.prototype.handleKeyPress=function(e){e.which===32||e.which===13||i.prototype.handleKeyPress.call(this,e)},t}(xt);E.registerComponent("Button",xe);var Rn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.mouseused_=!1,n.on("mousedown",n.handleMouseDown),n}return t.prototype.buildCSSClass=function(){return"vjs-big-play-button"},t.prototype.handleClick=function(e){var n=this.player_.play();if(this.mouseused_&&e.clientX&&e.clientY){ut(n);return}var s=this.player_.getChild("controlBar"),a=s&&s.getChild("playToggle");if(!a){this.player_.focus();return}var o=function(){return a.focus()};_n(n)?n.then(o,function(){}):this.setTimeout(o,1)},t.prototype.handleKeyPress=function(e){this.mouseused_=!1,i.prototype.handleKeyPress.call(this,e)},t.prototype.handleMouseDown=function(e){this.mouseused_=!0},t}(xe);Rn.prototype.controlText_="Play Video",E.registerComponent("BigPlayButton",Rn);var ws=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.controlText(e&&e.controlText||n.localize("Close")),n}return t.prototype.buildCSSClass=function(){return"vjs-close-button "+i.prototype.buildCSSClass.call(this)},t.prototype.handleClick=function(e){this.trigger({type:"close",bubbles:!1})},t}(xe);E.registerComponent("CloseButton",ws);var Ln=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"play",n.handlePlay),n.on(r,"pause",n.handlePause),n.on(r,"ended",n.handleEnded),n}return t.prototype.buildCSSClass=function(){return"vjs-play-control "+i.prototype.buildCSSClass.call(this)},t.prototype.handleClick=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.prototype.handleSeeked=function(e){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(e):this.handlePlay(e)},t.prototype.handlePlay=function(e){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},t.prototype.handlePause=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},t.prototype.handleEnded=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},t}(xe);Ln.prototype.controlText_="Play",E.registerComponent("PlayToggle",Ln);var Nn=function(t,r){t=t<0?0:t;var e=Math.floor(t%60),n=Math.floor(t/60%60),s=Math.floor(t/3600),a=Math.floor(r/60%60),o=Math.floor(r/3600);return(isNaN(t)||t===1/0)&&(s=n=e="-"),s=s>0||o>0?s+":":"",n=((s||a>=10)&&n<10?"0"+n:n)+":",e=e<10?"0"+e:e,s+n+e},nr=Nn;function As(i){nr=i}function Ps(){nr=Nn}var Ke=function(i){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:i;return nr(i,t)},Xe=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.throttledUpdateContent=ze(U(n,n.updateContent),25),n.on(r,"timeupdate",n.throttledUpdateContent),n}return t.prototype.createEl=function(e){var n=this.buildCSSClass(),s=i.prototype.createEl.call(this,"div",{className:n+" vjs-time-control vjs-control",innerHTML:'<span class="vjs-control-text">'+this.localize(this.labelText_)+" </span>"});return this.contentEl_=le("span",{className:n+"-display"},{"aria-live":"off"}),this.updateTextNode_(),s.appendChild(this.contentEl_),s},t.prototype.dispose=function(){this.contentEl_=null,this.textNode_=null,i.prototype.dispose.call(this)},t.prototype.updateTextNode_=function(){if(this.contentEl_){for(;this.contentEl_.firstChild;)this.contentEl_.removeChild(this.contentEl_.firstChild);this.textNode_=u.createTextNode(this.formattedTime_||this.formatTime_(0)),this.contentEl_.appendChild(this.textNode_)}},t.prototype.formatTime_=function(e){return Ke(e)},t.prototype.updateFormattedTime_=function(e){var n=this.formatTime_(e);n!==this.formattedTime_&&(this.formattedTime_=n,this.requestAnimationFrame(this.updateTextNode_))},t.prototype.updateContent=function(e){},t}(E);Xe.prototype.labelText_="Time",Xe.prototype.controlText_="Time",E.registerComponent("TimeDisplay",Xe);var ir=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"ended",n.handleEnded),n}return t.prototype.buildCSSClass=function(){return"vjs-current-time"},t.prototype.updateContent=function(e){var n=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateFormattedTime_(n)},t.prototype.handleEnded=function(e){this.player_.duration()&&this.updateFormattedTime_(this.player_.duration())},t}(Xe);ir.prototype.labelText_="Current Time",ir.prototype.controlText_="Current Time",E.registerComponent("CurrentTimeDisplay",ir);var sr=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"durationchange",n.updateContent),n.on(r,"loadedmetadata",n.throttledUpdateContent),n}return t.prototype.buildCSSClass=function(){return"vjs-duration"},t.prototype.updateContent=function(e){var n=this.player_.duration();n&&this.duration_!==n&&(this.duration_=n,this.updateFormattedTime_(n))},t}(Xe);sr.prototype.labelText_="Duration",sr.prototype.controlText_="Duration",E.registerComponent("DurationDisplay",sr);var Os=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"})},t}(E);E.registerComponent("TimeDivider",Os);var ar=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"durationchange",n.throttledUpdateContent),n.on(r,"ended",n.handleEnded),n}return t.prototype.buildCSSClass=function(){return"vjs-remaining-time"},t.prototype.formatTime_=function(e){return"-"+i.prototype.formatTime_.call(this,e)},t.prototype.updateContent=function(e){this.player_.duration()&&(this.player_.remainingTimeDisplay?this.updateFormattedTime_(this.player_.remainingTimeDisplay()):this.updateFormattedTime_(this.player_.remainingTime()))},t.prototype.handleEnded=function(e){this.player_.duration()&&this.updateFormattedTime_(0)},t}(Xe);ar.prototype.labelText_="Remaining Time",ar.prototype.controlText_="Remaining Time",E.registerComponent("RemainingTimeDisplay",ar);var Is=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.updateShowing(),n.on(n.player(),"durationchange",n.updateShowing),n}return t.prototype.createEl=function(){var e=i.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=le("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+" </span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},t.prototype.dispose=function(){this.contentEl_=null,i.prototype.dispose.call(this)},t.prototype.updateShowing=function(e){this.player().duration()===1/0?this.show():this.hide()},t}(E);E.registerComponent("LiveDisplay",Is);var or=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.bar=n.getChild(n.options_.barName),n.vertical(!!n.options_.vertical),n.enable(),n}return t.prototype.enabled=function(){return this.enabled_},t.prototype.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},t.prototype.disable=function(){if(this.enabled()){var e=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},t.prototype.createEl=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return n.className=n.className+" vjs-slider",n=J({tabIndex:0},n),s=J({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},s),i.prototype.createEl.call(this,e,n,s)},t.prototype.handleMouseDown=function(e){var n=this.bar.el_.ownerDocument;e.type==="mousedown"&&e.preventDefault(),e.type==="touchstart"&&!oe&&e.preventDefault(),Jr(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(n,"mousemove",this.handleMouseMove),this.on(n,"mouseup",this.handleMouseUp),this.on(n,"touchmove",this.handleMouseMove),this.on(n,"touchend",this.handleMouseUp),this.handleMouseMove(e)},t.prototype.handleMouseMove=function(e){},t.prototype.handleMouseUp=function(){var e=this.bar.el_.ownerDocument;Qr(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.update()},t.prototype.update=function(){if(this.el_){var e=this.getPercent(),n=this.bar;if(n){(typeof e!="number"||e!==e||e<0||e===1/0)&&(e=0);var s=(e*100).toFixed(2)+"%",a=n.el().style;return this.vertical()?a.height=s:a.width=s,e}}},t.prototype.calculateDistance=function(e){var n=Lt(this.el_,e);return this.vertical()?n.y:n.x},t.prototype.handleFocus=function(){this.on(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},t.prototype.handleKeyPress=function(e){e.which===37||e.which===40?(e.preventDefault(),this.stepBack()):(e.which===38||e.which===39)&&(e.preventDefault(),this.stepForward())},t.prototype.handleBlur=function(){this.off(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},t.prototype.handleClick=function(e){e.stopImmediatePropagation(),e.preventDefault()},t.prototype.vertical=function(e){if(e===void 0)return this.vertical_||!1;this.vertical_=!!e,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},t}(E);E.registerComponent("Slider",or);var js=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.partEls_=[],n.on(r,"progress",n.update),n}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Loaded")+"</span>: 0%</span>"})},t.prototype.dispose=function(){this.partEls_=null,i.prototype.dispose.call(this)},t.prototype.update=function(e){var n=this.player_.buffered(),s=this.player_.duration(),a=this.player_.bufferedEnd(),o=this.partEls_,l=function(Se,Te){var Qe=Se/Te||0;return(Qe>=1?1:Qe)*100+"%"};this.el_.style.width=l(a,s);for(var f=0;f<n.length;f++){var g=n.start(f),y=n.end(f),x=o[f];x||(x=this.el_.appendChild(le()),o[f]=x),x.style.left=l(g,a),x.style.width=l(y-g,a)}for(var N=o.length;N>n.length;N--)this.el_.removeChild(o[N-1]);o.length=n.length},t}(E);E.registerComponent("LoadProgressBar",js);var Ds=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"})},t.prototype.update=function(e,n,s){var a=it(this.el_),o=it(this.player_.el()),l=e.width*n;if(!(!o||!a)){var f=e.left-o.left+l,g=e.width-l+(o.right-e.right),y=a.width/2;f<y?y+=y-f:g<y&&(y=g),y<0?y=0:y>a.width&&(y=a.width),this.el_.style.right="-"+y+"px",tt(this.el_,s)}},t}(E);E.registerComponent("TimeTooltip",Ds);var lr=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Progress")+"</span>: 0%</span>"})},t.prototype.update=function(e,n){var s=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var a=s.player_.scrubbing()?s.player_.getCache().currentTime:s.player_.currentTime(),o=Ke(a,s.player_.duration()),l=s.getChild("timeTooltip");l&&l.update(e,n,o)})},t}(E);lr.prototype.options_={children:[]},(!S||S>8)&&!B&&!X&&lr.prototype.options_.children.push("timeTooltip"),E.registerComponent("PlayProgressBar",lr);var Fn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.update=ze(U(n,n.update),25),n}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},t.prototype.update=function(e,n){var s=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var a=s.player_.duration(),o=Ke(n*a,a);s.el_.style.left=e.width*n+"px",s.getChild("timeTooltip").update(e,n,o)})},t}(E);Fn.prototype.options_={children:["timeTooltip"]},E.registerComponent("MouseTimeDisplay",Fn);var Bn=5,$n=30,wt=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.setEventHandlers_(),n}return t.prototype.setEventHandlers_=function(){var e=this;this.update=ze(U(this,this.update),$n),this.on(this.player_,"timeupdate",this.update),this.on(this.player_,"ended",this.handleEnded),this.updateInterval=null,this.on(this.player_,["playing"],function(){e.clearInterval(e.updateInterval),e.updateInterval=e.setInterval(function(){e.requestAnimationFrame(function(){e.update()})},$n)}),this.on(this.player_,["ended","pause","waiting"],function(){e.clearInterval(e.updateInterval)}),this.on(this.player_,["timeupdate","ended"],this.update)},t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},t.prototype.update_=function(e,n){var s=this.player_.duration();this.el_.setAttribute("aria-valuenow",(n*100).toFixed(2)),this.el_.setAttribute("aria-valuetext",this.localize("progress bar timing: currentTime={1} duration={2}",[Ke(e,s),Ke(s,s)],"{1} of {2}")),this.bar.update(it(this.el_),n)},t.prototype.update=function(e){var n=i.prototype.update.call(this);return this.update_(this.getCurrentTime_(),n),n},t.prototype.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},t.prototype.handleEnded=function(e){this.update_(this.player_.duration(),1)},t.prototype.getPercent=function(){var e=this.getCurrentTime_()/this.player_.duration();return e>=1?1:e},t.prototype.handleMouseDown=function(e){Ve(e)&&(e.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),i.prototype.handleMouseDown.call(this,e))},t.prototype.handleMouseMove=function(e){if(Ve(e)){var n=this.calculateDistance(e)*this.player_.duration();n===this.player_.duration()&&(n=n-.1),this.player_.currentTime(n)}},t.prototype.enable=function(){i.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},t.prototype.disable=function(){i.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},t.prototype.handleMouseUp=function(e){i.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying&&ut(this.player_.play())},t.prototype.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+Bn)},t.prototype.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-Bn)},t.prototype.handleAction=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},t.prototype.handleKeyPress=function(e){e.which===32||e.which===13?(e.preventDefault(),this.handleAction(e)):i.prototype.handleKeyPress&&i.prototype.handleKeyPress.call(this,e)},t}(or);wt.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},(!S||S>8)&&!B&&!X&&wt.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),wt.prototype.playerEvent="timeupdate",E.registerComponent("SeekBar",wt);var Vn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.handleMouseMove=ze(U(n,n.handleMouseMove),25),n.throttledHandleMouseSeek=ze(U(n,n.handleMouseSeek),25),n.enable(),n}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},t.prototype.handleMouseMove=function(e){var n=this.getChild("seekBar");if(n){var s=n.getChild("mouseTimeDisplay"),a=n.el(),o=it(a),l=Lt(a,e).x;l>1?l=1:l<0&&(l=0),s&&s.update(o,l)}},t.prototype.handleMouseSeek=function(e){var n=this.getChild("seekBar");n&&n.handleMouseMove(e)},t.prototype.enabled=function(){return this.enabled_},t.prototype.disable=function(){this.children().forEach(function(e){return e.disable&&e.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},t.prototype.enable=function(){this.children().forEach(function(e){return e.enable&&e.enable()}),!this.enabled()&&(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},t.prototype.handleMouseDown=function(e){var n=this.el_.ownerDocument,s=this.getChild("seekBar");s&&s.handleMouseDown(e),this.on(n,"mousemove",this.throttledHandleMouseSeek),this.on(n,"touchmove",this.throttledHandleMouseSeek),this.on(n,"mouseup",this.handleMouseUp),this.on(n,"touchend",this.handleMouseUp)},t.prototype.handleMouseUp=function(e){var n=this.el_.ownerDocument,s=this.getChild("seekBar");s&&s.handleMouseUp(e),this.off(n,"mousemove",this.throttledHandleMouseSeek),this.off(n,"touchmove",this.throttledHandleMouseSeek),this.off(n,"mouseup",this.handleMouseUp),this.off(n,"touchend",this.handleMouseUp)},t}(E);Vn.prototype.options_={children:["seekBar"]},E.registerComponent("ProgressControl",Vn);var Hn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"fullscreenchange",n.handleFullscreenChange),u[bt.fullscreenEnabled]===!1&&n.disable(),n}return t.prototype.buildCSSClass=function(){return"vjs-fullscreen-control "+i.prototype.buildCSSClass.call(this)},t.prototype.handleFullscreenChange=function(e){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},t.prototype.handleClick=function(e){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},t}(xe);Hn.prototype.controlText_="Fullscreen",E.registerComponent("FullscreenToggle",Hn);var Ms=function(t,r){r.tech_&&!r.tech_.featuresVolumeControl&&t.addClass("vjs-hidden"),t.on(r,"loadstart",function(){r.tech_.featuresVolumeControl?t.removeClass("vjs-hidden"):t.addClass("vjs-hidden")})},Rs=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},t}(E);E.registerComponent("VolumeLevel",Rs);var ur=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on("slideractive",n.updateLastVolume_),n.on(r,"volumechange",n.updateARIAAttributes),r.ready(function(){return n.updateARIAAttributes()}),n}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},t.prototype.handleMouseDown=function(e){Ve(e)&&i.prototype.handleMouseDown.call(this,e)},t.prototype.handleMouseMove=function(e){Ve(e)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(e)))},t.prototype.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},t.prototype.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},t.prototype.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},t.prototype.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},t.prototype.updateARIAAttributes=function(e){var n=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",n),this.el_.setAttribute("aria-valuetext",n+"%")},t.prototype.volumeAsPercentage_=function(){return Math.round(this.player_.volume()*100)},t.prototype.updateLastVolume_=function(){var e=this,n=this.player_.volume();this.one("sliderinactive",function(){e.player_.volume()===0&&e.player_.lastVolume_(n)})},t}(or);ur.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},ur.prototype.playerEvent="volumechange",E.registerComponent("VolumeBar",ur);var zn=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};b(this,t),e.vertical=e.vertical||!1,(typeof e.volumeBar>"u"||we(e.volumeBar))&&(e.volumeBar=e.volumeBar||{},e.volumeBar.vertical=e.vertical);var n=v(this,i.call(this,r,e));return Ms(n,r),n.throttledHandleMouseMove=ze(U(n,n.handleMouseMove),25),n.on("mousedown",n.handleMouseDown),n.on("touchstart",n.handleMouseDown),n.on(n.volumeBar,["focus","slideractive"],function(){n.volumeBar.addClass("vjs-slider-active"),n.addClass("vjs-slider-active"),n.trigger("slideractive")}),n.on(n.volumeBar,["blur","sliderinactive"],function(){n.volumeBar.removeClass("vjs-slider-active"),n.removeClass("vjs-slider-active"),n.trigger("sliderinactive")}),n}return t.prototype.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),i.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},t.prototype.handleMouseDown=function(e){var n=this.el_.ownerDocument;this.on(n,"mousemove",this.throttledHandleMouseMove),this.on(n,"touchmove",this.throttledHandleMouseMove),this.on(n,"mouseup",this.handleMouseUp),this.on(n,"touchend",this.handleMouseUp)},t.prototype.handleMouseUp=function(e){var n=this.el_.ownerDocument;this.off(n,"mousemove",this.throttledHandleMouseMove),this.off(n,"touchmove",this.throttledHandleMouseMove),this.off(n,"mouseup",this.handleMouseUp),this.off(n,"touchend",this.handleMouseUp)},t.prototype.handleMouseMove=function(e){this.volumeBar.handleMouseMove(e)},t}(E);zn.prototype.options_={children:["volumeBar"]},E.registerComponent("VolumeControl",zn);var Ls=function(t,r){r.tech_&&!r.tech_.featuresMuteControl&&t.addClass("vjs-hidden"),t.on(r,"loadstart",function(){r.tech_.featuresMuteControl?t.removeClass("vjs-hidden"):t.addClass("vjs-hidden")})},Wn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return Ls(n,r),n.on(r,["loadstart","volumechange"],n.update),n}return t.prototype.buildCSSClass=function(){return"vjs-mute-control "+i.prototype.buildCSSClass.call(this)},t.prototype.handleClick=function(e){var n=this.player_.volume(),s=this.player_.lastVolume_();if(n===0){var a=s<.1?.1:s;this.player_.volume(a),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},t.prototype.update=function(e){this.updateIcon_(),this.updateControlText_()},t.prototype.updateIcon_=function(){var e=this.player_.volume(),n=3;B&&this.player_.muted(this.player_.tech_.el_.muted),e===0||this.player_.muted()?n=0:e<.33?n=1:e<.67&&(n=2);for(var s=0;s<4;s++)rt(this.el_,"vjs-vol-"+s);je(this.el_,"vjs-vol-"+n)},t.prototype.updateControlText_=function(){var e=this.player_.muted()||this.player_.volume()===0,n=e?"Unmute":"Mute";this.controlText()!==n&&this.controlText(n)},t}(xe);Wn.prototype.controlText_="Mute",E.registerComponent("MuteToggle",Wn);var Un=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};b(this,t),typeof e.inline<"u"?e.inline=e.inline:e.inline=!0,(typeof e.volumeControl>"u"||we(e.volumeControl))&&(e.volumeControl=e.volumeControl||{},e.volumeControl.vertical=!e.inline);var n=v(this,i.call(this,r,e));return n.on(r,["loadstart"],n.volumePanelState_),n.on(n.volumeControl,["slideractive"],n.sliderActive_),n.on(n.volumeControl,["sliderinactive"],n.sliderInactive_),n}return t.prototype.sliderActive_=function(){this.addClass("vjs-slider-active")},t.prototype.sliderInactive_=function(){this.removeClass("vjs-slider-active")},t.prototype.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},t.prototype.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),i.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},t}(E);Un.prototype.options_={children:["muteToggle","volumeControl"]},E.registerComponent("VolumePanel",Un);var cr=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return e&&(n.menuButton_=e.menuButton),n.focusedChild_=-1,n.on("keydown",n.handleKeyPress),n}return t.prototype.addItem=function(e){this.addChild(e),e.on("click",U(this,function(n){this.menuButton_&&(this.menuButton_.unpressButton(),e.name()!=="CaptionSettingsMenuItem"&&this.menuButton_.focus())}))},t.prototype.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=le(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var n=i.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return n.appendChild(this.contentEl_),Ce(n,"click",function(s){s.preventDefault(),s.stopImmediatePropagation()}),n},t.prototype.dispose=function(){this.contentEl_=null,i.prototype.dispose.call(this)},t.prototype.handleKeyPress=function(e){e.which===37||e.which===40?(e.preventDefault(),this.stepForward()):(e.which===38||e.which===39)&&(e.preventDefault(),this.stepBack())},t.prototype.stepForward=function(){var e=0;this.focusedChild_!==void 0&&(e=this.focusedChild_+1),this.focus(e)},t.prototype.stepBack=function(){var e=0;this.focusedChild_!==void 0&&(e=this.focusedChild_-1),this.focus(e)},t.prototype.focus=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,n=this.children().slice(),s=n.length&&n[0].className&&/vjs-menu-title/.test(n[0].className);s&&n.shift(),n.length>0&&(e<0?e=0:e>=n.length&&(e=n.length-1),this.focusedChild_=e,n[e].el_.focus())},t}(E);E.registerComponent("Menu",cr);var hr=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};b(this,t);var n=v(this,i.call(this,r,e));n.menuButton_=new xe(r,e),n.menuButton_.controlText(n.controlText_),n.menuButton_.el_.setAttribute("aria-haspopup","true");var s=xe.prototype.buildCSSClass();return n.menuButton_.el_.className=n.buildCSSClass()+" "+s,n.menuButton_.removeClass("vjs-control"),n.addChild(n.menuButton_),n.update(),n.enabled_=!0,n.on(n.menuButton_,"tap",n.handleClick),n.on(n.menuButton_,"click",n.handleClick),n.on(n.menuButton_,"focus",n.handleFocus),n.on(n.menuButton_,"blur",n.handleBlur),n.on("keydown",n.handleSubmenuKeyPress),n}return t.prototype.update=function(){var e=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=e,this.addChild(e),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},t.prototype.createMenu=function(){var e=new cr(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var n=le("li",{className:"vjs-menu-title",innerHTML:ae(this.options_.title),tabIndex:-1});this.hideThreshold_+=1,e.children_.unshift(n),vt(n,e.contentEl())}if(this.items=this.createItems(),this.items)for(var s=0;s<this.items.length;s++)e.addItem(this.items[s]);return e},t.prototype.createItems=function(){},t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},t.prototype.buildWrapperCSSClass=function(){var e="vjs-menu-button";this.options_.inline===!0?e+="-inline":e+="-popup";var n=xe.prototype.buildCSSClass();return"vjs-menu-button "+e+" "+n+" "+i.prototype.buildCSSClass.call(this)},t.prototype.buildCSSClass=function(){var e="vjs-menu-button";return this.options_.inline===!0?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+i.prototype.buildCSSClass.call(this)},t.prototype.controlText=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.menuButton_.el();return this.menuButton_.controlText(e,n)},t.prototype.handleClick=function(e){this.one(this.menu.contentEl(),"mouseleave",U(this,function(n){this.unpressButton(),this.el_.blur()})),this.buttonPressed_?this.unpressButton():this.pressButton()},t.prototype.focus=function(){this.menuButton_.focus()},t.prototype.blur=function(){this.menuButton_.blur()},t.prototype.handleFocus=function(){Ce(u,"keydown",U(this,this.handleKeyPress))},t.prototype.handleBlur=function(){pe(u,"keydown",U(this,this.handleKeyPress))},t.prototype.handleKeyPress=function(e){e.which===27||e.which===9?(this.buttonPressed_&&this.unpressButton(),e.which!==9&&(e.preventDefault(),this.menuButton_.el_.focus())):(e.which===38||e.which===40)&&(this.buttonPressed_||(this.pressButton(),e.preventDefault()))},t.prototype.handleSubmenuKeyPress=function(e){(e.which===27||e.which===9)&&(this.buttonPressed_&&this.unpressButton(),e.which!==9&&(e.preventDefault(),this.menuButton_.el_.focus()))},t.prototype.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),B&&qr())return;this.menu.focus()}},t.prototype.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},t.prototype.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},t.prototype.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},t}(E);E.registerComponent("MenuButton",hr);var dr=function(i){m(t,i);function t(r,e){b(this,t);var n=e.tracks,s=v(this,i.call(this,r,e));if(s.items.length<=1&&s.hide(),!n)return v(s);var a=U(s,s.update);return n.addEventListener("removetrack",a),n.addEventListener("addtrack",a),s.player_.on("ready",a),s.player_.on("dispose",function(){n.removeEventListener("removetrack",a),n.removeEventListener("addtrack",a)}),s}return t}(hr);E.registerComponent("TrackButton",dr);var ft=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.selectable=e.selectable,n.isSelected_=e.selected||!1,n.multiSelectable=e.multiSelectable,n.selected(n.isSelected_),n.selectable?n.multiSelectable?n.el_.setAttribute("role","menuitemcheckbox"):n.el_.setAttribute("role","menuitemradio"):n.el_.setAttribute("role","menuitem"),n}return t.prototype.createEl=function(e,n,s){return this.nonIconControl=!0,i.prototype.createEl.call(this,"li",J({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},n),s)},t.prototype.handleClick=function(e){this.selected(!0)},t.prototype.selected=function(e){this.selectable&&(e?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},t}(xt);E.registerComponent("MenuItem",ft);var pt=function(i){m(t,i);function t(r,e){b(this,t);var n=e.track,s=r.textTracks();e.label=n.label||n.language||"Unknown",e.selected=n.mode==="showing";var a=v(this,i.call(this,r,e));a.track=n;var o=function(){for(var y=arguments.length,x=Array(y),N=0;N<y;N++)x[N]=arguments[N];a.handleTracksChange.apply(a,x)},l=function(){for(var y=arguments.length,x=Array(y),N=0;N<y;N++)x[N]=arguments[N];a.handleSelectedLanguageChange.apply(a,x)};if(r.on(["loadstart","texttrackchange"],o),s.addEventListener("change",o),s.addEventListener("selectedlanguagechange",l),a.on("dispose",function(){r.off(["loadstart","texttrackchange"],o),s.removeEventListener("change",o),s.removeEventListener("selectedlanguagechange",l)}),s.onchange===void 0){var f=void 0;a.on(["tap","click"],function(){if(j(d.Event)!=="object")try{f=new d.Event("change")}catch{}f||(f=u.createEvent("Event"),f.initEvent("change",!0,!0)),s.dispatchEvent(f)})}return a.handleTracksChange(),a}return t.prototype.handleClick=function(e){var n=this.track.kind,s=this.track.kinds,a=this.player_.textTracks();if(s||(s=[n]),i.prototype.handleClick.call(this,e),!!a)for(var o=0;o<a.length;o++){var l=a[o];l===this.track&&s.indexOf(l.kind)>-1?l.mode!=="showing"&&(l.mode="showing"):l.mode!=="disabled"&&(l.mode="disabled")}},t.prototype.handleTracksChange=function(e){var n=this.track.mode==="showing";n!==this.isSelected_&&this.selected(n)},t.prototype.handleSelectedLanguageChange=function(e){if(this.track.mode==="showing"){var n=this.player_.cache_.selectedLanguage;if(n&&n.enabled&&n.language===this.track.language&&n.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},t.prototype.dispose=function(){this.track=null,i.prototype.dispose.call(this)},t}(ft);E.registerComponent("TextTrackMenuItem",pt);var qn=function(i){m(t,i);function t(r,e){return b(this,t),e.track={player:r,kind:e.kind,kinds:e.kinds,default:!1,mode:"disabled"},e.kinds||(e.kinds=[e.kind]),e.label?e.track.label=e.label:e.track.label=e.kinds.join(" and ")+" off",e.selectable=!0,e.multiSelectable=!1,v(this,i.call(this,r,e))}return t.prototype.handleTracksChange=function(e){for(var n=this.player().textTracks(),s=!0,a=0,o=n.length;a<o;a++){var l=n[a];if(this.options_.kinds.indexOf(l.kind)>-1&&l.mode==="showing"){s=!1;break}}s!==this.isSelected_&&this.selected(s)},t.prototype.handleSelectedLanguageChange=function(e){for(var n=this.player().textTracks(),s=!0,a=0,o=n.length;a<o;a++){var l=n[a];if(["captions","descriptions","subtitles"].indexOf(l.kind)>-1&&l.mode==="showing"){s=!1;break}}s&&(this.player_.cache_.selectedLanguage={enabled:!1})},t}(pt);E.registerComponent("OffTextTrackMenuItem",qn);var Ge=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return b(this,t),e.tracks=r.textTracks(),v(this,i.call(this,r,e))}return t.prototype.createItems=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt,s=void 0;this.label_&&(s=this.label_+" off"),e.push(new qn(this.player_,{kinds:this.kinds_,kind:this.kind_,label:s})),this.hideThreshold_+=1;var a=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var o=0;o<a.length;o++){var l=a[o];if(this.kinds_.indexOf(l.kind)>-1){var f=new n(this.player_,{track:l,selectable:!0,multiSelectable:!1});f.addClass("vjs-"+l.kind+"-menu-item"),e.push(f)}}return e},t}(dr);E.registerComponent("TextTrackButton",Ge);var Kn=function(i){m(t,i);function t(r,e){b(this,t);var n=e.track,s=e.cue,a=r.currentTime();e.selectable=!0,e.multiSelectable=!1,e.label=s.text,e.selected=s.startTime<=a&&a<s.endTime;var o=v(this,i.call(this,r,e));return o.track=n,o.cue=s,n.addEventListener("cuechange",U(o,o.update)),o}return t.prototype.handleClick=function(e){i.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},t.prototype.update=function(e){var n=this.cue,s=this.player_.currentTime();this.selected(n.startTime<=s&&s<n.endTime)},t}(ft);E.registerComponent("ChaptersTrackMenuItem",Kn);var fr=function(i){m(t,i);function t(r,e,n){return b(this,t),v(this,i.call(this,r,e,n))}return t.prototype.buildCSSClass=function(){return"vjs-chapters-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-chapters-button "+i.prototype.buildWrapperCSSClass.call(this)},t.prototype.update=function(e){(!this.track_||e&&(e.type==="addtrack"||e.type==="removetrack"))&&this.setTrack(this.findChaptersTrack()),i.prototype.update.call(this)},t.prototype.setTrack=function(e){if(this.track_!==e){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var n=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);n&&n.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=e,this.track_){this.track_.mode="hidden";var s=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);s&&s.addEventListener("load",this.updateHandler_)}}},t.prototype.findChaptersTrack=function(){for(var e=this.player_.textTracks()||[],n=e.length-1;n>=0;n--){var s=e[n];if(s.kind===this.kind_)return s}},t.prototype.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(ae(this.kind_))},t.prototype.createMenu=function(){return this.options_.title=this.getMenuCaption(),i.prototype.createMenu.call(this)},t.prototype.createItems=function(){var e=[];if(!this.track_)return e;var n=this.track_.cues;if(!n)return e;for(var s=0,a=n.length;s<a;s++){var o=n[s],l=new Kn(this.player_,{track:this.track_,cue:o});e.push(l)}return e},t}(Ge);fr.prototype.kind_="chapters",fr.prototype.controlText_="Chapters",E.registerComponent("ChaptersButton",fr);var pr=function(i){m(t,i);function t(r,e,n){b(this,t);var s=v(this,i.call(this,r,e,n)),a=r.textTracks(),o=U(s,s.handleTracksChange);return a.addEventListener("change",o),s.on("dispose",function(){a.removeEventListener("change",o)}),s}return t.prototype.handleTracksChange=function(e){for(var n=this.player().textTracks(),s=!1,a=0,o=n.length;a<o;a++){var l=n[a];if(l.kind!==this.kind_&&l.mode==="showing"){s=!0;break}}s?this.disable():this.enable()},t.prototype.buildCSSClass=function(){return"vjs-descriptions-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+i.prototype.buildWrapperCSSClass.call(this)},t}(Ge);pr.prototype.kind_="descriptions",pr.prototype.controlText_="Descriptions",E.registerComponent("DescriptionsButton",pr);var gr=function(i){m(t,i);function t(r,e,n){return b(this,t),v(this,i.call(this,r,e,n))}return t.prototype.buildCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+i.prototype.buildWrapperCSSClass.call(this)},t}(Ge);gr.prototype.kind_="subtitles",gr.prototype.controlText_="Subtitles",E.registerComponent("SubtitlesButton",gr);var vr=function(i){m(t,i);function t(r,e){b(this,t),e.track={player:r,kind:e.kind,label:e.kind+" settings",selectable:!1,default:!1,mode:"disabled"},e.selectable=!1,e.name="CaptionSettingsMenuItem";var n=v(this,i.call(this,r,e));return n.addClass("vjs-texttrack-settings"),n.controlText(", opens "+e.kind+" settings dialog"),n}return t.prototype.handleClick=function(e){this.player().getChild("textTrackSettings").open()},t}(pt);E.registerComponent("CaptionSettingsMenuItem",vr);var yr=function(i){m(t,i);function t(r,e,n){return b(this,t),v(this,i.call(this,r,e,n))}return t.prototype.buildCSSClass=function(){return"vjs-captions-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-captions-button "+i.prototype.buildWrapperCSSClass.call(this)},t.prototype.createItems=function(){var e=[];return!(this.player().tech_&&this.player().tech_.featuresNativeTextTracks)&&this.player().getChild("textTrackSettings")&&(e.push(new vr(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),i.prototype.createItems.call(this,e)},t}(Ge);yr.prototype.kind_="captions",yr.prototype.controlText_="Captions",E.registerComponent("CaptionsButton",yr);var Xn=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(e,n,s){var a='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);this.options_.track.kind==="captions"&&(a+=`
        <span aria-hidden="true" class="vjs-icon-placeholder"></span>
        <span class="vjs-control-text"> `+this.localize("Captions")+`</span>
      `),a+="</span>";var o=i.prototype.createEl.call(this,e,J({innerHTML:a},n),s);return o},t}(pt);E.registerComponent("SubsCapsMenuItem",Xn);var mr=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};b(this,t);var n=v(this,i.call(this,r,e));return n.label_="subtitles",["en","en-us","en-ca","fr-ca"].indexOf(n.player_.language_)>-1&&(n.label_="captions"),n.menuButton_.controlText(ae(n.label_)),n}return t.prototype.buildCSSClass=function(){return"vjs-subs-caps-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+i.prototype.buildWrapperCSSClass.call(this)},t.prototype.createItems=function(){var e=[];return!(this.player().tech_&&this.player().tech_.featuresNativeTextTracks)&&this.player().getChild("textTrackSettings")&&(e.push(new vr(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=i.prototype.createItems.call(this,e,Xn),e},t}(Ge);mr.prototype.kinds_=["captions","subtitles"],mr.prototype.controlText_="Subtitles",E.registerComponent("SubsCapsButton",mr);var Gn=function(i){m(t,i);function t(r,e){b(this,t);var n=e.track,s=r.audioTracks();e.label=n.label||n.language||"Unknown",e.selected=n.enabled;var a=v(this,i.call(this,r,e));a.track=n,a.addClass("vjs-"+n.kind+"-menu-item");var o=function(){for(var f=arguments.length,g=Array(f),y=0;y<f;y++)g[y]=arguments[y];a.handleTracksChange.apply(a,g)};return s.addEventListener("change",o),a.on("dispose",function(){s.removeEventListener("change",o)}),a}return t.prototype.createEl=function(e,n,s){var a='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);this.options_.track.kind==="main-desc"&&(a+=`
        <span aria-hidden="true" class="vjs-icon-placeholder"></span>
        <span class="vjs-control-text"> `+this.localize("Descriptions")+`</span>
      `),a+="</span>";var o=i.prototype.createEl.call(this,e,J({innerHTML:a},n),s);return o},t.prototype.handleClick=function(e){var n=this.player_.audioTracks();i.prototype.handleClick.call(this,e);for(var s=0;s<n.length;s++){var a=n[s];a.enabled=a===this.track}},t.prototype.handleTracksChange=function(e){this.selected(this.track.enabled)},t}(ft);E.registerComponent("AudioTrackMenuItem",Gn);var Yn=function(i){m(t,i);function t(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return b(this,t),e.tracks=r.audioTracks(),v(this,i.call(this,r,e))}return t.prototype.buildCSSClass=function(){return"vjs-audio-button "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-audio-button "+i.prototype.buildWrapperCSSClass.call(this)},t.prototype.createItems=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];this.hideThreshold_=1;for(var n=this.player_.audioTracks(),s=0;s<n.length;s++){var a=n[s];e.push(new Gn(this.player_,{track:a,selectable:!0,multiSelectable:!1}))}return e},t}(dr);Yn.prototype.controlText_="Audio Track",E.registerComponent("AudioTrackButton",Yn);var _r=function(i){m(t,i);function t(r,e){b(this,t);var n=e.rate,s=parseFloat(n,10);e.label=n,e.selected=s===1,e.selectable=!0,e.multiSelectable=!1;var a=v(this,i.call(this,r,e));return a.label=n,a.rate=s,a.on(r,"ratechange",a.update),a}return t.prototype.handleClick=function(e){i.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},t.prototype.update=function(e){this.selected(this.player().playbackRate()===this.rate)},t}(ft);_r.prototype.contentElType="button",E.registerComponent("PlaybackRateMenuItem",_r);var Jn=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.updateVisibility(),n.updateLabel(),n.on(r,"loadstart",n.updateVisibility),n.on(r,"ratechange",n.updateLabel),n}return t.prototype.createEl=function(){var e=i.prototype.createEl.call(this);return this.labelEl_=le("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},t.prototype.dispose=function(){this.labelEl_=null,i.prototype.dispose.call(this)},t.prototype.buildCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildCSSClass.call(this)},t.prototype.buildWrapperCSSClass=function(){return"vjs-playback-rate "+i.prototype.buildWrapperCSSClass.call(this)},t.prototype.createMenu=function(){var e=new cr(this.player()),n=this.playbackRates();if(n)for(var s=n.length-1;s>=0;s--)e.addChild(new _r(this.player(),{rate:n[s]+"x"}));return e},t.prototype.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},t.prototype.handleClick=function(e){for(var n=this.player().playbackRate(),s=this.playbackRates(),a=s[0],o=0;o<s.length;o++)if(s[o]>n){a=s[o];break}this.player().playbackRate(a)},t.prototype.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},t.prototype.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&this.playbackRates().length>0},t.prototype.updateVisibility=function(e){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},t.prototype.updateLabel=function(e){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},t}(hr);Jn.prototype.controlText_="Playback Rate",E.registerComponent("PlaybackRateMenuButton",Jn);var Qn=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.buildCSSClass=function(){return"vjs-spacer "+i.prototype.buildCSSClass.call(this)},t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},t}(E);E.registerComponent("Spacer",Qn);var Ns=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.buildCSSClass=function(){return"vjs-custom-control-spacer "+i.prototype.buildCSSClass.call(this)},t.prototype.createEl=function(){var e=i.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},t}(Qn);E.registerComponent("CustomControlSpacer",Ns);var Zn=function(i){m(t,i);function t(){return b(this,t),v(this,i.apply(this,arguments))}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},t}(E);Zn.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},E.registerComponent("ControlBar",Zn);var ei=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e));return n.on(r,"error",n.open),n}return t.prototype.buildCSSClass=function(){return"vjs-error-display "+i.prototype.buildCSSClass.call(this)},t.prototype.content=function(){var e=this.player().error();return e?this.localize(e.message):""},t}(Ue);ei.prototype.options_=ie(Ue.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),E.registerComponent("ErrorDisplay",ei);var br="vjs-text-track-settings",ti=["#000","Black"],ri=["#00F","Blue"],ni=["#0FF","Cyan"],ii=["#0F0","Green"],si=["#F0F","Magenta"],ai=["#F00","Red"],oi=["#FFF","White"],li=["#FF0","Yellow"],Tr=["1","Opaque"],Cr=["0.5","Semi-Transparent"],ui=["0","Transparent"],Fe={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[ti,oi,ai,ii,ri,li,si,ni]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[Tr,Cr,ui]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[oi,ti,ai,ii,ri,li,si,ni]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(t){return t==="1.00"?null:Number(t)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[Tr,Cr]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[ui,Cr,Tr]}};Fe.windowColor.options=Fe.backgroundColor.options;function ci(i,t){if(t&&(i=t(i)),i&&i!=="none")return i}function Fs(i,t){var r=i.options[i.options.selectedIndex].value;return ci(r,t)}function Bs(i,t,r){if(t){for(var e=0;e<i.options.length;e++)if(ci(i.options[e].value,r)===t){i.selectedIndex=e;break}}}var $s=function(i){m(t,i);function t(r,e){b(this,t),e.temporary=!1;var n=v(this,i.call(this,r,e));return n.updateDisplay=U(n,n.updateDisplay),n.fill(),n.hasBeenOpened_=n.hasBeenFilled_=!0,n.endDialog=le("p",{className:"vjs-control-text",textContent:n.localize("End of dialog window.")}),n.el().appendChild(n.endDialog),n.setDefaults(),e.persistTextTrackSettings===void 0&&(n.options_.persistTextTrackSettings=n.options_.playerOptions.persistTextTrackSettings),n.on(n.$(".vjs-done-button"),"click",function(){n.saveSettings(),n.close()}),n.on(n.$(".vjs-default-button"),"click",function(){n.setDefaults(),n.updateDisplay()}),se(Fe,function(s){n.on(n.$(s.selector),"change",n.updateDisplay)}),n.options_.persistTextTrackSettings&&n.restoreSettings(),n}return t.prototype.dispose=function(){this.endDialog=null,i.prototype.dispose.call(this)},t.prototype.createElSelect_=function(e){var n=this,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"label",o=Fe[e],l=o.id.replace("%s",this.id_),f=[s,l].join(" ").trim();return["<"+a+' id="'+l+'" class="'+(a==="label"?"vjs-label":"")+'">',this.localize(o.label),"</"+a+">",'<select aria-labelledby="'+f+'">'].concat(o.options.map(function(g){var y=l+"-"+g[1].replace(/\W+/g,"");return['<option id="'+y+'" value="'+g[0]+'" ','aria-labelledby="'+f+" "+y+'">',n.localize(g[1]),"</option>"].join("")})).concat("</select>").join("")},t.prototype.createElFgColor_=function(){var e="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",e),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",e),"</span>","</fieldset>"].join("")},t.prototype.createElBgColor_=function(){var e="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",e),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",e),"</span>","</fieldset>"].join("")},t.prototype.createElWinColor_=function(){var e="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",e),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",e),"</span>","</fieldset>"].join("")},t.prototype.createElColors_=function(){return le("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},t.prototype.createElFont_=function(){return le("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},t.prototype.createElControls_=function(){var e=this.localize("restore all settings to the default values");return le("div",{className:"vjs-track-settings-controls",innerHTML:['<button type="button" class="vjs-default-button" title="'+e+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+e+"</span>","</button>",'<button type="button" class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},t.prototype.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},t.prototype.label=function(){return this.localize("Caption Settings Dialog")},t.prototype.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},t.prototype.buildCSSClass=function(){return i.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},t.prototype.getValues=function(){var e=this;return te(Fe,function(n,s,a){var o=Fs(e.$(s.selector),s.parser);return o!==void 0&&(n[a]=o),n},{})},t.prototype.setValues=function(e){var n=this;se(Fe,function(s,a){Bs(n.$(s.selector),e[a],s.parser)})},t.prototype.setDefaults=function(){var e=this;se(Fe,function(n){var s=n.hasOwnProperty("default")?n.default:0;e.$(n.selector).selectedIndex=s})},t.prototype.restoreSettings=function(){var e=void 0;try{e=JSON.parse(d.localStorage.getItem(br))}catch(n){K.warn(n)}e&&this.setValues(e)},t.prototype.saveSettings=function(){if(this.options_.persistTextTrackSettings){var e=this.getValues();try{Object.keys(e).length?d.localStorage.setItem(br,JSON.stringify(e)):d.localStorage.removeItem(br)}catch(n){K.warn(n)}}},t.prototype.updateDisplay=function(){var e=this.player_.getChild("textTrackDisplay");e&&e.updateDisplay()},t.prototype.conditionalBlur_=function(){this.previouslyActiveEl_=null,this.off(u,"keydown",this.handleKeyDown);var e=this.player_.controlBar,n=e&&e.subsCapsButton,s=e&&e.captionsButton;n?n.focus():s&&s.focus()},t}(Ue);E.registerComponent("TextTrackSettings",$s);var Vs=function(i){m(t,i);function t(r,e){b(this,t);var n=e.ResizeObserver||d.ResizeObserver;e.ResizeObserver===null&&(n=!1);var s=ie({createEl:!n},e),a=v(this,i.call(this,r,s));return a.ResizeObserver=e.ResizeObserver||d.ResizeObserver,a.loadListener_=null,a.resizeObserver_=null,a.debouncedHandler_=Hi(function(){a.resizeHandler()},100,!1,a),n?(a.resizeObserver_=new a.ResizeObserver(a.debouncedHandler_),a.resizeObserver_.observe(r.el())):(a.loadListener_=function(){!a.el_||!a.el_.contentWindow||Ce(a.el_.contentWindow,"resize",a.debouncedHandler_)},a.one("load",a.loadListener_)),a}return t.prototype.createEl=function(){return i.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager"})},t.prototype.resizeHandler=function(){!this.player_||!this.player_.trigger||this.player_.trigger("playerresize")},t.prototype.dispose=function(){this.debouncedHandler_&&this.debouncedHandler_.cancel(),this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.el_&&this.el_.contentWindow&&pe(this.el_.contentWindow,"resize",this.debouncedHandler_),this.loadListener_&&this.off("load",this.loadListener_),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null},t}(E);E.registerComponent("ResizeManager",Vs);var Sr=function(t){var r=t.el();if(r.hasAttribute("src"))return t.triggerSourceset(r.src),!0;var e=t.$$("source"),n=[],s="";if(!e.length)return!1;for(var a=0;a<e.length;a++){var o=e[a].src;o&&n.indexOf(o)===-1&&n.push(o)}return n.length?(n.length===1&&(s=n[0]),t.triggerSourceset(s),!0):!1},hi={};h||(hi=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(t){var r=u.createElement(this.nodeName.toLowerCase());r.innerHTML=t;for(var e=u.createDocumentFragment();r.childNodes.length;)e.appendChild(r.childNodes[0]);return this.innerText="",d.Element.prototype.appendChild.call(this,e),this.innerHTML}}));var di=function(t,r){for(var e={},n=0;n<t.length&&(e=Object.getOwnPropertyDescriptor(t[n],r),!(e&&e.set&&e.get));n++);return e.enumerable=!0,e.configurable=!0,e},Hs=function(t){return di([t.el(),d.HTMLMediaElement.prototype,d.Element.prototype,hi],"innerHTML")},fi=function(t){var r=t.el();if(!r.resetSourceWatch_){var e={},n=Hs(t),s=function(o){return function(){for(var l=arguments.length,f=Array(l),g=0;g<l;g++)f[g]=arguments[g];var y=o.apply(r,f);return Sr(t),y}};["append","appendChild","insertAdjacentHTML"].forEach(function(a){r[a]&&(e[a]=r[a],r[a]=s(e[a]))}),Object.defineProperty(r,"innerHTML",ie(n,{set:s(n.set)})),r.resetSourceWatch_=function(){r.resetSourceWatch_=null,Object.keys(e).forEach(function(a){r[a]=e[a]}),Object.defineProperty(r,"innerHTML",n)},t.one("sourceset",r.resetSourceWatch_)}},pi={};h||(pi=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?En(d.Element.prototype.getAttribute.call(this,"src")):""},set:function(t){return d.Element.prototype.setAttribute.call(this,"src",t),t}}));var zs=function(t){return di([t.el(),d.HTMLMediaElement.prototype,pi],"src")},Ws=function(t){if(t.featuresSourceset){var r=t.el();if(!r.resetSourceset_){var e=zs(t),n=r.setAttribute,s=r.load;Object.defineProperty(r,"src",ie(e,{set:function(o){var l=e.set.call(r,o);return t.triggerSourceset(r.src),l}})),r.setAttribute=function(a,o){var l=n.call(r,a,o);return/src/i.test(a)&&t.triggerSourceset(r.src),l},r.load=function(){var a=s.call(r);return Sr(t)||(t.triggerSourceset(""),fi(t)),a},r.currentSrc?t.triggerSourceset(r.currentSrc):Sr(t)||fi(t),r.resetSourceset_=function(){r.resetSourceset_=null,r.load=s,r.setAttribute=n,Object.defineProperty(r,"src",e),r.resetSourceWatch_&&r.resetSourceWatch_()}}}},Us=z([`Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.
            This may prevent text tracks from loading.`],[`Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.
            This may prevent text tracks from loading.`]),w=function(i){m(t,i);function t(r,e){b(this,t);var n=v(this,i.call(this,r,e)),s=r.source,a=!1;if(s&&(n.el_.currentSrc!==s.src||r.tag&&r.tag.initNetworkState_===3)?n.setSource(s):n.handleLateInit_(n.el_),r.enableSourceset&&n.setupSourcesetHandling_(),n.el_.hasChildNodes()){for(var o=n.el_.childNodes,l=o.length,f=[];l--;){var g=o[l],y=g.nodeName.toLowerCase();y==="track"&&(n.featuresNativeTextTracks?(n.remoteTextTrackEls().addTrackElement_(g),n.remoteTextTracks().addTrack(g.track),n.textTracks().addTrack(g.track),!a&&!n.el_.hasAttribute("crossorigin")&&kt(g.src)&&(a=!0)):f.push(g))}for(var x=0;x<f.length;x++)n.el_.removeChild(f[x])}return n.proxyNativeTracks_(),n.featuresNativeTextTracks&&a&&K.warn(T(Us)),n.restoreMetadataTracksInIOSNativePlayer_(),($||I||ne)&&r.nativeControlsForTouch===!0&&n.setControls(!0),n.proxyWebkitFullscreen_(),n.triggerReady(),n}return t.prototype.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),t.disposeMediaElement(this.el_),this.options_=null,i.prototype.dispose.call(this)},t.prototype.setupSourcesetHandling_=function(){Ws(this)},t.prototype.restoreMetadataTracksInIOSNativePlayer_=function(){var e=this.textTracks(),n=void 0,s=function(){n=[];for(var l=0;l<e.length;l++){var f=e[l];f.kind==="metadata"&&n.push({track:f,storedMode:f.mode})}};s(),e.addEventListener("change",s),this.on("dispose",function(){return e.removeEventListener("change",s)});var a=function o(){for(var l=0;l<n.length;l++){var f=n[l];f.track.mode==="disabled"&&f.track.mode!==f.storedMode&&(f.track.mode=f.storedMode)}e.removeEventListener("change",o)};this.on("webkitbeginfullscreen",function(){e.removeEventListener("change",s),e.removeEventListener("change",a),e.addEventListener("change",a)}),this.on("webkitendfullscreen",function(){e.removeEventListener("change",s),e.addEventListener("change",s),e.removeEventListener("change",a)})},t.prototype.proxyNativeTracks_=function(){var e=this;_e.names.forEach(function(n){var s=_e[n],a=e.el()[s.getterName],o=e[s.getterName]();if(!(!e["featuresNative"+s.capitalName+"Tracks"]||!a||!a.addEventListener)){var l={change:function(y){o.trigger({type:"change",target:o,currentTarget:o,srcElement:o})},addtrack:function(y){o.addTrack(y.track)},removetrack:function(y){o.removeTrack(y.track)}},f=function(){for(var y=[],x=0;x<o.length;x++){for(var N=!1,Z=0;Z<a.length;Z++)if(a[Z]===o[x]){N=!0;break}N||y.push(o[x])}for(;y.length;)o.removeTrack(y.shift())};Object.keys(l).forEach(function(g){var y=l[g];a.addEventListener(g,y),e.on("dispose",function(x){return a.removeEventListener(g,y)})}),e.on("loadstart",f),e.on("dispose",function(g){return e.off("loadstart",f)})}})},t.prototype.createEl=function(){var e=this.options_.tag;if(!e||!(this.options_.playerElIngest||this.movingMediaElementInDOM)){if(e){var n=e.cloneNode(!0);e.parentNode&&e.parentNode.insertBefore(n,e),t.disposeMediaElement(e),e=n}else{e=u.createElement("video");var s=this.options_.tag&&ke(this.options_.tag),a=ie({},s);(!$||this.options_.nativeControlsForTouch!==!0)&&delete a.controls,Gr(e,J(a,{id:this.options_.techId,class:"vjs-tech"}))}e.playerId=this.options_.playerId}typeof this.options_.preload<"u"&&nt(e,"preload",this.options_.preload);for(var o=["loop","muted","playsinline","autoplay"],l=0;l<o.length;l++){var f=o[l],g=this.options_[f];typeof g<"u"&&(g?nt(e,f,f):yt(e,f),e[f]=g)}return e},t.prototype.handleLateInit_=function(e){if(!(e.networkState===0||e.networkState===3)){if(e.readyState===0){var n=!1,s=function(){n=!0};this.on("loadstart",s);var a=function(){n||this.trigger("loadstart")};this.on("loadedmetadata",a),this.ready(function(){this.off("loadstart",s),this.off("loadedmetadata",a),n||this.trigger("loadstart")});return}var o=["loadstart"];o.push("loadedmetadata"),e.readyState>=2&&o.push("loadeddata"),e.readyState>=3&&o.push("canplay"),e.readyState>=4&&o.push("canplaythrough"),this.ready(function(){o.forEach(function(l){this.trigger(l)},this)})}},t.prototype.setCurrentTime=function(e){try{this.el_.currentTime=e}catch(n){K(n,"Video is not ready. (Video.js)")}},t.prototype.duration=function(){var e=this;if(this.el_.duration===1/0&&X&&oe&&this.el_.currentTime===0){var n=function s(){e.el_.currentTime>0&&(e.el_.duration===1/0&&e.trigger("durationchange"),e.off("timeupdate",s))};return this.on("timeupdate",n),NaN}return this.el_.duration||NaN},t.prototype.width=function(){return this.el_.offsetWidth},t.prototype.height=function(){return this.el_.offsetHeight},t.prototype.proxyWebkitFullscreen_=function(){var e=this;if("webkitDisplayingFullscreen"in this.el_){var n=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},s=function(){"webkitPresentationMode"in this.el_&&this.el_.webkitPresentationMode!=="picture-in-picture"&&(this.one("webkitendfullscreen",n),this.trigger("fullscreenchange",{isFullscreen:!0}))};this.on("webkitbeginfullscreen",s),this.on("dispose",function(){e.off("webkitbeginfullscreen",s),e.off("webkitendfullscreen",n)})}},t.prototype.supportsFullScreen=function(){if(typeof this.el_.webkitEnterFullScreen=="function"){var e=d.navigator&&d.navigator.userAgent||"";if(/Android/.test(e)||!/Chrome|Mac OS X 10.5/.test(e))return!0}return!1},t.prototype.enterFullScreen=function(){var e=this.el_;e.paused&&e.networkState<=e.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){e.pause(),e.webkitEnterFullScreen()},0)):e.webkitEnterFullScreen()},t.prototype.exitFullScreen=function(){this.el_.webkitExitFullScreen()},t.prototype.src=function(e){if(e===void 0)return this.el_.src;this.setSrc(e)},t.prototype.reset=function(){t.resetMediaElement(this.el_)},t.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},t.prototype.setControls=function(e){this.el_.controls=!!e},t.prototype.addTextTrack=function(e,n,s){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,n,s):i.prototype.addTextTrack.call(this,e,n,s)},t.prototype.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return i.prototype.createRemoteTextTrack.call(this,e);var n=u.createElement("track");return e.kind&&(n.kind=e.kind),e.label&&(n.label=e.label),(e.language||e.srclang)&&(n.srclang=e.language||e.srclang),e.default&&(n.default=e.default),e.id&&(n.id=e.id),e.src&&(n.src=e.src),n},t.prototype.addRemoteTextTrack=function(e,n){var s=i.prototype.addRemoteTextTrack.call(this,e,n);return this.featuresNativeTextTracks&&this.el().appendChild(s),s},t.prototype.removeRemoteTextTrack=function(e){if(i.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var n=this.$$("track"),s=n.length;s--;)(e===n[s]||e===n[s].track)&&this.el().removeChild(n[s])},t.prototype.getVideoPlaybackQuality=function(){if(typeof this.el().getVideoPlaybackQuality=="function")return this.el().getVideoPlaybackQuality();var e={};return typeof this.el().webkitDroppedFrameCount<"u"&&typeof this.el().webkitDecodedFrameCount<"u"&&(e.droppedVideoFrames=this.el().webkitDroppedFrameCount,e.totalVideoFrames=this.el().webkitDecodedFrameCount),d.performance&&typeof d.performance.now=="function"?e.creationTime=d.performance.now():d.performance&&d.performance.timing&&typeof d.performance.timing.navigationStart=="number"&&(e.creationTime=d.Date.now()-d.performance.timing.navigationStart),e},t}(re);if(Ae()){w.TEST_VID=u.createElement("video");var At=u.createElement("track");At.kind="captions",At.srclang="en",At.label="English",w.TEST_VID.appendChild(At)}w.isSupported=function(){try{w.TEST_VID.volume=.5}catch{return!1}return!!(w.TEST_VID&&w.TEST_VID.canPlayType)},w.canPlayType=function(i){return w.TEST_VID.canPlayType(i)},w.canPlaySource=function(i,t){return w.canPlayType(i.type)},w.canControlVolume=function(){try{var i=w.TEST_VID.volume;return w.TEST_VID.volume=i/2+.1,i!==w.TEST_VID.volume}catch{return!1}},w.canMuteVolume=function(){try{var i=w.TEST_VID.muted;return w.TEST_VID.muted=!i,w.TEST_VID.muted?nt(w.TEST_VID,"muted","muted"):yt(w.TEST_VID,"muted","muted"),i!==w.TEST_VID.muted}catch{return!1}},w.canControlPlaybackRate=function(){if(X&&oe&&p<58)return!1;try{var i=w.TEST_VID.playbackRate;return w.TEST_VID.playbackRate=i/2+.1,i!==w.TEST_VID.playbackRate}catch{return!1}},w.canOverrideAttributes=function(){if(h)return!1;try{var i=function(){};Object.defineProperty(u.createElement("video"),"src",{get:i,set:i}),Object.defineProperty(u.createElement("audio"),"src",{get:i,set:i}),Object.defineProperty(u.createElement("video"),"innerHTML",{get:i,set:i}),Object.defineProperty(u.createElement("audio"),"innerHTML",{get:i,set:i})}catch{return!1}return!0},w.supportsNativeTextTracks=function(){return A||B&&oe},w.supportsNativeVideoTracks=function(){return!!(w.TEST_VID&&w.TEST_VID.videoTracks)},w.supportsNativeAudioTracks=function(){return!!(w.TEST_VID&&w.TEST_VID.audioTracks)},w.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],w.prototype.featuresVolumeControl=w.canControlVolume(),w.prototype.featuresMuteControl=w.canMuteVolume(),w.prototype.featuresPlaybackRate=w.canControlPlaybackRate(),w.prototype.featuresSourceset=w.canOverrideAttributes(),w.prototype.movingMediaElementInDOM=!B,w.prototype.featuresFullscreenResize=!0,w.prototype.featuresProgressEvents=!0,w.prototype.featuresTimeupdateEvents=!0,w.prototype.featuresNativeTextTracks=w.supportsNativeTextTracks(),w.prototype.featuresNativeVideoTracks=w.supportsNativeVideoTracks(),w.prototype.featuresNativeAudioTracks=w.supportsNativeAudioTracks();var Er=w.TEST_VID&&w.TEST_VID.constructor.prototype.canPlayType,qs=/^application\/(?:x-|vnd\.apple\.)mpegurl/i,Ks=/^video\/mp4/i;w.patchCanPlayType=function(){Y>=4&&!ve&&!oe?w.TEST_VID.constructor.prototype.canPlayType=function(i){return i&&qs.test(i)?"maybe":Er.call(this,i)}:R&&(w.TEST_VID.constructor.prototype.canPlayType=function(i){return i&&Ks.test(i)?"maybe":Er.call(this,i)})},w.unpatchCanPlayType=function(){var i=w.TEST_VID.constructor.prototype.canPlayType;return w.TEST_VID.constructor.prototype.canPlayType=Er,i},w.patchCanPlayType(),w.disposeMediaElement=function(i){if(i){for(i.parentNode&&i.parentNode.removeChild(i);i.hasChildNodes();)i.removeChild(i.firstChild);i.removeAttribute("src"),typeof i.load=="function"&&function(){try{i.load()}catch{}}()}},w.resetMediaElement=function(i){if(i){for(var t=i.querySelectorAll("source"),r=t.length;r--;)i.removeChild(t[r]);i.removeAttribute("src"),typeof i.load=="function"&&function(){try{i.load()}catch{}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(i){w.prototype[i]=function(){return this.el_[i]||this.el_.hasAttribute(i)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(i){w.prototype["set"+ae(i)]=function(t){this.el_[i]=t,t?this.el_.setAttribute(i,i):this.el_.removeAttribute(i)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(i){w.prototype[i]=function(){return this.el_[i]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(i){w.prototype["set"+ae(i)]=function(t){this.el_[i]=t}}),["pause","load","play"].forEach(function(i){w.prototype[i]=function(){return this.el_[i]()}}),re.withSourceHandlers(w),w.nativeSourceHandler={},w.nativeSourceHandler.canPlayType=function(i){try{return w.TEST_VID.canPlayType(i)}catch{return""}},w.nativeSourceHandler.canHandleSource=function(i,t){if(i.type)return w.nativeSourceHandler.canPlayType(i.type);if(i.src){var r=Qt(i.src);return w.nativeSourceHandler.canPlayType("video/"+r)}return""},w.nativeSourceHandler.handleSource=function(i,t,r){t.setSrc(i.src)},w.nativeSourceHandler.dispose=function(){},w.registerSourceHandler(w.nativeSourceHandler),re.registerTech("Html5",w);var Xs=z([`
        Using the tech directly can be dangerous. I hope you know what you're doing.
        See https://github.com/videojs/video.js/issues/2617 for more info.
      `],[`
        Using the tech directly can be dangerous. I hope you know what you're doing.
        See https://github.com/videojs/video.js/issues/2617 for more info.
      `]),gi=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],kr={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},xr=["tiny","xsmall","small","medium","large","xlarge","huge"],Pt={};xr.forEach(function(i){var t=i.charAt(0)==="x"?"x-"+i.substring(1):i;Pt[i]="vjs-layout-"+t});var Gs={tiny:210,xsmall:320,small:425,medium:768,large:1440,xlarge:2560,huge:1/0},ue=function(i){m(t,i);function t(r,e,n){if(b(this,t),r.id=r.id||e.id||"vjs_video_"+De(),e=J(t.getTagSettings(r),e),e.initChildren=!1,e.createEl=!1,e.evented=!1,e.reportTouchActivity=!1,!e.language)if(typeof r.closest=="function"){var s=r.closest("[lang]");s&&s.getAttribute&&(e.language=s.getAttribute("lang"))}else for(var a=r;a&&a.nodeType===1;){if(ke(a).hasOwnProperty("lang")){e.language=a.getAttribute("lang");break}a=a.parentNode}var o=v(this,i.call(this,null,e,n));if(o.log=zr(o.id_),o.isPosterFromTech_=!1,o.queuedCallbacks_=[],o.isReady_=!1,o.hasStarted_=!1,o.userActive_=!1,!o.options_||!o.options_.techOrder||!o.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(o.tag=r,o.tagAttributes=r&&ke(r),o.language(o.options_.language),e.languages){var l={};Object.getOwnPropertyNames(e.languages).forEach(function(x){l[x.toLowerCase()]=e.languages[x]}),o.languages_=l}else o.languages_=t.prototype.options_.languages;o.cache_={},o.poster_=e.poster||"",o.controls_=!!e.controls,o.cache_.lastVolume=1,r.controls=!1,r.removeAttribute("controls"),r.hasAttribute("autoplay")?o.options_.autoplay=!0:o.autoplay(o.options_.autoplay),o.scrubbing_=!1,o.el_=o.createEl(),o.cache_.lastPlaybackRate=o.defaultPlaybackRate(),Wt(o,{eventBusKey:"el_"});var f=ie(o.options_);if(e.plugins){var g=e.plugins;Object.keys(g).forEach(function(x){if(typeof this[x]=="function")this[x](g[x]);else throw new Error('plugin "'+x+'" does not exist')},o)}o.options_.playerOptions=f,o.middleware_=[],o.initChildren(),o.isAudio(r.nodeName.toLowerCase()==="audio"),o.controls()?o.addClass("vjs-controls-enabled"):o.addClass("vjs-controls-disabled"),o.el_.setAttribute("role","region"),o.isAudio()?o.el_.setAttribute("aria-label",o.localize("Audio Player")):o.el_.setAttribute("aria-label",o.localize("Video Player")),o.isAudio()&&o.addClass("vjs-audio"),o.flexNotSupported_()&&o.addClass("vjs-no-flex"),B||o.addClass("vjs-workinghover"),t.players[o.id_]=o;var y=D.split(".")[0];return o.addClass("vjs-v"+y),o.userActive(!0),o.reportUserActivity(),o.one("play",o.listenForUserActivity_),o.on("fullscreenchange",o.handleFullscreenChange_),o.on("stageclick",o.handleStageClick_),o.breakpoints(o.options_.breakpoints),o.responsive(o.options_.responsive),o.changingSrc_=!1,o.playWaitingForReady_=!1,o.playOnLoadstart_=null,o}return t.prototype.dispose=function(){this.trigger("dispose"),this.off("dispose"),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),t.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),ys(this),i.prototype.dispose.call(this)},t.prototype.createEl=function(){var e=this.tag,n=void 0,s=this.playerElIngest_=e.parentNode&&e.parentNode.hasAttribute&&e.parentNode.hasAttribute("data-vjs-player"),a=this.tag.tagName.toLowerCase()==="video-js";s?n=this.el_=e.parentNode:a||(n=this.el_=i.prototype.createEl.call(this,"div"));var o=ke(e);if(a){for(n=this.el_=e,e=this.tag=u.createElement("video");n.children.length;)e.appendChild(n.firstChild);Ie(n,"video-js")||je(n,"video-js"),n.appendChild(e),s=this.playerElIngest_=n,["autoplay","controls","crossOrigin","defaultMuted","defaultPlaybackRate","loop","muted","playbackRate","src","volume"].forEach(function(N){typeof n[N]<"u"&&(e[N]=n[N])})}if(e.setAttribute("tabindex","-1"),o.tabindex="-1",S&&(e.setAttribute("role","application"),o.role="application"),e.removeAttribute("width"),e.removeAttribute("height"),"width"in o&&delete o.width,"height"in o&&delete o.height,Object.getOwnPropertyNames(o).forEach(function(N){N==="class"?(n.className+=" "+o[N],a&&(e.className+=" "+o[N])):(n.setAttribute(N,o[N]),a&&e.setAttribute(N,o[N]))}),e.playerId=e.id,e.id+="_html5_api",e.className="vjs-tech",e.player=n.player=this,this.addClass("vjs-paused"),d.VIDEOJS_NO_DYNAMIC_STYLE!==!0){this.styleEl_=cn("vjs-styles-dimensions");var l=Pe(".vjs-styles-defaults"),f=Pe("head");f.insertBefore(this.styleEl_,l?l.nextSibling:f.firstChild)}this.fill_=!1,this.fluid_=!1,this.width(this.options_.width),this.height(this.options_.height),this.fill(this.options_.fill),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var g=e.getElementsByTagName("a"),y=0;y<g.length;y++){var x=g.item(y);je(x,"vjs-hidden"),x.setAttribute("hidden","hidden")}return e.initNetworkState_=e.networkState,e.parentNode&&!s&&e.parentNode.insertBefore(n,e),vt(e,n),this.children_.unshift(e),this.el_.setAttribute("lang",this.language_),this.el_=n,n},t.prototype.width=function(e){return this.dimension("width",e)},t.prototype.height=function(e){return this.dimension("height",e)},t.prototype.dimension=function(e,n){var s=e+"_";if(n===void 0)return this[s]||0;if(n===""){this[s]=void 0,this.updateStyleEl_();return}var a=parseFloat(n);if(isNaN(a)){K.error('Improper value "'+n+'" supplied for for '+e);return}this[s]=a,this.updateStyleEl_()},t.prototype.fluid=function(e){if(e===void 0)return!!this.fluid_;this.fluid_=!!e,e?(this.addClass("vjs-fluid"),this.fill(!1)):this.removeClass("vjs-fluid"),this.updateStyleEl_()},t.prototype.fill=function(e){if(e===void 0)return!!this.fill_;this.fill_=!!e,e?(this.addClass("vjs-fill"),this.fluid(!1)):this.removeClass("vjs-fill")},t.prototype.aspectRatio=function(e){if(e===void 0)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},t.prototype.updateStyleEl_=function(){if(d.VIDEOJS_NO_DYNAMIC_STYLE===!0){var e=typeof this.width_=="number"?this.width_:this.options_.width,n=typeof this.height_=="number"?this.height_:this.options_.height,s=this.tech_&&this.tech_.el();s&&(e>=0&&(s.width=e),n>=0&&(s.height=n));return}var a=void 0,o=void 0,l=void 0,f=void 0;this.aspectRatio_!==void 0&&this.aspectRatio_!=="auto"?l=this.aspectRatio_:this.videoWidth()>0?l=this.videoWidth()+":"+this.videoHeight():l="16:9";var g=l.split(":"),y=g[1]/g[0];this.width_!==void 0?a=this.width_:this.height_!==void 0?a=this.height_/y:a=this.videoWidth()||300,this.height_!==void 0?o=this.height_:o=a*y,/^[^a-zA-Z]/.test(this.id())?f="dimensions-"+this.id():f=this.id()+"-dimensions",this.addClass(f),hn(this.styleEl_,`
      .`+f+` {
        width: `+a+`px;
        height: `+o+`px;
      }

      .`+f+`.vjs-fluid {
        padding-top: `+y*100+`%;
      }
    `)},t.prototype.loadTech_=function(e,n){var s=this;this.tech_&&this.unloadTech_();var a=ae(e),o=e.charAt(0).toLowerCase()+e.slice(1);a!=="Html5"&&this.tag&&(re.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=a,this.isReady_=!1;var l=typeof this.autoplay()=="string"?!1:this.autoplay(),f={source:n,autoplay:l,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+o+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset};ge.names.forEach(function(y){var x=ge[y];f[x.getterName]=s[x.privateName]}),J(f,this.options_[a]),J(f,this.options_[o]),J(f,this.options_[e.toLowerCase()]),this.tag&&(f.tag=this.tag),n&&n.src===this.cache_.src&&this.cache_.currentTime>0&&(f.startTime=this.cache_.currentTime);var g=re.getTech(e);if(!g)throw new Error("No Tech named '"+a+"' exists! '"+a+"' should be registered using videojs.registerTech()'");this.tech_=new g(f),this.tech_.ready(U(this,this.handleTechReady_),!0),bn.jsonToTextTracks(this.textTracksJson_||[],this.tech_),gi.forEach(function(y){s.on(s.tech_,y,s["handleTech"+ae(y)+"_"])}),Object.keys(kr).forEach(function(y){s.on(s.tech_,y,function(x){if(s.tech_.playbackRate()===0&&s.tech_.seeking()){s.queuedCallbacks_.push({callback:s["handleTech"+kr[y]+"_"].bind(s),event:x});return}s["handleTech"+kr[y]+"_"](x)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode!==this.el()&&(a!=="Html5"||!this.tag)&&vt(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},t.prototype.unloadTech_=function(){var e=this;ge.names.forEach(function(n){var s=ge[n];e[s.privateName]=e[s.getterName]()}),this.textTracksJson_=bn.textTracksToJson(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},t.prototype.tech=function(e){return e===void 0&&K.warn(T(Xs)),this.tech_},t.prototype.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mousedown",this.handleTechClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},t.prototype.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mousedown",this.handleTechClick_)},t.prototype.handleTechReady_=function(){if(this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_(),(this.src()||this.currentSrc())&&this.tag&&this.options_.autoplay&&this.paused())try{delete this.tag.poster}catch(e){K("deleting tag.poster throws in some browsers",e)}},t.prototype.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(this.autoplay())},t.prototype.manualAutoplay_=function(e){var n=this;if(!(!this.tech_||typeof e!="string")){var s=function(){var l=n.muted();n.muted(!0);var f=n.play();if(!(!f||!f.then||!f.catch))return f.catch(function(g){n.muted(l)})},a=void 0;if(e==="any"?(a=this.play(),a&&a.then&&a.catch&&a.catch(function(){return s()})):e==="muted"?a=s():a=this.play(),!(!a||!a.then||!a.catch))return a.then(function(){n.trigger({type:"autoplay-success",autoplay:e})}).catch(function(o){n.trigger({type:"autoplay-failure",autoplay:e})})}},t.prototype.updateSourceCaches_=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=e,s="";typeof n!="string"&&(n=e.src,s=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],n&&!s&&(s=bs(this,n)),this.cache_.source=ie({},e,{src:n,type:s});for(var a=this.cache_.sources.filter(function(x){return x.src&&x.src===n}),o=[],l=this.$$("source"),f=[],g=0;g<l.length;g++){var y=ke(l[g]);o.push(y),y.src&&y.src===n&&f.push(y.src)}f.length&&!a.length?this.cache_.sources=o:a.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=n},t.prototype.handleTechSourceset_=function(e){var n=this;if(!this.changingSrc_){var s=function(g){return n.updateSourceCaches_(g)},a=this.currentSource().src,o=e.src;if(a&&!/^blob:/.test(a)&&/^blob:/.test(o)&&(!this.lastSource_||this.lastSource_.tech!==o&&this.lastSource_.player!==a)&&(s=function(){}),s(o),!e.src){var l=function f(g){if(g.type!=="sourceset"){var y=n.techGet("currentSrc");n.lastSource_.tech=y,n.updateSourceCaches_(y)}n.tech_.off(["sourceset","loadstart"],f)};this.tech_.one(["sourceset","loadstart"],l)}}this.lastSource_={player:this.currentSource().src,tech:e.src},this.trigger({src:e.src,type:"sourceset"})},t.prototype.hasStarted=function(e){if(e===void 0)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},t.prototype.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},t.prototype.handleTechRateChange_=function(){this.tech_.playbackRate()>0&&this.cache_.lastPlaybackRate===0&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},t.prototype.handleTechWaiting_=function(){var e=this;this.addClass("vjs-waiting"),this.trigger("waiting"),this.one("timeupdate",function(){return e.removeClass("vjs-waiting")})},t.prototype.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},t.prototype.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},t.prototype.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},t.prototype.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},t.prototype.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.trigger("seeked")},t.prototype.handleTechFirstPlay_=function(){this.options_.starttime&&(K.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},t.prototype.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},t.prototype.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},t.prototype.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},t.prototype.handleTechClick_=function(e){Ve(e)&&this.controls_&&(this.paused()?ut(this.play()):this.pause())},t.prototype.handleTechTap_=function(){this.userActive(!this.userActive())},t.prototype.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},t.prototype.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},t.prototype.handleTechTouchEnd_=function(e){e.preventDefault()},t.prototype.handleFullscreenChange_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},t.prototype.handleStageClick_=function(){this.reportUserActivity()},t.prototype.handleTechFullscreenChange_=function(e,n){n&&this.isFullscreen(n.isFullscreen),this.trigger("fullscreenchange")},t.prototype.handleTechError_=function(){var e=this.tech_.error();this.error(e)},t.prototype.handleTechTextData_=function(){var e=null;arguments.length>1&&(e=arguments[1]),this.trigger("textdata",e)},t.prototype.getCache=function(){return this.cache_},t.prototype.techCall_=function(e,n){this.ready(function(){if(e in gs)return fs(this.middleware_,this.tech_,e,n);if(e in In)return On(this.middleware_,this.tech_,e,n);try{this.tech_&&this.tech_[e](n)}catch(s){throw K(s),s}},!0)},t.prototype.techGet_=function(e){if(!(!this.tech_||!this.tech_.isReady_)){if(e in ps)return ds(this.middleware_,this.tech_,e);if(e in In)return On(this.middleware_,this.tech_,e);try{return this.tech_[e]()}catch(n){throw this.tech_[e]===void 0?(K("Video.js: "+e+" method not defined for "+this.techName_+" playback technology.",n),n):n.name==="TypeError"?(K("Video.js: "+e+" unavailable on "+this.techName_+" playback technology element.",n),this.tech_.isReady_=!1,n):(K(n),n)}}},t.prototype.play=function(){var e=this,n=this.options_.Promise||d.Promise;return n?new n(function(s){e.play_(s)}):this.play_()},t.prototype.play_=function(){var e=this,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ut;if(this.playOnLoadstart_&&this.off("loadstart",this.playOnLoadstart_),this.isReady_)if(!this.changingSrc_&&(this.src()||this.currentSrc())){n(this.techGet_("play"));return}else this.playOnLoadstart_=function(){e.playOnLoadstart_=null,n(e.play())},this.one("loadstart",this.playOnLoadstart_);else{if(this.playWaitingForReady_)return;this.playWaitingForReady_=!0,this.ready(function(){e.playWaitingForReady_=!1,n(e.play())})}},t.prototype.pause=function(){this.techCall_("pause")},t.prototype.paused=function(){return this.techGet_("paused")!==!1},t.prototype.played=function(){return this.techGet_("played")||Re(0,0)},t.prototype.scrubbing=function(e){if(typeof e>"u")return this.scrubbing_;this.scrubbing_=!!e,e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},t.prototype.currentTime=function(e){if(typeof e<"u"){e<0&&(e=0),this.techCall_("setCurrentTime",e);return}return this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime},t.prototype.duration=function(e){if(e===void 0)return this.cache_.duration!==void 0?this.cache_.duration:NaN;e=parseFloat(e),e<0&&(e=1/0),e!==this.cache_.duration&&(this.cache_.duration=e,e===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),this.trigger("durationchange"))},t.prototype.remainingTime=function(){return this.duration()-this.currentTime()},t.prototype.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},t.prototype.buffered=function(){var e=this.techGet_("buffered");return(!e||!e.length)&&(e=Re(0,0)),e},t.prototype.bufferedPercent=function(){return mn(this.buffered(),this.duration())},t.prototype.bufferedEnd=function(){var e=this.buffered(),n=this.duration(),s=e.end(e.length-1);return s>n&&(s=n),s},t.prototype.volume=function(e){var n=void 0;if(e!==void 0){n=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=n,this.techCall_("setVolume",n),n>0&&this.lastVolume_(n);return}return n=parseFloat(this.techGet_("volume")),isNaN(n)?1:n},t.prototype.muted=function(e){if(e!==void 0){this.techCall_("setMuted",e);return}return this.techGet_("muted")||!1},t.prototype.defaultMuted=function(e){return e!==void 0?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},t.prototype.lastVolume_=function(e){if(e!==void 0&&e!==0){this.cache_.lastVolume=e;return}return this.cache_.lastVolume},t.prototype.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},t.prototype.isFullscreen=function(e){if(e!==void 0){this.isFullscreen_=!!e;return}return!!this.isFullscreen_},t.prototype.requestFullscreen=function(){var e=bt;this.isFullscreen(!0),e.requestFullscreen?(Ce(u,e.fullscreenchange,U(this,function n(s){this.isFullscreen(u[e.fullscreenElement]),this.isFullscreen()===!1&&pe(u,e.fullscreenchange,n),this.trigger("fullscreenchange")})),this.el_[e.requestFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},t.prototype.exitFullscreen=function(){var e=bt;this.isFullscreen(!1),e.requestFullscreen?u[e.exitFullscreen]():this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},t.prototype.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=u.documentElement.style.overflow,Ce(u,"keydown",U(this,this.fullWindowOnEscKey)),u.documentElement.style.overflow="hidden",je(u.body,"vjs-full-window"),this.trigger("enterFullWindow")},t.prototype.fullWindowOnEscKey=function(e){e.keyCode===27&&(this.isFullscreen()===!0?this.exitFullscreen():this.exitFullWindow())},t.prototype.exitFullWindow=function(){this.isFullWindow=!1,pe(u,"keydown",this.fullWindowOnEscKey),u.documentElement.style.overflow=this.docOrigOverflow,rt(u.body,"vjs-full-window"),this.trigger("exitFullWindow")},t.prototype.canPlayType=function(e){for(var n=void 0,s=0,a=this.options_.techOrder;s<a.length;s++){var o=a[s],l=re.getTech(o);if(l||(l=E.getComponent(o)),!l){K.error('The "'+o+'" tech is undefined. Skipped browser support check for that tech.');continue}if(l.isSupported()&&(n=l.canPlayType(e),n))return n}return""},t.prototype.selectSource=function(e){var n=this,s=this.options_.techOrder.map(function(g){return[g,re.getTech(g)]}).filter(function(g){var y=g[0],x=g[1];return x?x.isSupported():(K.error('The "'+y+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),a=function(y,x,N){var Z=void 0;return y.some(function(Se){return x.some(function(Te){if(Z=N(Se,Te),Z)return!0})}),Z},o=void 0,l=function(y){return function(x,N){return y(N,x)}},f=function(y,x){var N=y[0],Z=y[1];if(Z.canPlaySource(x,n.options_[N.toLowerCase()]))return{source:x,tech:N}};return this.options_.sourceOrder?o=a(e,s,l(f)):o=a(s,e,f),o||!1},t.prototype.src=function(e){var n=this;if(typeof e>"u")return this.cache_.src||"";var s=Ts(e);if(!s.length){this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0);return}this.changingSrc_=!0,this.cache_.sources=s,this.updateSourceCaches_(s[0]),cs(this,s[0],function(a,o){n.middleware_=o,n.cache_.sources=s,n.updateSourceCaches_(a);var l=n.src_(a);if(l){if(s.length>1)return n.src(s.slice(1));n.changingSrc_=!1,n.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),n.triggerReady();return}hs(o,n.tech_)})},t.prototype.src_=function(e){var n=this,s=this.selectSource([e]);return s?Ui(s.tech,this.techName_)?(this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0),!1):(this.changingSrc_=!0,this.loadTech_(s.tech,s.source),this.tech_.ready(function(){n.changingSrc_=!1}),!1):!0},t.prototype.load=function(){this.techCall_("load")},t.prototype.reset=function(){this.tech_&&this.tech_.clearTracks("text"),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset")},t.prototype.currentSources=function(){var e=this.currentSource(),n=[];return Object.keys(e).length!==0&&n.push(e),this.cache_.sources||n},t.prototype.currentSource=function(){return this.cache_.source||{}},t.prototype.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},t.prototype.currentType=function(){return this.currentSource()&&this.currentSource().type||""},t.prototype.preload=function(e){if(e!==void 0){this.techCall_("setPreload",e),this.options_.preload=e;return}return this.techGet_("preload")},t.prototype.autoplay=function(e){if(e===void 0)return this.options_.autoplay||!1;var n=void 0;typeof e=="string"&&/(any|play|muted)/.test(e)?(this.options_.autoplay=e,this.manualAutoplay_(e),n=!1):e?this.options_.autoplay=!0:this.options_.autoplay=!1,n=n||this.options_.autoplay,this.tech_&&this.techCall_("setAutoplay",n)},t.prototype.playsinline=function(e){return e!==void 0?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},t.prototype.loop=function(e){if(e!==void 0){this.techCall_("setLoop",e),this.options_.loop=e;return}return this.techGet_("loop")},t.prototype.poster=function(e){if(e===void 0)return this.poster_;e||(e=""),e!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},t.prototype.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var e=this.tech_.poster()||"";e!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},t.prototype.controls=function(e){if(e===void 0)return!!this.controls_;e=!!e,this.controls_!==e&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},t.prototype.usingNativeControls=function(e){if(e===void 0)return!!this.usingNativeControls_;e=!!e,this.usingNativeControls_!==e&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},t.prototype.error=function(e){if(e===void 0)return this.error_||null;if(e===null){this.error_=e,this.removeClass("vjs-error"),this.errorDisplay&&this.errorDisplay.close();return}this.error_=new fe(e),this.addClass("vjs-error"),K.error("(CODE:"+this.error_.code+" "+fe.errorTypes[this.error_.code]+")",this.error_.message,this.error_),this.trigger("error")},t.prototype.reportUserActivity=function(e){this.userActivity_=!0},t.prototype.userActive=function(e){if(e===void 0)return this.userActive_;if(e=!!e,e!==this.userActive_){if(this.userActive_=e,this.userActive_){this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),this.trigger("useractive");return}this.tech_&&this.tech_.one("mousemove",function(n){n.stopPropagation(),n.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},t.prototype.listenForUserActivity_=function(){var e=void 0,n=void 0,s=void 0,a=U(this,this.reportUserActivity),o=function(x){(x.screenX!==n||x.screenY!==s)&&(n=x.screenX,s=x.screenY,a())},l=function(){a(),this.clearInterval(e),e=this.setInterval(a,250)},f=function(x){a(),this.clearInterval(e)};this.on("mousedown",l),this.on("mousemove",o),this.on("mouseup",f),this.on("keydown",a),this.on("keyup",a);var g=void 0;this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(g);var y=this.options_.inactivityTimeout;y<=0||(g=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},y))}},250)},t.prototype.playbackRate=function(e){if(e!==void 0){this.techCall_("setPlaybackRate",e);return}return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1},t.prototype.defaultPlaybackRate=function(e){return e!==void 0?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},t.prototype.isAudio=function(e){if(e!==void 0){this.isAudio_=!!e;return}return!!this.isAudio_},t.prototype.addTextTrack=function(e,n,s){if(this.tech_)return this.tech_.addTextTrack(e,n,s)},t.prototype.addRemoteTextTrack=function(e,n){if(this.tech_)return this.tech_.addRemoteTextTrack(e,n)},t.prototype.removeRemoteTextTrack=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.track,s=n===void 0?arguments[0]:n;if(this.tech_)return this.tech_.removeRemoteTextTrack(s)},t.prototype.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},t.prototype.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},t.prototype.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},t.prototype.language=function(e){if(e===void 0)return this.language_;this.language_=String(e).toLowerCase()},t.prototype.languages=function(){return ie(t.prototype.options_.languages,this.languages_)},t.prototype.toJSON=function(){var e=ie(this.options_),n=e.tracks;e.tracks=[];for(var s=0;s<n.length;s++){var a=n[s];a=ie(a),a.player=void 0,e.tracks[s]=a}return e},t.prototype.createModal=function(e,n){var s=this;n=n||{},n.content=e||"";var a=new Ue(this,n);return this.addChild(a),a.on("dispose",function(){s.removeChild(a)}),a.open(),a},t.prototype.updateCurrentBreakpoint_=function(){if(this.responsive())for(var e=this.currentBreakpoint(),n=this.currentWidth(),s=0;s<xr.length;s++){var a=xr[s],o=this.breakpoints_[a];if(n<=o){if(e===a)return;e&&this.removeClass(Pt[e]),this.addClass(Pt[a]),this.breakpoint_=a;break}}},t.prototype.removeCurrentBreakpoint_=function(){var e=this.currentBreakpointClass();this.breakpoint_="",e&&this.removeClass(e)},t.prototype.breakpoints=function(e){return e===void 0?J(this.breakpoints_):(this.breakpoint_="",this.breakpoints_=J({},Gs,e),this.updateCurrentBreakpoint_(),J(this.breakpoints_))},t.prototype.responsive=function(e){if(e===void 0)return this.responsive_;e=!!e;var n=this.responsive_;if(e!==n)return this.responsive_=e,e?(this.on("playerresize",this.updateCurrentBreakpoint_),this.updateCurrentBreakpoint_()):(this.off("playerresize",this.updateCurrentBreakpoint_),this.removeCurrentBreakpoint_()),e},t.prototype.currentBreakpoint=function(){return this.breakpoint_},t.prototype.currentBreakpointClass=function(){return Pt[this.breakpoint_]||""},t.getTagSettings=function(e){var n={sources:[],tracks:[]},s=ke(e),a=s["data-setup"];if(Ie(e,"vjs-fill")&&(s.fill=!0),Ie(e,"vjs-fluid")&&(s.fluid=!0),a!==null){var o=C(a||"{}"),l=o[0],f=o[1];l&&K.error(l),J(s,f)}if(J(n,s),e.hasChildNodes())for(var g=e.childNodes,y=0,x=g.length;y<x;y++){var N=g[y],Z=N.nodeName.toLowerCase();Z==="source"?n.sources.push(ke(N)):Z==="track"&&n.tracks.push(ke(N))}return n},t.prototype.flexNotSupported_=function(){var e=u.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},t}(E);ge.names.forEach(function(i){var t=ge[i];ue.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),ue.players={};var gt=d.navigator;ue.prototype.options_={techOrder:re.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","controlBar","errorDisplay","textTrackSettings"],language:gt&&(gt.languages&&gt.languages[0]||gt.userLanguage||gt.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media.",breakpoints:{},responsive:!1},h||ue.prototype.options_.children.push("resizeManager"),["ended","seeking","seekable","networkState","readyState"].forEach(function(i){ue.prototype[i]=function(){return this.techGet_(i)}}),gi.forEach(function(i){ue.prototype["handleTech"+ae(i)+"_"]=function(){return this.trigger(i)}}),E.registerComponent("Player",ue);var Ot="plugin",Ye="activePlugins_",Je={},It=function(t){return Je.hasOwnProperty(t)},jt=function(t){return It(t)?Je[t]:void 0},vi=function(t,r){t[Ye]=t[Ye]||{},t[Ye][r]=!0},Dt=function(t,r,e){var n=(e?"before":"")+"pluginsetup";t.trigger(n,r),t.trigger(n+":"+r.name,r)},Ys=function(t,r){var e=function(){Dt(this,{name:t,plugin:r,instance:null},!0);var s=r.apply(this,arguments);return vi(this,t),Dt(this,{name:t,plugin:r,instance:s}),s};return Object.keys(r).forEach(function(n){e[n]=r[n]}),e},yi=function(t,r){return r.prototype.name=t,function(){Dt(this,{name:t,plugin:r,instance:null},!0);for(var e=arguments.length,n=Array(e),s=0;s<e;s++)n[s]=arguments[s];var a=new(Function.prototype.bind.apply(r,[null].concat([this].concat(n))));return this[t]=function(){return a},Dt(this,a.getEventHash()),a}},Ee=function(){function i(t){if(b(this,i),this.constructor===i)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=t,Wt(this),delete this.trigger,vn(this,this.constructor.defaultState),vi(t,this.name),this.dispose=U(this,this.dispose),t.on("dispose",this.dispose)}return i.prototype.version=function(){return this.constructor.VERSION},i.prototype.getEventHash=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return r.name=this.name,r.plugin=this.constructor,r.instance=this,r},i.prototype.trigger=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return He(this.eventBusEl_,r,this.getEventHash(e))},i.prototype.handleStateChanged=function(r){},i.prototype.dispose=function(){var r=this.name,e=this.player;this.trigger("dispose"),this.off(),e.off("dispose",this.dispose),e[Ye][r]=!1,this.player=this.state=null,e[r]=yi(r,Je[r])},i.isBasic=function(r){var e=typeof r=="string"?jt(r):r;return typeof e=="function"&&!i.prototype.isPrototypeOf(e.prototype)},i.registerPlugin=function(r,e){if(typeof r!="string")throw new Error('Illegal plugin name, "'+r+'", must be a string, was '+(typeof r>"u"?"undefined":j(r))+".");if(It(r))K.warn('A plugin named "'+r+'" already exists. You may want to avoid re-registering plugins!');else if(ue.prototype.hasOwnProperty(r))throw new Error('Illegal plugin name, "'+r+'", cannot share a name with an existing player method!');if(typeof e!="function")throw new Error('Illegal plugin for "'+r+'", must be a function, was '+(typeof e>"u"?"undefined":j(e))+".");return Je[r]=e,r!==Ot&&(i.isBasic(e)?ue.prototype[r]=Ys(r,e):ue.prototype[r]=yi(r,e)),e},i.deregisterPlugin=function(r){if(r===Ot)throw new Error("Cannot de-register base plugin.");It(r)&&(delete Je[r],delete ue.prototype[r])},i.getPlugins=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Object.keys(Je),e=void 0;return r.forEach(function(n){var s=jt(n);s&&(e=e||{},e[n]=s)}),e},i.getPluginVersion=function(r){var e=jt(r);return e&&e.VERSION||""},i}();Ee.getPlugin=jt,Ee.BASE_PLUGIN_NAME=Ot,Ee.registerPlugin(Ot,Ee),ue.prototype.usingPlugin=function(i){return!!this[Ye]&&this[Ye][i]===!0},ue.prototype.hasPlugin=function(i){return!!It(i)};var Js=function(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+(typeof r>"u"?"undefined":j(r)));t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),r&&(t.super_=r)},Qs=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=function(){t.apply(this,arguments)},n={};(typeof r>"u"?"undefined":j(r))==="object"?(r.constructor!==Object.prototype.constructor&&(e=r.constructor),n=r):typeof r=="function"&&(e=r),Js(e,t);for(var s in n)n.hasOwnProperty(s)&&(e.prototype[s]=n[s]);return e};typeof HTMLVideoElement>"u"&&Ae()&&(u.createElement("video"),u.createElement("audio"),u.createElement("track"),u.createElement("video-js"));var mi=function(t){return t.indexOf("#")===0?t.slice(1):t};function O(i,t,r){var e=O.getPlayer(i);if(e)return t&&K.warn('Player "'+i+'" is already initialised. Options will not be applied.'),r&&e.ready(r),e;var n=typeof i=="string"?Pe("#"+mi(i)):i;if(!et(n))throw new TypeError("The element or ID supplied is not valid. (videojs)");u.body.contains(n)||K.warn("The element supplied is not included in the DOM"),t=t||{},O.hooks("beforesetup").forEach(function(a){var o=a(n,ie(t));if(!he(o)||Array.isArray(o)){K.error("please return an object in beforesetup hooks");return}t=ie(t,o)});var s=E.getComponent("Player");return e=new s(n,t,r),O.hooks("setup").forEach(function(a){return a(e)}),e}if(O.hooks_={},O.hooks=function(i,t){return O.hooks_[i]=O.hooks_[i]||[],t&&(O.hooks_[i]=O.hooks_[i].concat(t)),O.hooks_[i]},O.hook=function(i,t){O.hooks(i,t)},O.hookOnce=function(i,t){O.hooks(i,[].concat(t).map(function(r){var e=function n(){return O.removeHook(i,n),r.apply(void 0,arguments)};return e}))},O.removeHook=function(i,t){var r=O.hooks(i).indexOf(t);return r<=-1?!1:(O.hooks_[i]=O.hooks_[i].slice(),O.hooks_[i].splice(r,1),!0)},d.VIDEOJS_NO_DYNAMIC_STYLE!==!0&&Ae()){var Mt=Pe(".vjs-styles-defaults");if(!Mt){Mt=cn("vjs-styles-defaults");var wr=Pe("head");wr&&wr.insertBefore(Mt,wr.firstChild),hn(Mt,`
      .video-js {
        width: 300px;
        height: 150px;
      }

      .vjs-fluid {
        padding-top: 56.25%
      }
    `)}}return Ht(1,O),O.VERSION=D,O.options=ue.prototype.options_,O.getPlayers=function(){return ue.players},O.getPlayer=function(i){var t=ue.players,r=void 0;if(typeof i=="string"){var e=mi(i),n=t[e];if(n)return n;r=Pe("#"+e)}else r=i;if(et(r)){var s=r,a=s.player,o=s.playerId;if(a||t[o])return a||t[o]}},O.getAllPlayers=function(){return Object.keys(ue.players).map(function(i){return ue.players[i]}).filter(Boolean)},O.players=ue.players,O.getComponent=E.getComponent,O.registerComponent=function(i,t){re.isTech(t)&&K.warn("The "+i+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),E.registerComponent.call(E,i,t)},O.getTech=re.getTech,O.registerTech=re.registerTech,O.use=us,!h&&Object.defineProperty?(Object.defineProperty(O,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(O.middleware,"TERMINATOR",{value:dt,writeable:!1,enumerable:!0})):O.middleware={TERMINATOR:dt},O.browser=V,O.TOUCH_ENABLED=$,O.extend=Qs,O.mergeOptions=ie,O.bind=U,O.registerPlugin=Ee.registerPlugin,O.deregisterPlugin=Ee.deregisterPlugin,O.plugin=function(i,t){return K.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Ee.registerPlugin(i,t)},O.getPlugins=Ee.getPlugins,O.getPlugin=Ee.getPlugin,O.getPluginVersion=Ee.getPluginVersion,O.addLanguage=function(i,t){var r;return i=(""+i).toLowerCase(),O.options.languages=ie(O.options.languages,(r={},r[i]=t,r)),O.options.languages[i]},O.log=K,O.createLogger=zr,O.createTimeRange=O.createTimeRanges=Re,O.formatTime=Ke,O.setFormatTime=As,O.resetFormatTime=Ps,O.parseUrl=Jt,O.isCrossOrigin=kt,O.EventTarget=ce,O.on=Ce,O.one=ot,O.off=pe,O.trigger=He,O.xhr=P,O.TextTrack=ct,O.AudioTrack=xn,O.VideoTrack=wn,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(i){O[i]=function(){return K.warn("videojs."+i+"() is deprecated; use videojs.dom."+i+"() instead"),sn[i].apply(null,arguments)}}),O.computedStyle=Rt,O.dom=sn,O.url=is,Hr=O,Hr}(function(c,d){(function(u,T){c.exports=T(ya())})(Be,function(u){return function(T){function C(F){if(P[F])return P[F].exports;var D=P[F]={i:F,l:!1,exports:{}};return T[F].call(D.exports,D,D.exports,C),D.l=!0,D.exports}var P={};return C.m=T,C.c=P,C.i=function(F){return F},C.d=function(F,D,M){C.o(F,D)||Object.defineProperty(F,D,{configurable:!1,enumerable:!0,get:M})},C.n=function(F){var D=F&&F.__esModule?function(){return F.default}:function(){return F};return C.d(D,"a",D),D},C.o=function(F,D){return Object.prototype.hasOwnProperty.call(F,D)},C.p="/",C(C.s=3)}([function(T,C){T.exports=u},function(T,C,P){function F(_,I,W){return I in _?Object.defineProperty(_,I,{value:W,enumerable:!0,configurable:!0,writable:!0}):_[I]=W,_}Object.defineProperty(C,"__esModule",{value:!0});var D=P(0),M=function(_){return _&&_.__esModule?_:{default:_}}(D),G=window.videojs||M.default;typeof Object.assign!="function"&&Object.defineProperty(Object,"assign",{value:function(_,I){if(_==null)throw new TypeError("Cannot convert undefined or null to object");for(var W=Object(_),B=1;B<arguments.length;B++){var q=arguments[B];if(q!=null)for(var X in q)Object.prototype.hasOwnProperty.call(q,X)&&(W[X]=q[X])}return W},writable:!0,configurable:!0});var H=["loadeddata","canplay","canplaythrough","play","pause","waiting","playing","ended","error"];C.default={name:"video-player",props:{start:{type:Number,default:0},crossOrigin:{type:String,default:""},playsinline:{type:Boolean,default:!1},customEventName:{type:String,default:"statechanged"},options:{type:Object,required:!0},events:{type:Array,default:function(){return[]}},globalOptions:{type:Object,default:function(){return{controls:!0,controlBar:{remainingTimeDisplay:!1,playToggle:{},progressControl:{},fullscreenToggle:{},volumeMenuButton:{inline:!1,vertical:!0}},techOrder:["html5"],plugins:{}}}},globalEvents:{type:Array,default:function(){return[]}}},data:function(){return{player:null,reseted:!0}},mounted:function(){this.player||this.initialize()},beforeDestroy:function(){this.player&&this.dispose()},methods:{initialize:function(){var _=this,I=Object.assign({},this.globalOptions,this.options);this.playsinline&&(this.$refs.video.setAttribute("playsinline",this.playsinline),this.$refs.video.setAttribute("webkit-playsinline",this.playsinline),this.$refs.video.setAttribute("x5-playsinline",this.playsinline),this.$refs.video.setAttribute("x5-video-player-type","h5"),this.$refs.video.setAttribute("x5-video-player-fullscreen",!1)),this.crossOrigin!==""&&(this.$refs.video.crossOrigin=this.crossOrigin,this.$refs.video.setAttribute("crossOrigin",this.crossOrigin));var W=function(q,X){q&&_.$emit(q,_.player),X&&_.$emit(_.customEventName,F({},q,X))};I.plugins&&delete I.plugins.__ob__;var B=this;this.player=G(this.$refs.video,I,function(){for(var q=this,X=H.concat(B.events).concat(B.globalEvents),Y={},R=0;R<X.length;R++)typeof X[R]=="string"&&Y[X[R]]===void 0&&function(ne){Y[ne]=null,q.on(ne,function(){W(ne,!0)})}(X[R]);this.on("timeupdate",function(){W("timeupdate",this.currentTime())}),B.$emit("ready",this)})},dispose:function(_){var I=this;this.player&&this.player.dispose&&(this.player.techName_!=="Flash"&&this.player.pause&&this.player.pause(),this.player.dispose(),this.player=null,this.$nextTick(function(){I.reseted=!1,I.$nextTick(function(){I.reseted=!0,I.$nextTick(function(){_&&_()})})}))}},watch:{options:{deep:!0,handler:function(_,I){var W=this;this.dispose(function(){_&&_.sources&&_.sources.length&&W.initialize()})}}}}},function(T,C,P){Object.defineProperty(C,"__esModule",{value:!0});var F=P(1),D=P.n(F);for(var M in F)["default","default"].indexOf(M)<0&&function(I){P.d(C,I,function(){return F[I]})}(M);var G=P(5),H=P(4),_=H(D.a,G.a,!1,null,null,null);C.default=_.exports},function(T,C,P){function F(B){return B&&B.__esModule?B:{default:B}}Object.defineProperty(C,"__esModule",{value:!0}),C.install=C.videoPlayer=C.videojs=void 0;var D=P(0),M=F(D),G=P(2),H=F(G),_=window.videojs||M.default,I=function(B,q){q&&(q.options&&(H.default.props.globalOptions.default=function(){return q.options}),q.events&&(H.default.props.globalEvents.default=function(){return q.events})),B.component(H.default.name,H.default)},W={videojs:_,videoPlayer:H.default,install:I};C.default=W,C.videojs=_,C.videoPlayer=H.default,C.install=I},function(T,C){T.exports=function(P,F,D,M,G,H){var _,I=P=P||{},W=typeof P.default;W!=="object"&&W!=="function"||(_=P,I=P.default);var B=typeof I=="function"?I.options:I;F&&(B.render=F.render,B.staticRenderFns=F.staticRenderFns,B._compiled=!0),D&&(B.functional=!0),G&&(B._scopeId=G);var q;if(H?(q=function(R){R=R||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,R||typeof __VUE_SSR_CONTEXT__>"u"||(R=__VUE_SSR_CONTEXT__),M&&M.call(this,R),R&&R._registeredComponents&&R._registeredComponents.add(H)},B._ssrRegister=q):M&&(q=M),q){var X=B.functional,Y=X?B.render:B.beforeCreate;X?(B._injectStyles=q,B.render=function(R,ne){return q.call(ne),Y(R,ne)}):B.beforeCreate=Y?[].concat(Y,q):[q]}return{esModule:_,exports:I,options:B}}},function(T,C,P){var F=function(){var G=this,H=G.$createElement,_=G._self._c||H;return G.reseted?_("div",{staticClass:"video-player"},[_("video",{ref:"video",staticClass:"video-js"})]):G._e()},D=[],M={render:F,staticRenderFns:D};C.a=M}])})})(Mi);var ma=Mi.exports;const _a={components:{"vue-video-player":ma.videoPlayer},name:"DetailAdvanced",data(){return{videoUrl:"",caseData:{scripts:[]},runData:{runResult:"",fileTag:""},updateCurrent:0,showLogPanel:!1,showWarnings:!0,caseInfoData:[{name:"用例ID",value:1}],runInfoData:[{name:"用例ID",value:1}],logContent:"",logContentError:"",progressPercentage:0,progressStatus:"default",logRefreshTimer:null,markDialogVisible:!1,markForm:{runResult:"fail",reason:"",runInfo:"",remark:""},reasonOptions:[{label:"业务BUG",value:"biz_bug"},{label:"业务变更",value:"biz_change"},{label:"环境异常",value:"env_bug"},{label:"脚本问题",value:"script_bug"},{label:"其他原因",value:"other"}],testDetailData:null,caseId:"",configId:"",showTestDetail:!1,aiAnalyzing:!1,aiDialogVisible:!1,aiAnalysisResult:"",currentErrorText:""}},async mounted(){const c=this.$route.query.id;c&&(this.logRefreshTimer=setInterval(()=>{this.runData.status==="running"?this.fetchRunData(c):clearInterval(this.logRefreshTimer)},4e3));const d=this.$route.query.caseId;d&&d!=="0"&&this.$request.get(`/cases/${d}`).then(u=>{this.caseData=u.data;const T=u.data;this.caseInfoData=[{name:"用例名称",value:T.caseName},{name:"归属业务域",value:T.app},{name:"用例分组",value:T.groupName.join(", ")},{name:"步骤总数",value:T.scripts.reduce((C,P)=>C+JSON.parse(P.content).length,0)},{name:"配置列表",value:T.config.configName}]}).catch(u=>{console.log(u)})},methods:{handleSeek(){const d=this.$refs.videoPlayer.player.el().querySelector("video");if(d&&d.buffered.length>0){const u=`bytes=${d.buffered.end(0)}-`;fetch(d.src,{headers:{Range:u},cache:"force-cache"})}},parseProgress(c){if(!c)return;const d=c.split(`
`),u=d[d.length-1]||d[d.length-2];if(u){const T=u.match(/\[(\d+)%\]/);T&&(this.progressPercentage=parseInt(T[1]),this.progressStatus=this.progressPercentage===100?"success":"default")}},countStatus(c){return c?c.runResult==="success"?"success":"fail":"未知"},viewReport(){if(this.runData.status==="running"){this.$message.error("正在执行中，请稍后");return}window.open(`/notify/getReports?caseName=${this.runData.caseName}&fileTag=${this.runData.fileTag}&groupName=${this.runData.groupName}`,"_blank")},stopExecution(){if(this.runData.status!=="running"&&this.runData.status!=="pending"){this.$message.error("已执行完成");return}const{caseName:c}=this.runData;c?this.$request.post("/stopTest",{caseName:c,id:this.$route.query.id}).then(()=>{this.$message.success("已终止执行");const d=this.$route.query.id;d&&this.fetchRunData(d)}).catch(d=>{console.log(d),this.$message.error("终止执行失败")}):this.$message.error("用例名称不存在")},onAlertChange(c){console.log(c)},statusType(c){switch(c){case"success":return"success";case"fail":return"danger";case"pending":return"warning";default:return"default"}},fetchRunData(c){this.$request.get(`/records/${c}`).then(d=>{const u=d.data;this.runData=d.data;let T={caseList:[],userAgent:"",status:"fail",duration:0};T=JSON.parse(u.runInfo),this.$request.get(`/records/getLogs?fileTag=${u.fileTag}`).then(C=>{console.log(C),this.logContent=C.data,this.parseProgress(C.data)}),u.status==="failed"&&(this.logContentError=u.runInfo),this.videoUrl=`/records/video/${this.runData.fileTag}.mp4`,this.runInfoData=[{name:"用例ID",value:u.caseId},{name:"开始时间",value:new Date(u.startTime).toLocaleString()},{name:"结束时间",value:new Date(u.endTime).toLocaleString()},{name:"执行进度",value:u.status},{name:"执行人",value:u.operator},{name:"用例耗时(ms)",value:T.duration},{name:"userAgent",value:T.userAgent}],u.status==="running"?this.updateCurrent=1:(u.status==="success"||u.status==="fail")&&(this.updateCurrent=2,u.fileTag&&this.fetchTestDetail(u.fileTag))}).catch(d=>{console.log(d)})},fetchCaseData(c){this.$request.get(`/cases/${c}`).then(d=>{const u=d.data;this.caseId=c,this.configId=u.config.id||u.config._id||"",this.caseInfoData=[{name:"用例名称",value:u.caseName},{name:"创建时间",value:new Date(u.createdAt).toLocaleString()},{name:"创建人",value:u.creator},{name:"编辑时间",value:new Date(u.updatedAt).toLocaleString()},{name:"编辑人",value:u.updater},{name:"归属业务域",value:u.app},{name:"用例分组",value:u.groupName.join(", ")},{name:"步骤总数",value:u.scripts.reduce((T,C)=>T+JSON.parse(C.content).length,0)},{name:"配置列表",value:u.config.configName},{name:"备注",value:u.status}]}).catch(d=>{console.log(d)})},fetchTestDetail(c){this.$request.get(`/records/getJsonFiles?fileTag=${c}`).then(d=>{this.testDetailData=d.data,this.showTestDetail=!0,console.log("测试详细信息:",d.data)}).catch(d=>{console.log("获取测试详细信息失败:",d),this.$message.error("获取测试详细信息失败")})},parseScreenshotFromError(c){const d=c.match(/Screenshot:\s*([^\s]+\.png)/i);if(d){let u=d[1];if(u.includes(":\\")||u.includes(":/")){const T=u.toLowerCase().indexOf("screenshots");T!==-1&&(u="../"+u.substring(T).replace(/\\/g,"/"))}return u}return null},getScreenshotUrl(c){return c?`${c}`:null},previewScreenshot(c){if(!c)return;const d=window.open("","_blank","width=800,height=600,scrollbars=yes,resizable=yes");d&&(d.document.write(`
          <html>
            <head><title>错误截图预览</title></head>
            <body style="margin: 0; padding: 20px; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh;">
              <img src="${c}" style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);" alt="错误截图" />
            </body>
          </html>
        `),d.document.close())},editCase(){if(!this.caseId){this.$message.warning("用例ID不存在，无法跳转到编辑页面");return}this.$router.push(`/userCase/edit?id=${this.caseId}`)},editConfig(){if(!this.configId){this.$message.warning("配置ID不存在，无法跳转到编辑页面");return}this.$router.push(`/config/edit?id=${this.configId}`)},showMarkDialog(){if(!this.runData||!this.runData.id){this.$message.error("执行记录数据不完整");return}this.markForm.reason=this.runData.reason||"",this.markForm.remark=this.runData.remark||"",this.markForm.runResult="fail";let c={};if(this.runData.runInfo)try{c=JSON.parse(this.runData.runInfo),this.markForm.runInfo=this.runData.runInfo}catch(d){this.markForm.runInfo="",console.warn("runInfo is not valid JSON, will create new structured runInfo for marking failure.",d)}else this.markForm.runInfo="";this.markDialogVisible=!0},handleMarkConfirm(){if(!this.markForm.reason){this.$message.warning("请选择原因");return}if(!this.runData||!this.runData.id){this.$message.error("执行记录ID不存在");return}let c={};if(this.markForm.runInfo)try{c=JSON.parse(this.markForm.runInfo)}catch(d){console.error("Error parsing markForm.runInfo:",d)}this.$request.post(`/records/${this.runData.id}`,{runResult:"fail",reason:this.markForm.reason,remark:this.markForm.remark}).then(()=>{this.markDialogVisible=!1,this.$message.success("标记成功");const d=this.$route.query.id;d&&this.fetchRunData(d)}).catch(d=>{console.error(d),this.$message.error("标记失败")})},async analyzeError(c){this.currentErrorText="错误日志："+c+"执行日志："+this.filteredLogContent,this.aiDialogVisible=!0,this.aiAnalyzing=!0,this.aiAnalysisResult="";try{const d=`ai_analysis_${this.$route.query.id}`,u=sessionStorage.getItem(d);if(u){this.aiAnalysisResult=u,this.aiAnalyzing=!1;return}const T=await fetch("https://open.bigmodel.cn/api/paas/v4/chat/completions",{method:"POST",headers:{Authorization:"Bearer 2bda76eaa4244703abc6b9ce755244e3.v3ZJ6jZQZp65VgC3","Content-Type":"application/json"},body:JSON.stringify({model:"glm-4.5",do_sample:!0,stream:!1,thinking:{type:"enabled"},temperature:.6,top_p:.95,response_format:{type:"text"},meta:{user_info:"UI测试专家",bot_name:"UI测试专家",bot_info:"UI测试专家，擅长解决UI测试中出现的问题",user_name:"测试"},request_id:"sanliang",messages:[{role:"user",content:c+"使用testcafe时出现的错误日志，请分析总结错误原因，不超过100字"}]})});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const C=await T.json();if(C.choices&&C.choices.length>0&&C.choices[0].message){const P=C.choices[0].message.content;this.aiAnalysisResult=P,sessionStorage.setItem(d,P)}else this.aiAnalysisResult="抱歉，AI分析服务暂时无法提供分析结果，请稍后重试。"}catch(d){console.error("AI分析请求失败:",d),this.aiAnalysisResult="抱歉，AI分析服务请求失败，请检查网络连接或稍后重试。错误信息: "+d.message}finally{this.aiAnalyzing=!1}}},computed:{videoOptions(){return{playbackRates:[.5,1.5,3,5],sources:[{type:"video/mp4",src:this.videoUrl||""}],controls:!0,autoplay:!1,fluid:!0,responsive:!0,controlBar:{progressControl:{seekBar:{mouseTimeDisplay:!0}},remainingTimeDisplay:{displayNegative:!1}},html5:{vhs:{enableLowInitialPlaylist:!0,smoothQualityChange:!0,overrideNative:!0},nativeVideoTracks:!1,nativeAudioTracks:!1,nativeTextTracks:!1}}},filteredLogContent(){return this.logContent?this.showWarnings?this.logContent.split(`
`).filter(c=>c.includes("[RUNNING]")).join(`
`):this.logContent:""},mergedInfoData(){const c=[];return this.caseInfoData&&this.caseInfoData.length>0&&c.push(...this.caseInfoData),this.runInfoData&&this.runInfoData.length>0&&c.push(...this.runInfoData),c},formattedAnalysisResult(){if(!this.aiAnalysisResult)return"";let c=this.aiAnalysisResult.replace(/^#### (.*$)/gim,'<h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">$1</h4>').replace(/^### (.*$)/gim,'<h3 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">$1</h3>').replace(/^## (.*$)/gim,'<h2 style="color: #333; margin: 20px 0 10px 0; font-size: 18px; font-weight: bold;">$1</h2>').replace(/^# (.*$)/gim,'<h1 style="color: #333; margin: 24px 0 12px 0; font-size: 20px; font-weight: bold;">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong style="font-weight: bold; color: #d73027;">$1</strong>').replace(/```([\s\S]*?)```/g,'<pre style="background: #f1f3f4; padding: 12px; border-radius: 4px; margin: 8px 0; overflow-x: auto; border: 1px solid #e1e4e8;"><code style="font-family: Consolas, Monaco, monospace; font-size: 13px;">$1</code></pre>').replace(/`([^`]+)`/g,'<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px; font-family: Consolas, Monaco, monospace; font-size: 13px; color: #d73027;">$1</code>').replace(/^\* (.*$)/gim,'<li style="margin: 4px 0; padding-left: 8px;">$1</li>').replace(/(<li[^>]*>.*<\/li>)/,'<ul style="margin: 8px 0; padding-left: 20px;">$1</ul>').replace(/^\d+\. (.*$)/gim,'<li style="margin: 4px 0; padding-left: 8px;">$1</li>').replace(/\n\n/g,'</p><p style="margin: 8px 0; line-height: 1.6;">').replace(/\n/g,"<br>");return!c.includes("<h1>")&&!c.includes("<h2>")&&!c.includes("<h3>")&&!c.includes("<ul>")&&!c.includes("<pre>")&&(c='<p style="margin: 8px 0; line-height: 1.6;">'+c+"</p>"),c}},watch:{"$route.query.id":{handler(c){c&&this.fetchRunData(c)},immediate:!0},"$route.query.caseId":{handler(c){c&&c!="0"&&this.fetchCaseData(c)},immediate:!0}},beforeDestroy(){this.logRefreshTimer&&clearInterval(this.logRefreshTimer)}};var ba=function(){var c=this,d=c.$createElement,u=c._self._c||d;return u("div",{staticClass:"detail-advanced"},[u("t-card",{staticClass:"container-base-margin-top",attrs:{title:"用例信息",bordered:!1},scopedSlots:c._u([{key:"footer",fn:function(){return[u("t-space",[c.runData.runResult!=="success"?u("t-button",{attrs:{variant:"text",theme:"warning"},on:{click:c.showMarkDialog}},[c._v("标记失败原因")]):c._e(),u("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:c.viewReport}},[c._v("查看完整执行报告")]),c.caseId?u("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:c.editCase}},[c._v("查看用例")]):c._e(),c.configId?u("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:c.editConfig}},[c._v("查看配置")]):c._e()],1)]},proxy:!0}])},[u("t-descriptions",c._l(c.mergedInfoData,function(T,C){return u("t-descriptions-item",{key:C,attrs:{label:T.name}},[u("div",[T.name==="执行进度"||T.name==="用例状态"?u("t-tag",{attrs:{theme:c.statusType(T.value),size:"small"}},[c._v(" "+c._s(T.value)+" ")]):u("span",[c._v(c._s(T.value))])],1)])}),1)],1),c.logContent?u("t-card",{staticClass:"container-base-margin-top",attrs:{title:"",bordered:!1},scopedSlots:c._u([{key:"header",fn:function(){return[u("div",{staticStyle:{width:"100%",display:"flex","justify-content":"space-between","align-items":"center"}},[u("h3",[c._v("执行日志")]),u("t-button",{attrs:{theme:c.showWarnings?"primary":"default",size:"small"},on:{click:function(T){c.showWarnings=!c.showWarnings}}},[c._v(" "+c._s(c.showWarnings?"显示更多":"显示更少")+" ")])],1)]},proxy:!0}],null,!1,3655577040)},[u("t-progress",{staticStyle:{"margin-bottom":"16px"},attrs:{theme:"plump",percentage:c.progressPercentage}}),c.logContentError?u("t-alert",{staticStyle:{margin:"10px"},attrs:{theme:"error",message:c.logContentError}}):c._e(),u("div",{staticClass:"log-panel"},[u("pre",[c._v(c._s(c.filteredLogContent))])]),c.videoUrl&&c.runData.runResult!=="success"?u("div",{staticClass:"video-container"},[u("vue-video-player",{ref:"videoPlayer",staticClass:"vjs-custom-skin",attrs:{options:c.videoOptions}})],1):c._e()],1):c._e(),c.showTestDetail&&c.testDetailData?u("t-card",{staticClass:"container-base-margin-top",attrs:{title:"用例错误信息",bordered:!1}},[c._l(c.testDetailData.fixtures,function(T,C){return u("div",{key:C,staticStyle:{"margin-bottom":"16px"}},c._l(T.tests,function(P,F){return u("div",{key:F},[u("t-card",{staticStyle:{"margin-bottom":"8px"},attrs:{size:"small"},scopedSlots:c._u([{key:"header",fn:function(){return[u("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[u("span",[c._v(c._s(P.name))])])]},proxy:!0}],null,!0)},[P.errs.length>0?u("div",{staticStyle:{"margin-top":"12px"}},[u("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"8px"}},[u("h5",{staticStyle:{color:"#e34d59",margin:"0","margin-right":"8px"}},[c._v("错误信息:")]),u("t-button",{attrs:{theme:"primary",variant:"text",size:"small",loading:c.aiAnalyzing},on:{click:function(D){c.analyzeError(P.errs.join(`
`))}},scopedSlots:c._u([{key:"icon",fn:function(){return[u("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"}},[u("path",{attrs:{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.58L19 8l-9 9z"}}),u("circle",{attrs:{cx:"12",cy:"8",r:"2"}}),u("path",{attrs:{d:"M12 14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}})])]},proxy:!0}],null,!0)},[c._v(" AI分析 ")])],1),c._l(P.errs,function(D,M){return u("div",{key:M,staticStyle:{"margin-bottom":"8px"}},[u("t-alert",{staticStyle:{"white-space":"pre-wrap","font-family":"monospace","font-size":"12px"},attrs:{theme:"error"}},[c._v(" "+c._s(D)+" ")]),c.parseScreenshotFromError(D)?u("div",{staticStyle:{"margin-top":"8px"}},[u("h6",{staticStyle:{color:"#666","margin-bottom":"4px"}},[c._v("错误截图:")]),u("div",{staticStyle:{border:"1px solid #ddd","border-radius":"4px",padding:"8px","background-color":"#f9f9f9"}},[u("img",{staticStyle:{"max-width":"100%",height:"auto","border-radius":"4px",cursor:"pointer"},attrs:{src:c.parseScreenshotFromError(D),alt:"错误截图 "+(M+1)},on:{click:function(G){c.previewScreenshot(c.parseScreenshotFromError(D))}}})])]):c._e()],1)})],2):c._e()])],1)}),0)}),c.testDetailData.warnings&&c.testDetailData.warnings.length>0?u("div",{staticStyle:{"margin-top":"16px"}},[u("h4",{staticStyle:{color:"#f2994a"}},[c._v("警告信息")]),c._l(c.testDetailData.warnings,function(T,C){return u("div",{key:C,staticStyle:{"margin-bottom":"8px"}},[u("t-alert",{attrs:{theme:"warning"}},[c._v(c._s(T))])],1)})],2):c._e()],2):c._e(),c.$route.query.caseId!=="0"?u("t-card",{staticClass:"container-base-margin-top",attrs:{title:"脚本执行顺序",bordered:!1}},[u("t-steps",{attrs:{separator:"arrow",readonly:""}},c._l(c.caseData.scripts,function(T,C){return u("t-step-item",{key:C,attrs:{title:T.scriptName,status:"finish",content:JSON.parse(T.content).map(function(P){return P.step}).join(`
`)}})}),1)],1):c._e(),u("t-dialog",{attrs:{header:"失败原因",visible:c.markDialogVisible,onConfirm:c.handleMarkConfirm,onCancel:function(){return c.markDialogVisible=!1}},on:{"update:visible":function(T){c.markDialogVisible=T}}},[u("t-form",{attrs:{data:c.markForm,"label-width":"80px"}},[u("t-form-item",{attrs:{label:"原因"}},[u("t-select",{attrs:{options:c.reasonOptions,placeholder:"请选择失败原因"},model:{value:c.markForm.reason,callback:function(T){c.$set(c.markForm,"reason",T)},expression:"markForm.reason"}})],1),u("t-form-item",{attrs:{label:"备注"}},[u("t-textarea",{attrs:{placeholder:"请输入备注信息"},model:{value:c.markForm.remark,callback:function(T){c.$set(c.markForm,"remark",T)},expression:"markForm.remark"}})],1)],1)],1),u("t-dialog",{attrs:{header:"AI错误分析",visible:c.aiDialogVisible,width:"800px",onCancel:function(){return c.aiDialogVisible=!1},footer:!1},on:{"update:visible":function(T){c.aiDialogVisible=T}}},[u("div",{staticStyle:{"max-height":"500px","overflow-y":"auto"}},[u("div",{staticStyle:{"margin-bottom":"16px"}},[u("h4",{staticStyle:{"margin-bottom":"8px"}},[c._v("错误信息:")]),u("div",{staticStyle:{background:"#f5f5f5",padding:"12px","border-radius":"4px","font-family":"monospace","font-size":"12px","white-space":"pre-wrap","max-height":"150px","overflow-y":"auto"}},[c._v(" "+c._s(c.currentErrorText)+" ")])]),c.aiAnalyzing?u("div",{staticStyle:{"text-align":"center",padding:"20px"}},[u("t-loading",{attrs:{size:"large"}}),u("p",{staticStyle:{"margin-top":"12px",color:"#666"}},[c._v("AI正在分析错误信息，请稍候...")])],1):c.aiAnalysisResult?u("div",{staticStyle:{"margin-top":"16px"}},[u("h4",{staticStyle:{"margin-bottom":"8px"}},[c._v("AI分析结果:")]),u("div",{staticStyle:{background:"#f8f9fa",padding:"16px","border-radius":"4px","border-left":"4px solid #007bff","white-space":"pre-wrap","line-height":"1.6"},domProps:{innerHTML:c._s(c.formattedAnalysisResult)}})]):c._e()])])],1)},Ta=[];const Di={};var Ca=ta(_a,ba,Ta,!1,Sa,null,null,null);function Sa(c){for(let d in Di)this[d]=Di[d]}const ka=function(){return Ca.exports}();export{ka as default};
