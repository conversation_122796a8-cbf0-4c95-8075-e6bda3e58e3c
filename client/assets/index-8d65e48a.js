import{n as r}from"./index-8c8571d2.js";var o=function(){var e=this,l=e.$createElement,a=e._self._c||l;return a("t-dialog",{attrs:{visible:e.visible,header:"预览配置",width:"800px",footer:!1},on:{close:e.handleClose}},[e.previewData?a("div",{staticClass:"preview-content"},[a("t-collapse",{staticClass:"preview-collapse",model:{value:e.collapseValue,callback:function(t){e.collapseValue=t},expression:"collapseValue"}},[a("t-collapse-panel",{attrs:{value:"basic",header:"基本信息"}},[a("t-descriptions",{attrs:{column:2,bordered:""}},e._l(e.basicInfoData,function(t){return a("t-descriptions-item",{key:t.label,attrs:{label:t.label}},[e._v(" "+e._s(t.value)+" ")])}),1)],1),a("t-collapse-panel",{attrs:{value:"env",header:"环境配置"}},[a("t-tabs",{attrs:{theme:"card"},model:{value:e.previewEnv,callback:function(t){e.previewEnv=t},expression:"previewEnv"}},e._l(e.envOptions,function(t){return a("t-tab-panel",{key:t.value,attrs:{value:t.value,label:t.label}},[a("div",{staticClass:"env-config-content"},[a("t-descriptions",{attrs:{layout:"vertical",column:1}},e._l(e.getEnvConfigData(t.value),function(s){return a("t-descriptions-item",{key:s.label,attrs:{label:s.label}},[e._v(" "+e._s(s.value)+" ")])}),1),e.getVariableTableData(t.value).length>0?a("div",[a("t-table",{staticStyle:{padding:"15px"},attrs:{data:e.getVariableTableData(t.value),columns:e.variableColumns,size:"small",pagination:!1}})],1):e._e()],1)])}),1)],1),e.previewData.localstorage&&e.previewData.localstorage.length>0?a("t-collapse-panel",{attrs:{value:"localstorage",header:"LocalStorage配置"}},[a("t-descriptions",{attrs:{column:2,bordered:""}},e._l(e.previewData.localstorage,function(t){return a("t-descriptions-item",{key:t.key,attrs:{label:t.key}},[e._v(" "+e._s(t.value)+" ")])}),1)],1):e._e(),e.previewData.session&&e.previewData.session.length>0?a("t-collapse-panel",{attrs:{value:"session",header:"SessionStorage配置"}},[a("t-descriptions",{attrs:{column:2,bordered:""}},e._l(e.previewData.session,function(t){return a("t-descriptions-item",{key:t.key,attrs:{label:t.key}},[e._v(" "+e._s(t.value)+" ")])}),1)],1):e._e(),e.previewData.cookie&&e.previewData.cookie.length>0?a("t-collapse-panel",{attrs:{value:"cookie",header:"Cookie配置"}},[a("t-descriptions",{attrs:{column:2,bordered:""}},e._l(e.previewData.cookie,function(t){return a("t-descriptions-item",{key:t.key,attrs:{label:t.key}},[e._v(" "+e._s(t.value)+" ")])}),1)],1):e._e(),e.previewData.request&&e.previewData.request.length>0?a("t-collapse-panel",{attrs:{value:"request",header:"Request配置"}},[a("t-descriptions",{attrs:{column:1,bordered:""}},e._l(e.previewData.request,function(t){return a("t-descriptions-item",{key:t.path,attrs:{label:t.path+" ("+t.method+")"}},[e._v(" "+e._s(t.response)+" ")])}),1)],1):e._e()],1)],1):e._e()])},n=[];const v={name:"ConfigPreview",props:{visible:{type:Boolean,default:!1},previewData:{type:Object,default:null}},data(){return{previewEnv:"global",collapseValue:["basic","env"],envOptions:[{label:"全局环境",value:"global"},{label:"测试环境",value:"test"},{label:"预发环境",value:"pre"},{label:"生产环境",value:"prod"},{label:"项目1环境",value:"project1"},{label:"项目2环境",value:"project2"}],variableColumns:[{colKey:"name",title:"变量名",width:150},{colKey:"value",title:"变量值",ellipsis:!0}]}},computed:{basicInfoData(){return this.previewData?[{label:"配置ID",value:this.previewData.id||"未设置"},{label:"配置名称",value:this.previewData.configName||this.previewData.name||"未设置"},{label:"创建人",value:this.previewData.creator||"未设置"},{label:"业务域",value:this.previewData.app||"未设置"},{label:"浏览器宽度",value:this.previewData.width?this.previewData.width+"px":"未设置"},{label:"浏览器高度",value:this.previewData.height?this.previewData.height+"px":"未设置"},{label:"创建时间",value:this.previewData.createdAt?this.$dayjs(this.previewData.createdAt).format("YYYY-MM-DD HH:mm:ss"):"未设置"},{label:"更新时间",value:this.previewData.updatedAt?this.$dayjs(this.previewData.updatedAt).format("YYYY-MM-DD HH:mm:ss"):"未设置"}]:[]},hasMockConfig(){return this.previewData?this.previewData.localstorage&&this.previewData.localstorage.length>0||this.previewData.session&&this.previewData.session.length>0||this.previewData.cookie&&this.previewData.cookie.length>0||this.previewData.request&&this.previewData.request.length>0:!1}},methods:{handleClose(){this.$emit("close")},getEnvConfigData(e){return this.previewData?this.previewData.envConfigs&&this.previewData.envConfigs[e]?[{label:"初始URL",value:this.previewData.envConfigs[e].url||"未配置"}]:[{label:"初始URL",value:this.previewData.url||"未配置"}]:[{label:"初始URL",value:"未配置"}]},getVariableTableData(e){if(!this.previewData)return[];if(this.previewData.variables&&Array.isArray(this.previewData.variables)){const l=[];return this.previewData.variables.forEach(a=>{if(a.data&&Array.isArray(a.data)){const t=a.data.find(s=>s.env===e);t&&l.push({name:a.variableName,value:t.value})}}),l}if(this.previewData.envConfigs&&this.previewData.envConfigs[e]&&this.previewData.envConfigs[e].variableValues){const l=this.previewData.envConfigs[e].variableValues;return Object.keys(l).map(a=>({name:a,value:l[a]}))}return[]},getMockTypeLabel(e){return{localstorage:"LocalStorage",session:"SessionStorage",cookie:"Cookie",request:"Request"}[e]||e}},watch:{visible(e){e&&(this.previewEnv="global")}}},i={};var p=r(v,o,n,!1,u,"2ab8157d",null,null);function u(e){for(let l in i)this[l]=i[l]}const h=function(){return p.exports}();export{h as C};
