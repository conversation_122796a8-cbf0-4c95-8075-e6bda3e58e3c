import{n as Da}from"./index-8c8571d2.js";var Pf=function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("t-drawer",{attrs:{visible:s.internalVisible,header:"脚本编辑器",size:"65%",closeBtn:!0,sizeDraggable:!0,footer:!0},on:{close:s.handleClose},scopedSlots:s._u([{key:"footer",fn:function(){return[e("div",{staticClass:"drawer-footer"},[e("t-button",{on:{click:s.editOriginalJson}},[s._v("修改原始JSON")]),e("t-button",{attrs:{theme:"primary"},on:{click:s.addStep}},[s._v("添加步骤")]),e("t-button",{attrs:{theme:"success"},on:{click:s.saveScript}},[s._v("保存脚本")])],1)]},proxy:!0}])},[e("div",{staticClass:"script-editor",class:{"with-json-panel":s.showJsonPanel}},[e("div",{staticClass:"script-info-section"},[e("div",{staticClass:"section-title"},[s._v("脚本基础信息")]),e("t-form",{staticClass:"script-info-form",attrs:{data:s.editableScriptInfo,"label-width":"100px"}},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:8}},[e("t-form-item",{attrs:{label:"脚本名称",required:""}},[e("t-input",{attrs:{placeholder:"请输入脚本名称",size:"small"},model:{value:s.editableScriptInfo.scriptName,callback:function(i){s.$set(s.editableScriptInfo,"scriptName",i)},expression:"editableScriptInfo.scriptName"}})],1)],1),e("t-col",{attrs:{span:8}},[e("t-form-item",{attrs:{label:"业务域"}},[e("t-select",{attrs:{placeholder:"请选择业务域",clearable:"",loading:s.appLoading,options:s.appOptions,size:"small"},model:{value:s.editableScriptInfo.app,callback:function(i){s.$set(s.editableScriptInfo,"app",i)},expression:"editableScriptInfo.app"}})],1)],1)],1),e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"脚本描述"}},[e("t-input",{attrs:{placeholder:"请输入脚本描述",size:"small"},model:{value:s.editableScriptInfo.description,callback:function(i){s.$set(s.editableScriptInfo,"description",i)},expression:"editableScriptInfo.description"}})],1)],1)],1),e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"备注"}},[e("t-textarea",{attrs:{placeholder:"请输入备注信息",autosize:{minRows:2,maxRows:4},size:"small"},model:{value:s.editableScriptInfo.remark,callback:function(i){s.$set(s.editableScriptInfo,"remark",i)},expression:"editableScriptInfo.remark"}})],1)],1)],1)],1)],1),e("div",{staticClass:"main-content"},[s.parseError?e("div",{staticClass:"json-editor-container"},[e("div",{staticClass:"error-message"},[e("t-alert",{attrs:{theme:"warning",title:"脚本内容解析失败"}},[s._v(' 脚本内容格式有误，请直接编辑JSON数据。修改完成后点击"保存"按钮。 ')])],1),e("div",{staticClass:"json-editor"},[e("t-textarea",{staticClass:"json-textarea",attrs:{placeholder:"请输入有效的JSON格式脚本内容",autosize:{minRows:20,maxRows:30}},model:{value:s.rawJsonContent,callback:function(i){s.rawJsonContent=i},expression:"rawJsonContent"}})],1),e("div",{staticClass:"json-editor-actions"},[e("t-button",{attrs:{theme:"primary"},on:{click:s.tryParseJson}},[s._v("重新解析")]),e("t-button",{on:{click:s.resetToEmpty}},[s._v("重置为空")])],1)]):e("div",{staticClass:"steps-container",class:{"half-width":s.showJsonPanel}},s._l(s.steps,function(i,n){return e("div",{key:n,staticClass:"step-item",class:{"step-selected":s.selectedStepIndex===n},on:{click:function(r){return s.selectStep(n)}}},[e("div",{staticClass:"step-header"},[e("span",{staticClass:"step-number"},[s._v("步骤 "+s._s(n+1))]),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(r){return r.stopPropagation(),s.removeStep(n)}}},[s._v(" 删除 ")])],1),e("t-form",{staticClass:"step-form",attrs:{data:i,"label-width":"120px"}},[e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"操作类型"}},[e("t-select",{attrs:{options:s.typeOptions,placeholder:"选择操作类型",size:"small"},on:{change:function(r){return s.onTypeChange(i,n)}},model:{value:i.type,callback:function(r){s.$set(i,"type",r)},expression:"step.type"}})],1)],1),e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"描述"}},[e("t-input",{attrs:{placeholder:"请输入操作描述",size:"small"},model:{value:i.desc,callback:function(r){s.$set(i,"desc",r)},expression:"step.desc"}})],1)],1),e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"文本"}},[e("t-input",{attrs:{placeholder:"请输入操作文本",size:"small"},model:{value:i.text,callback:function(r){s.$set(i,"text",r)},expression:"step.text"}})],1)],1),["wait","navigateTo","keypress","resize","resizeToFitDevice","choose","debug","assertDomain","datepicker","setWindowName","switchWindow","closeWindow","willAuthPassword","request","requestAndRedirect","switchCustomUserAgent","setting","quickSetting","captureURL","refresh","willAuth"].includes(i.type)?s._e():e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"值"}},[typeof i.value!="object"?e("t-input",{attrs:{placeholder:"请输入值",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}}):e("t-textarea",{attrs:{value:s.formatValue(i.value),placeholder:"请输入值(JSON格式)",autosize:{minRows:4,maxRows:6},size:"small"},on:{input:function(r){return s.updateValue(i,r)}}})],1)],1)],1)],1),["click","repeatedClick","rightClick","doubleClick","input","hover","drag","assertStyle","assertAttr","assert","upload","file"].includes(i.type)?e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"元素选择器"}},[e("t-input",{attrs:{size:"small",placeholder:"请输入元素选择器 (支持css://前缀或data-esign-inject-name)"},model:{value:i.element,callback:function(r){s.$set(i,"element",r)},expression:"step.element"}})],1)],1),i.type==="click"?e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"事件类型"}},[e("t-input",{attrs:{placeholder:"默认为click",size:"small"},model:{value:i.event,callback:function(r){s.$set(i,"event",r)},expression:"step.event"}})],1)],1):s._e(),["click","repeatedClick"].includes(i.type)?e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"选项"}},[e("t-input",{attrs:{placeholder:"点击选项(JSON格式)",size:"small"},model:{value:i.options,callback:function(r){s.$set(i,"options",r)},expression:"step.options"}})],1)],1):s._e()],1)],1):s._e(),["navigateTo","keypress","wait","refresh"].includes(i.type)?e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[i.type==="navigateTo"?e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"目标URL"}},[e("t-input",{attrs:{placeholder:"请输入要导航的URL",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1):s._e(),i.type==="keypress"?e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"按键"}},[e("t-input",{attrs:{placeholder:"请输入按键 (如: enter, space, ctrl+a)",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1):s._e(),i.type==="wait"?e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"等待时间(毫秒)"}},[e("t-input-number",{attrs:{placeholder:"等待时间",min:0,size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1):s._e()],1)],1):s._e(),["resize","resizeToFitDevice"].includes(i.type)?e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"宽度"}},[e("t-input-number",{attrs:{placeholder:"窗口宽度",size:"small"},model:{value:i.value.width,callback:function(r){s.$set(i.value,"width",r)},expression:"step.value.width"}})],1)],1),e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"高度"}},[e("t-input-number",{attrs:{placeholder:"窗口高度",size:"small"},model:{value:i.value.height,callback:function(r){s.$set(i.value,"height",r)},expression:"step.value.height"}})],1)],1)],1)],1):s._e(),i.type==="choose"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"选择值"}},[e("t-input",{attrs:{placeholder:"请输入选择的值",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),i.type==="debug"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"调试信息"}},[e("t-input",{attrs:{placeholder:"请输入调试信息",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),i.type==="assertDomain"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"期望域名"}},[e("t-input",{attrs:{placeholder:"请输入期望的域名",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),i.type==="datepicker"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"日期值"}},[e("t-input",{attrs:{placeholder:"请输入日期值 (如: 2024-01-01)",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),["setWindowName","switchWindow","closeWindow"].includes(i.type)?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:i.type==="setWindowName"?"窗口名称":i.type==="switchWindow"?"目标窗口":"窗口标识"}},[e("t-input",{attrs:{placeholder:i.type==="setWindowName"?"请输入窗口名称":i.type==="switchWindow"?"请输入要切换的窗口":"请输入要关闭的窗口标识",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),i.type==="willAuthPassword"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"密码"}},[e("t-input",{attrs:{type:"password",placeholder:"请输入认证密码",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),["request","requestAndRedirect"].includes(i.type)?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"请求URL"}},[e("t-input",{attrs:{placeholder:"请输入请求URL",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),i.type==="switchCustomUserAgent"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"用户代理"}},[e("t-input",{attrs:{placeholder:"请输入用户代理字符串",size:"small"},model:{value:i.value,callback:function(r){s.$set(i,"value",r)},expression:"step.value"}})],1)],1)],1):s._e(),["setting","quickSetting"].includes(i.type)?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"设置内容"}},[e("t-textarea",{attrs:{value:s.formatValue(i.value),placeholder:"请输入设置内容(JSON格式)",autosize:{minRows:3,maxRows:6},size:"small"},on:{input:function(r){return s.updateValue(i,r)}}})],1)],1)],1):s._e(),i.type==="captureURL"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"捕获配置"}},[e("t-textarea",{attrs:{value:s.formatValue(i.value),placeholder:"请输入URL捕获配置(JSON格式)",autosize:{minRows:2,maxRows:4},size:"small"},on:{input:function(r){return s.updateValue(i,r)}}})],1)],1)],1):s._e(),i.type==="assert"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:4}},[e("t-form-item",{attrs:{label:"版本"}},[e("t-input-number",{attrs:{placeholder:"版本",size:"small"},model:{value:i.ver,callback:function(r){s.$set(i,"ver",r)},expression:"step.ver"}})],1)],1),e("t-col",{attrs:{span:4}},[e("t-form-item",{attrs:{label:"是否相等"}},[e("t-switch",{attrs:{size:"small"},model:{value:i.isEql,callback:function(r){s.$set(i,"isEql",r)},expression:"step.isEql"}})],1)],1),e("t-col",{attrs:{span:4}},[e("t-form-item",{attrs:{label:"开始IF流程"}},[e("t-switch",{attrs:{size:"small"},model:{value:i.isStartIFFlow,callback:function(r){s.$set(i,"isStartIFFlow",r)},expression:"step.isStartIFFlow"}})],1)],1)],1):s._e(),["upload","file"].includes(i.type)?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"文件路径"}},[e("t-input",{attrs:{placeholder:"请输入文件路径",size:"small"},model:{value:i.filePath,callback:function(r){s.$set(i,"filePath",r)},expression:"step.filePath"}})],1)],1)],1):s._e(),i.type==="drag"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"拖拽类型"}},[e("t-select",{attrs:{options:[{label:"相对位置",value:"relative"},{label:"绝对位置",value:"absolute"}],size:"small"},model:{value:i.value.type,callback:function(r){s.$set(i.value,"type",r)},expression:"step.value.type"}})],1)],1),e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"X坐标"}},[e("t-input-number",{attrs:{placeholder:"X坐标",size:"small"},model:{value:i.value.x,callback:function(r){s.$set(i.value,"x",r)},expression:"step.value.x"}})],1)],1),e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"Y坐标"}},[e("t-input-number",{attrs:{placeholder:"Y坐标",size:"small"},model:{value:i.value.y,callback:function(r){s.$set(i.value,"y",r)},expression:"step.value.y"}})],1)],1)],1):s._e(),i.type==="compare"?e("t-row",{attrs:{gutter:16}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"比较选择器"}},[e("t-input",{attrs:{placeholder:"请输入比较选择器",size:"small"},model:{value:i.value.selector,callback:function(r){s.$set(i.value,"selector",r)},expression:"step.value.selector"}})],1)],1)],1):s._e(),e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"是否属于子流程"}},[e("t-switch",{attrs:{size:"small"},model:{value:i.isFlow,callback:function(r){s.$set(i,"isFlow",r)},expression:"step.isFlow"}})],1)],1),e("t-col",{attrs:{span:24}},[e("t-form-item",{attrs:{label:"忽略错误"}},[e("t-switch",{attrs:{size:"small"},model:{value:i.ignore,callback:function(r){s.$set(i,"ignore",r)},expression:"step.ignore"}})],1)],1)],1)],1),i.extra?e("div",{staticClass:"form-section"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:12}},[e("t-form-item",{attrs:{label:"额外信息"}},[e("t-textarea",{attrs:{placeholder:"请输入额外信息(JSON格式)",autosize:{minRows:3,maxRows:6},size:"small"},on:{blur:function(r){return s.updateExtra(i)}},model:{value:i.extraStr,callback:function(r){s.$set(i,"extraStr",r)},expression:"step.extraStr"}})],1)],1)],1)],1):s._e()],1)],1)}),0),s.showJsonPanel?e("div",{staticClass:"json-panel"},[e("div",{staticClass:"json-header"},[e("span",{staticClass:"json-title"},[s._v("原始JSON数据")]),e("t-button",{attrs:{variant:"text"},on:{click:function(i){s.showJsonPanel=!1}}},[e("t-icon",{attrs:{name:"close"}})],1)],1),e("div",{ref:"jsonContent",staticClass:"json-content"},s._l(s.steps,function(i,n){return e("div",{key:n,staticClass:"json-step",class:{highlighted:s.highlightedStepIndex===n},attrs:{id:`json-step-${n}`}},[e("div",{staticClass:"json-step-header"},[s._v("步骤 "+s._s(n+1))]),e("pre",{staticClass:"json-step-content"},[s._v(s._s(s.formatStepJson(i)))])])}),0)]):s._e()])])])},Rf=[];const Bf={name:"ScriptEditor",props:{visible:{type:Boolean,default:!1},scriptContent:{type:String,default:"[]"},scriptInfo:{type:Object,default:()=>({id:null,scriptName:"",description:"",app:"",remark:""})}},data(){return{internalVisible:!1,steps:[],appOptions:[],appLoading:!1,showJsonPanel:!1,highlightedStepIndex:null,parseError:!1,rawJsonContent:"",selectedStepIndex:null,editableScriptInfo:{id:null,scriptName:"",description:"",app:"",remark:""},typeOptions:[{label:"点击",value:"click"},{label:"按下",value:"pointerdown"},{label:"重复点击",value:"repeatedClick"},{label:"右键点击",value:"rightClick"},{label:"双击",value:"doubleClick"},{label:"输入",value:"input"},{label:"按键",value:"keypress"},{label:"刷新",value:"refresh"},{label:"调整窗口大小",value:"resize"},{label:"适配设备大小",value:"resizeToFitDevice"},{label:"选择",value:"choose"},{label:"调试",value:"debug"},{label:"悬停",value:"hover"},{label:"导航到",value:"navigateTo"},{label:"等待",value:"wait"},{label:"拖拽",value:"drag"},{label:"域名断言",value:"assertDomain"},{label:"样式断言",value:"assertStyle"},{label:"属性断言",value:"assertAttr"},{label:"断言",value:"assert"},{label:"上传文件",value:"upload"},{label:"文件",value:"file"},{label:"日期选择器",value:"datepicker"},{label:"设置",value:"setting"},{label:"快速设置",value:"quickSetting"},{label:"比较",value:"compare"},{label:"设置窗口名称",value:"setWindowName"},{label:"切换窗口",value:"switchWindow"},{label:"关闭窗口",value:"closeWindow"},{label:"将要认证",value:"willAuth"},{label:"密码认证",value:"willAuthPassword"},{label:"请求",value:"request"},{label:"请求并重定向",value:"requestAndRedirect"},{label:"切换用户代理",value:"switchCustomUserAgent"},{label:"捕获URL",value:"captureURL"}]}},computed:{formattedJsonData(){const s=this.steps.map(t=>{const{extraStr:e,...i}=t;return i});return JSON.stringify(s,null,2)}},watch:{visible:{handler(s){console.log("ScriptEditor - visible变化:",s),this.internalVisible=s,s&&(console.log("ScriptEditor - 组件显示，开始加载数据"),console.log("ScriptEditor - 接收到的scriptInfo:",this.scriptInfo),console.log("ScriptEditor - 接收到的scriptContent:",this.scriptContent),this.loadScript(),this.fetchAppOptions(),this.syncScriptInfo())},immediate:!0},scriptInfo:{handler(s){this.syncScriptInfo()},deep:!0,immediate:!0}},mounted(){this.fetchAppOptions()},methods:{syncScriptInfo(){console.log("ScriptEditor - syncScriptInfo被调用, scriptInfo:",this.scriptInfo),this.scriptInfo?(this.editableScriptInfo={id:this.scriptInfo.id||null,scriptName:this.scriptInfo.scriptName||"",description:this.scriptInfo.description||"",app:this.scriptInfo.app||"",remark:this.scriptInfo.remark||""},console.log("ScriptEditor - 设置editableScriptInfo:",this.editableScriptInfo)):console.log("ScriptEditor - scriptInfo为空")},loadScript(){try{const s=JSON.parse(this.scriptContent||"[]");this.steps=s.map(t=>({...t,extraStr:t.extra?JSON.stringify(t.extra,null,2):"{}"})),this.parseError=!1,this.rawJsonContent=""}catch(s){console.error("解析脚本内容失败:",s),this.parseError=!0,this.rawJsonContent=this.scriptContent||"[]",this.steps=[]}},formatValue(s){return typeof s=="object"&&s!==null?JSON.stringify(s,null,2):s},updateValue(s,t){try{s.value=JSON.parse(t)}catch{s.value=t}},addStep(){const s={type:"click",text:"点击",desc:"点击元素",value:"",element:"",event:"click",isFlow:!1,ignore:!1,extraStr:"{}",options:""};this.selectedStepIndex!==null?(this.steps.splice(this.selectedStepIndex+1,0,s),this.selectedStepIndex=this.selectedStepIndex+1):(this.steps.unshift(s),this.selectedStepIndex=0)},selectStep(s){this.selectedStepIndex===s?this.selectedStepIndex=null:this.selectedStepIndex=s,this.scrollToJsonStep(s)},removeStep(s){this.steps.splice(s,1),this.selectedStepIndex===s?this.selectedStepIndex=null:this.selectedStepIndex!==null&&this.selectedStepIndex>s&&(this.selectedStepIndex=this.selectedStepIndex-1)},onTypeChange(s,t){Object.assign(s,{click:{text:"点击",desc:"点击元素",element:"",event:"click",isFlow:!1,ignore:!1,extra:{},options:""},repeatedClick:{text:"重复点击",desc:"重复点击元素",element:"",value:1,options:"{ speed: 0.95 }",isFlow:!1,ignore:!1},rightClick:{text:"右键点击",desc:"右键点击元素",element:"",isFlow:!1,ignore:!1},doubleClick:{text:"双击",desc:"双击元素",element:"",isFlow:!1,ignore:!1},input:{text:"输入",desc:"输入文本",element:"",value:"",isFlow:!1,ignore:!1,extra:{}},keypress:{text:"按键",desc:"按键操作",value:"",isFlow:!1,ignore:!1},refresh:{text:"刷新",desc:"刷新页面",isFlow:!1},resize:{text:"调整窗口大小",desc:"调整窗口大小",value:{width:1920,height:1080},isFlow:!1},resizeToFitDevice:{text:"适配设备大小",desc:"适配设备大小",value:{width:1920,height:1080},isFlow:!1},choose:{text:"选择",desc:"选择操作",value:"",isFlow:!1,ignore:!1,extra:{}},debug:{text:"调试",desc:"调试信息",value:"",isFlow:!1,ignore:!1},hover:{text:"悬停",desc:"悬停在元素上",element:"",isFlow:!1,ignore:!1},navigateTo:{text:"导航到",desc:"导航到URL",value:"",isFlow:!1},wait:{text:"等待",desc:"等待时间",value:1e3,isFlow:!1,ignore:!1},drag:{text:"拖拽",desc:"拖拽元素",element:"",value:{type:"relative",x:0,y:0},isFlow:!1,ignore:!1},assertDomain:{text:"域名断言",desc:"域名断言",value:"",isFlow:!1},assertStyle:{text:"样式断言",desc:"样式断言",element:"",value:"",isFlow:!1},assertAttr:{text:"属性断言",desc:"属性断言",element:"",value:"",isFlow:!1},assert:{text:"ER-判断",desc:"断言",element:"",ver:1,value:"",isEql:!1,isStartIFFlow:!1,isFlow:!1},upload:{text:"上传文件",desc:"上传文件",element:"",filePath:"",isFlow:!1,ignore:!1},file:{text:"文件",desc:"文件操作",element:"",filePath:"",isFlow:!1,ignore:!1},datepicker:{text:"日期选择器",desc:"日期选择器",value:"",isFlow:!1,ignore:!1},setting:{text:"设置",desc:"设置操作",value:{key:"value"},isFlow:!1},quickSetting:{text:"快速设置",desc:"快速设置",value:{key:"value"},isFlow:!1},compare:{text:"比较",desc:"比较操作",value:{selector:""},isFlow:!1},setWindowName:{text:"当前窗口为",desc:"设置窗口名称",value:"",isFlow:!1},switchWindow:{text:"切换窗口",desc:"切换窗口",value:"",isFlow:!1},closeWindow:{text:"关闭窗口",desc:"关闭窗口",value:"",isFlow:!1},willAuth:{text:"将要认证",desc:"将要认证",isFlow:!1},willAuthPassword:{text:"密码认证",desc:"密码认证",value:"",isFlow:!1},request:{text:"请求",desc:"发送请求",value:"",isFlow:!1},requestAndRedirect:{text:"请求并重定向",desc:"请求并重定向",value:"",isFlow:!1},switchCustomUserAgent:{text:"切换用户代理",desc:"切换用户代理",value:"",isFlow:!1},captureURL:{text:"捕获URL",desc:"捕获URL",value:{pattern:"",variable:""},isFlow:!1}}[s.type]||{})},updateExtra(s){try{s.extra=JSON.parse(s.extraStr||"{}")}catch(t){console.error("解析额外信息失败:",t),s.extra={}}},async fetchAppOptions(){this.appLoading=!0;try{const s=await this.$request.get("https://m.esign.cn/infocenter-manager/forward/bizDomain/list");s.data&&Array.isArray(s.data.data)&&s.data.code==0?this.appOptions=s.data.data.map(t=>({label:t.name,value:t.name})):this.appOptions=[]}catch{this.appOptions=[]}finally{this.appLoading=!1}},validateScriptInfo(){return this.editableScriptInfo.scriptName.trim()?!0:(this.$message.error("请输入脚本名称"),!1)},saveScript(){var e;if(!this.validateScriptInfo())return;let s;if(this.parseError)try{JSON.parse(this.rawJsonContent),s=this.rawJsonContent}catch{this.$message.error("JSON格式有误，请检查后再保存");return}else{const i=this.steps.map(n=>{const{extraStr:r,...o}=n;return o.value===""&&(o.value=""),o});s=JSON.stringify(i)}const t={...this.editableScriptInfo,content:s,editor:((e=this.$store.state.user.userInfo)==null?void 0:e.alias)||""};this.$emit("save",t),this.handleClose()},formatStepJson(s){const{extraStr:t,...e}=s;return JSON.stringify(e,null,2)},scrollToJsonStep(s){this.showJsonPanel?this.performScroll(s):(this.showJsonPanel=!0,this.$nextTick(()=>{this.performScroll(s)}))},performScroll(s){this.highlightedStepIndex=null,this.$nextTick(()=>{const t=document.getElementById(`json-step-${s}`),e=this.$refs.jsonContent;if(t&&e){const i=t.offsetTop-e.offsetTop-20;e.scrollTo({top:i,behavior:"smooth"}),this.highlightedStepIndex=s,setTimeout(()=>{this.highlightedStepIndex=null},3e3)}})},tryParseJson(){try{const s=JSON.parse(this.rawJsonContent);this.steps=s.map(t=>({...t,extraStr:t.extra?JSON.stringify(t.extra,null,2):"{}"})),this.parseError=!1,this.rawJsonContent="",this.$message.success("JSON解析成功！")}catch(s){this.$message.error("JSON格式仍然有误，请检查语法"),console.error("JSON解析失败:",s)}},resetToEmpty(){this.rawJsonContent="[]",this.steps=[],this.parseError=!1,this.$message.success("已重置为空脚本")},handleClose(){this.$emit("update:visible",!1)},editOriginalJson(){this.handleClose(),this.$emit("edit-json")}}},To={};var Ef=Da(Bf,Pf,Rf,!1,Lf,"15542fa2",null,null);function Lf(s){for(let t in To)this[t]=To[t]}const iy=function(){return Ef.exports}();let Jn=[],Ta=[];(()=>{let s="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let t=0,e=0;t<s.length;t++)(t%2?Ta:Jn).push(e=e+s[t])})();function If(s){if(s<768)return!1;for(let t=0,e=Jn.length;;){let i=t+e>>1;if(s<Jn[i])e=i;else if(s>=Ta[i])t=i+1;else return!0;if(t==e)return!1}}function Po(s){return s>=127462&&s<=127487}const Ro=8205;function Nf(s,t,e=!0,i=!0){return(e?Pa:Ff)(s,t,i)}function Pa(s,t,e){if(t==s.length)return t;t&&Ra(s.charCodeAt(t))&&Ba(s.charCodeAt(t-1))&&t--;let i=vn(s,t);for(t+=Bo(i);t<s.length;){let n=vn(s,t);if(i==Ro||n==Ro||e&&If(n))t+=Bo(n),i=n;else if(Po(n)){let r=0,o=t-2;for(;o>=0&&Po(vn(s,o));)r++,o-=2;if(r%2==0)break;t+=2}else break}return t}function Ff(s,t,e){for(;t>0;){let i=Pa(s,t-2,e);if(i<t)return i;t--}return 0}function vn(s,t){let e=s.charCodeAt(t);if(!Ba(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return Ra(i)?(e-55296<<10)+(i-56320)+65536:e}function Ra(s){return s>=56320&&s<57344}function Ba(s){return s>=55296&&s<56320}function Bo(s){return s<65536?1:2}class z{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=Xe(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),Jt.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=Xe(this,t,e);let i=[];return this.decompose(t,e,i,0),Jt.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new xi(this),r=new xi(t);for(let o=e,l=e;;){if(n.next(o),r.next(o),o=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(l+=n.value.length,n.done||l>=i)return!0}}iter(t=1){return new xi(this,t)}iterRange(t,e=this.length){return new Ea(this,t,e)}iterLines(t,e){let i;if(t==null)i=this.iter();else{e==null&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new La(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(t.length==0)throw new RangeError("A document must have at least one line");return t.length==1&&!t[0]?z.empty:t.length<=32?new Y(t):Jt.from(Y.split(t,[]))}}class Y extends z{constructor(t,e=zf(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.text[r],l=n+o.length;if((e?i:l)>=t)return new Vf(n,l,i,o);n=l+1,i++}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new Y(Eo(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(n&1){let o=i.pop(),l=xs(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new Y(l,o.length+r.length));else{let a=l.length>>1;i.push(new Y(l.slice(0,a)),new Y(l.slice(a)))}}else i.push(r)}replace(t,e,i){if(!(i instanceof Y))return super.replace(t,e,i);[t,e]=Xe(this,t,e);let n=xs(this.text,xs(i.text,Eo(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new Y(n,r):Jt.from(Y.split(n,[]),r)}sliceString(t,e=this.length,i=`
`){[t,e]=Xe(this,t,e);let n="";for(let r=0,o=0;r<=e&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>t&&o&&(n+=i),t<a&&e>r&&(n+=l.slice(Math.max(0,t-r),e-r)),r=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,i.length==32&&(e.push(new Y(i,n)),i=[],n=-1);return n>-1&&e.push(new Y(i,n)),e}}class Jt extends z{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let i of t)this.lines+=i.lines}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.children[r],l=n+o.length,a=i+o.lines-1;if((e?a:l)>=t)return o.lineInner(t,e,i,n);n=l+1,i=a+1}}decompose(t,e,i,n){for(let r=0,o=0;o<=e&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(t<=a&&e>=o){let h=n&((o<=t?1:0)|(a>=e?2:0));o>=t&&a<=e&&!h?i.push(l):l.decompose(t-o,e-o,i,h)}o=a+1}}replace(t,e,i){if([t,e]=Xe(this,t,e),i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],l=r+o.length;if(t>=r&&e<=l){let a=o.replace(t-r,e-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>5-1&&a.lines>h>>5+1){let c=this.children.slice();return c[n]=a,new Jt(c,this.length-(e-t)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i=`
`){[t,e]=Xe(this,t,e);let n="";for(let r=0,o=0;r<this.children.length&&o<=e;r++){let l=this.children[r],a=o+l.length;o>t&&r&&(n+=i),t<a&&e>o&&(n+=l.sliceString(t-o,e-o,i)),o=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof Jt))return 0;let i=0,[n,r,o,l]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==o||r==l)return i;let a=this.children[n],h=t.children[r];if(a!=h)return i+a.scanIdentical(h,e);i+=a.length+1}}static from(t,e=t.reduce((i,n)=>i+n.length+1,-1)){let i=0;for(let d of t)i+=d.lines;if(i<32){let d=[];for(let p of t)p.flatten(d);return new Y(d,e)}let n=Math.max(32,i>>5),r=n<<1,o=n>>1,l=[],a=0,h=-1,c=[];function f(d){let p;if(d.lines>r&&d instanceof Jt)for(let m of d.children)f(m);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof Y&&a&&(p=c[c.length-1])instanceof Y&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new Y(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>n&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:Jt.from(c,h)),h=-1,a=c.length=0)}for(let d of t)f(d);return u(),l.length==1?l[0]:new Jt(l,e)}}z.empty=new Y([""],0);function zf(s){let t=-1;for(let e of s)t+=e.length+1;return t}function xs(s,t,e=0,i=1e9){for(let n=0,r=0,o=!0;r<s.length&&n<=i;r++){let l=s[r],a=n+l.length;a>=e&&(a>i&&(l=l.slice(0,i-n)),n<e&&(l=l.slice(e-n)),o?(t[t.length-1]+=l,o=!1):t.push(l)),n=a+1}return t}function Eo(s,t,e){return xs(s,[""],t,e)}class xi{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof Y?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],o=r>>1,l=n instanceof Y?n.text.length:n.children.length;if(o==(e>0?l:0)){if(i==0)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(e>0?0:1)){if(this.offsets[i]+=e,t==0)return this.lineBreak=!0,this.value=`
`,this;t--}else if(n instanceof Y){let a=n.text[o+(e<0?-1:0)];if(this.offsets[i]+=e,a.length>Math.max(0,t))return this.value=t==0?a:e>0?a.slice(t):a.slice(0,a.length-t),this;t-=a.length}else{let a=n.children[o+(e<0?-1:0)];t>a.length?(t-=a.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(e>0?1:(a instanceof Y?a.text.length:a.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class Ea{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new xi(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class La{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(z.prototype[Symbol.iterator]=function(){return this.iter()},xi.prototype[Symbol.iterator]=Ea.prototype[Symbol.iterator]=La.prototype[Symbol.iterator]=function(){return this});class Vf{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function Xe(s,t,e){return t=Math.max(0,Math.min(s.length,t)),[t,Math.max(t,Math.min(s.length,e))]}function at(s,t,e=!0,i=!0){return Nf(s,t,e,i)}function Hf(s){return s>=56320&&s<57344}function Wf(s){return s>=55296&&s<56320}function bt(s,t){let e=s.charCodeAt(t);if(!Wf(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return Hf(i)?(e-55296<<10)+(i-56320)+65536:e}function _r(s){return s<=65535?String.fromCharCode(s):(s-=65536,String.fromCharCode((s>>10)+55296,(s&1023)+56320))}function Gt(s){return s<65536?1:2}const Gn=/\r\n?|\n/;var pt=function(s){return s[s.Simple=0]="Simple",s[s.TrackDel=1]="TrackDel",s[s.TrackBefore=2]="TrackBefore",s[s.TrackAfter=3]="TrackAfter",s}(pt||(pt={}));class Zt{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],o=this.sections[e++];o<0?(t(i,n,r),n+=r):n+=o,i+=r}}iterChangedRanges(t,e=!1){Yn(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new Zt(t)}composeDesc(t){return this.empty?t:t.empty?this:Ia(this,t)}mapDesc(t,e=!1){return t.empty?this:Qn(this,t,e)}mapPos(t,e=-1,i=pt.Simple){let n=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=n+l;if(a<0){if(h>t)return r+(t-n);r+=l}else{if(i!=pt.Simple&&h>=t&&(i==pt.TrackDel&&n<t&&h>t||i==pt.TrackBefore&&n<t||i==pt.TrackAfter&&h>t))return null;if(h>t||h==t&&e<0&&!l)return t==n||e<0?r:r+a;r+=a}n=h}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=this.sections[i++],o=this.sections[i++],l=n+r;if(o>=0&&n<=e&&l>=t)return n<t&&l>e?"cover":!0;n=l}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(e=>typeof e!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new Zt(t)}static create(t){return new Zt(t)}}class tt extends Zt{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return Yn(this,(e,i,n,r,o)=>t=t.replace(n,n+(i-e),o),!1),t}mapDesc(t,e=!1){return Qn(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let o=e[n],l=e[n+1];if(l>=0){e[n]=l,e[n+1]=o;let a=n>>1;for(;i.length<a;)i.push(z.empty);i.push(o?t.slice(r,r+o):z.empty)}r+=o}return new tt(e,i)}compose(t){return this.empty?t:t.empty?this:Ia(this,t,!0)}map(t,e=!1){return t.empty?this:Qn(this,t,e,!0)}iterChanges(t,e=!1){Yn(this,t,e)}get desc(){return Zt.create(this.sections)}filter(t){let e=[],i=[],n=[],r=new Ai(this);t:for(let o=0,l=0;;){let a=o==t.length?1e9:t[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break t;let c=Math.min(r.len,a-l);ht(n,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;ht(e,c,f),f>0&&de(i,e,r.text),r.forward(c),l+=c}let h=t[o++];for(;l<h;){if(r.done)break t;let c=Math.min(r.len,h-l);ht(e,c,-1),ht(n,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new tt(e,i),filtered:Zt.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):n==0?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!n.length)return;o<e&&ht(n,e-o,-1);let f=new tt(n,r);l=l?l.compose(f.map(l)):f,n=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof tt){if(c.length!=e)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${e})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>e)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${e})`);let p=d?typeof d=="string"?z.of(d.split(i||Gn)):d:z.empty,m=p.length;if(f==u&&m==0)return;f<o&&a(),f>o&&ht(n,f-o,-1),ht(n,u-f,m),de(r,n,p),o=u}}return h(t),a(!l),l}static empty(t){return new tt(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if(typeof r=="number")e.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)e.push(r[0],0);else{for(;i.length<n;)i.push(z.empty);i[n]=z.of(r.slice(1)),e.push(r[0],i[n].length)}}}return new tt(e,i)}static createSet(t,e){return new tt(t,e)}}function ht(s,t,e,i=!1){if(t==0&&e<=0)return;let n=s.length-2;n>=0&&e<=0&&e==s[n+1]?s[n]+=t:n>=0&&t==0&&s[n]==0?s[n+1]+=e:i?(s[n]+=t,s[n+1]+=e):s.push(t,e)}function de(s,t,e){if(e.length==0)return;let i=t.length-2>>1;if(i<s.length)s[s.length-1]=s[s.length-1].append(e);else{for(;s.length<i;)s.push(z.empty);s.push(e)}}function Yn(s,t,e){let i=s.inserted;for(let n=0,r=0,o=0;o<s.sections.length;){let l=s.sections[o++],a=s.sections[o++];if(a<0)n+=l,r+=l;else{let h=n,c=r,f=z.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(e||o==s.sections.length||s.sections[o+1]<0);)l=s.sections[o++],a=s.sections[o++];t(n,h,r,c,f),n=h,r=c}}}function Qn(s,t,e,i=!1){let n=[],r=i?[]:null,o=new Ai(s),l=new Ai(t);for(let a=-1;;){if(o.done&&l.len||l.done&&o.len)throw new Error("Mismatched change set lengths");if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);ht(n,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!e))){let h=l.len;for(ht(n,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(ht(n,0,o.ins),r&&de(r,n,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;ht(n,h,a<o.i?o.ins:0),r&&a<o.i&&de(r,n,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?tt.createSet(n,r):Zt.create(n);throw new Error("Mismatched change set lengths")}}}function Ia(s,t,e=!1){let i=[],n=e?[]:null,r=new Ai(s),o=new Ai(t);for(let l=!1;;){if(r.done&&o.done)return n?tt.createSet(i,n):Zt.create(i);if(r.ins==0)ht(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)ht(i,0,o.ins,l),n&&de(n,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;ht(i,a,c,l),n&&c&&de(n,i,o.text)}else o.ins==-1?(ht(i,r.off?0:r.len,a,l),n&&de(n,i,r.textBit(a))):(ht(i,r.off?0:r.len,o.off?0:o.ins,l),n&&!o.off&&de(n,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class Ai{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?z.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?z.empty:e[i].slice(this.off,t==null?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){this.ins==-1?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class Te{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let t=this.flags&7;return t==7?null:t}get goalColumn(){let t=this.flags>>6;return t==16777215?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new Te(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return x.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return x.range(this.anchor,i)}eq(t,e=!1){return this.anchor==t.anchor&&this.head==t.head&&(!e||!this.empty||this.assoc==t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return x.range(t.anchor,t.head)}static create(t,e,i){return new Te(t,e,i)}}class x{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:x.create(this.ranges.map(i=>i.map(t,e)),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new x([this.main],0)}addRange(t,e=!0){return x.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,x.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||typeof t.main!="number"||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new x(t.ranges.map(e=>Te.fromJSON(e)),t.main)}static single(t,e=t){return new x([x.range(t,e)],0)}static create(t,e=0){if(t.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return x.normalized(t.slice(),e);i=r.to}return new x(t,e)}static cursor(t,e=0,i,n){return Te.create(t,t,(e==0?0:e<0?8:16)|(i==null?7:Math.min(6,i))|(n??16777215)<<6)}static range(t,e,i,n){let r=(i??16777215)<<6|(n==null?7:Math.min(6,n));return e<t?Te.create(e,t,48|r):Te.create(t,e,(e>t?8:0)|r)}static normalized(t,e=0){let i=t[e];t.sort((n,r)=>n.from-r.from),e=t.indexOf(i);for(let n=1;n<t.length;n++){let r=t[n],o=t[n-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);n<=e&&e--,t.splice(--n,2,r.anchor>r.head?x.range(a,l):x.range(l,a))}}return new x(t,e)}}function Na(s,t){for(let e of s.ranges)if(e.to>t)throw new RangeError("Selection points outside of document")}let Kr=0;class D{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Kr++,this.default=t([]),this.extensions=typeof r=="function"?r(this):r}get reader(){return this}static define(t={}){return new D(t.combine||(e=>e),t.compareInput||((e,i)=>e===i),t.compare||(t.combine?(e,i)=>e===i:Ur),!!t.static,t.enables)}of(t){return new ws([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new ws(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new ws(t,this,2,e)}from(t,e){return e||(e=i=>i),this.compute([t],i=>e(i.field(t)))}}function Ur(s,t){return s==t||s.length==t.length&&s.every((e,i)=>e===t[i])}class ws{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Kr++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,o=t[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((e=t[f.id])!==null&&e!==void 0?e:1)&1||c.push(t[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||Xn(f,c)){let d=i(f);if(l?!Lo(d,f.values[o],n):!n(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d,p=u.config.address[r];if(p!=null){let m=Es(u,p);if(this.dependencies.every(g=>g instanceof D?u.facet(g)===f.facet(g):g instanceof it?u.field(g,!1)==f.field(g,!1):!0)||(l?Lo(d=i(f),m,n):n(d=i(f),m)))return f.values[o]=m,0}else d=i(f);return f.values[o]=d,1}}}}function Lo(s,t,e){if(s.length!=t.length)return!1;for(let i=0;i<s.length;i++)if(!e(s[i],t[i]))return!1;return!0}function Xn(s,t){let e=!1;for(let i of t)wi(s,i)&1&&(e=!0);return e}function $f(s,t,e){let i=e.map(a=>s[a.id]),n=e.map(a=>a.type),r=i.filter(a=>!(a&1)),o=s[t.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=Es(a,i[c]);if(n[c]==2)for(let u of f)h.push(u);else h.push(f)}return t.combine(h)}return{create(a){for(let h of i)wi(a,h);return a.values[o]=l(a),1},update(a,h){if(!Xn(a,r))return 0;let c=l(a);return t.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=Xn(a,i),f=h.config.facets[t.id],u=h.facet(t);if(f&&!c&&Ur(e,f))return a.values[o]=u,0;let d=l(a);return t.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const Gi=D.define({static:!0});class it{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(t){let e=new it(Kr++,t.create,t.update,t.compare||((i,n)=>i===n),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(Gi).find(i=>i.field==this);return((e==null?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:i=>(i.values[e]=this.create(i),1),update:(i,n)=>{let r=i.values[e],o=this.updateF(r,n);return this.compareF(r,o)?0:(i.values[e]=o,1)},reconfigure:(i,n)=>{let r=i.facet(Gi),o=n.facet(Gi),l;return(l=r.find(a=>a.field==this))&&l!=o.find(a=>a.field==this)?(i.values[e]=l.create(i),1):n.config.address[this.id]!=null?(i.values[e]=n.field(this),0):(i.values[e]=this.create(i),1)}}}init(t){return[this,Gi.of({field:this,create:t})]}get extension(){return this}}const Oe={lowest:4,low:3,default:2,high:1,highest:0};function ai(s){return t=>new Fa(t,s)}const ze={highest:ai(Oe.highest),high:ai(Oe.high),default:ai(Oe.default),low:ai(Oe.low),lowest:ai(Oe.lowest)};class Fa{constructor(t,e){this.inner=t,this.prec=e}}class on{of(t){return new Zn(this,t)}reconfigure(t){return on.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class Zn{constructor(t,e){this.compartment=t,this.inner=e}}class Bs{constructor(t,e,i,n,r,o){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return e==null?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),o=new Map;for(let u of qf(t,e,o))u instanceof it?n.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of n)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i==null?void 0:i.config.facets;for(let u in r){let d=r[u],p=d[0].facet,m=c&&c[u]||[];if(d.every(g=>g.type==0))if(l[p.id]=a.length<<1|1,Ur(m,d))a.push(i.facet(p));else{let g=p.combine(d.map(b=>b.value));a.push(i&&p.compare(g,i.facet(p))?i.facet(p):g)}else{for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(b=>g.dynamicSlot(b)));l[p.id]=h.length<<1,h.push(g=>$f(g,p,d))}}let f=h.map(u=>u(l));return new Bs(t,o,f,l,a,r)}}function qf(s,t,e){let i=[[],[],[],[],[]],n=new Map;function r(o,l){let a=n.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof Zn&&e.delete(o.compartment)}if(n.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof Zn){if(e.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=t.get(o.compartment)||o.inner;e.set(o.compartment,h),r(h,l)}else if(o instanceof Fa)r(o.inner,o.prec);else if(o instanceof it)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof ws)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Oe.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(s,Oe.default),i.reduce((o,l)=>o.concat(l))}function wi(s,t){if(t&1)return 2;let e=t>>1,i=s.status[e];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;s.status[e]=4;let n=s.computeSlot(s,s.config.dynamicSlots[e]);return s.status[e]=2|n}function Es(s,t){return t&1?s.config.staticValues[t>>1]:s.values[t>>1]}const za=D.define(),tr=D.define({combine:s=>s.some(t=>t),static:!0}),Va=D.define({combine:s=>s.length?s[0]:void 0,static:!0}),Ha=D.define(),Wa=D.define(),$a=D.define(),qa=D.define({combine:s=>s.length?s[0]:!1});class he{constructor(t,e){this.type=t,this.value=e}static define(){return new jf}}class jf{of(t){return new he(this,t)}}class _f{constructor(t){this.map=t}of(t){return new B(this,t)}}class B{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return e===void 0?void 0:e==this.value?this:new B(this.type,e)}is(t){return this.type==t}static define(t={}){return new _f(t.map||(e=>e))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let r=n.map(e);r&&i.push(r)}return i}}B.reconfigure=B.define();B.appendConfig=B.define();class et{constructor(t,e,i,n,r,o){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&Na(i,e.newLength),r.some(l=>l.type==et.time)||(this.annotations=r.concat(et.time.of(Date.now())))}static create(t,e,i,n,r,o){return new et(t,e,i,n,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(et.userEvent);return!!(e&&(e==t||e.length>t.length&&e.slice(0,t.length)==t&&e[t.length]=="."))}}et.time=he.define();et.userEvent=he.define();et.addToHistory=he.define();et.remote=he.define();function Kf(s,t){let e=[];for(let i=0,n=0;;){let r,o;if(i<s.length&&(n==t.length||t[n]>=s[i]))r=s[i++],o=s[i++];else if(n<t.length)r=t[n++],o=t[n++];else return e;!e.length||e[e.length-1]<r?e.push(r,o):e[e.length-1]<o&&(e[e.length-1]=o)}}function ja(s,t,e){var i;let n,r,o;return e?(n=t.changes,r=tt.empty(t.changes.length),o=s.changes.compose(t.changes)):(n=t.changes.map(s.changes),r=s.changes.mapDesc(t.changes,!0),o=s.changes.compose(n)),{changes:o,selection:t.selection?t.selection.map(r):(i=s.selection)===null||i===void 0?void 0:i.map(n),effects:B.mapEffects(s.effects,n).concat(B.mapEffects(t.effects,r)),annotations:s.annotations.length?s.annotations.concat(t.annotations):t.annotations,scrollIntoView:s.scrollIntoView||t.scrollIntoView}}function er(s,t,e){let i=t.selection,n=Ke(t.annotations);return t.userEvent&&(n=n.concat(et.userEvent.of(t.userEvent))),{changes:t.changes instanceof tt?t.changes:tt.of(t.changes||[],e,s.facet(Va)),selection:i&&(i instanceof x?i:x.single(i.anchor,i.head)),effects:Ke(t.effects),annotations:n,scrollIntoView:!!t.scrollIntoView}}function _a(s,t,e){let i=er(s,t.length?t[0]:{},s.doc.length);t.length&&t[0].filter===!1&&(e=!1);for(let r=1;r<t.length;r++){t[r].filter===!1&&(e=!1);let o=!!t[r].sequential;i=ja(i,er(s,t[r],o?i.changes.newLength:s.doc.length),o)}let n=et.create(s,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return Jf(e?Uf(n):n)}function Uf(s){let t=s.startState,e=!0;for(let n of t.facet(Ha)){let r=n(s);if(r===!1){e=!1;break}Array.isArray(r)&&(e=e===!0?r:Kf(e,r))}if(e!==!0){let n,r;if(e===!1)r=s.changes.invertedDesc,n=tt.empty(t.doc.length);else{let o=s.changes.filter(e);n=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}s=et.create(t,n,s.selection&&s.selection.map(r),B.mapEffects(s.effects,r),s.annotations,s.scrollIntoView)}let i=t.facet(Wa);for(let n=i.length-1;n>=0;n--){let r=i[n](s);r instanceof et?s=r:Array.isArray(r)&&r.length==1&&r[0]instanceof et?s=r[0]:s=_a(t,Ke(r),!1)}return s}function Jf(s){let t=s.startState,e=t.facet($a),i=s;for(let n=e.length-1;n>=0;n--){let r=e[n](s);r&&Object.keys(r).length&&(i=ja(i,er(t,r,s.changes.newLength),!0))}return i==s?s:et.create(t,s.changes,s.selection,i.effects,i.annotations,i.scrollIntoView)}const Gf=[];function Ke(s){return s==null?Gf:Array.isArray(s)?s:[s]}var G=function(s){return s[s.Word=0]="Word",s[s.Space=1]="Space",s[s.Other=2]="Other",s}(G||(G={}));const Yf=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let ir;try{ir=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function Qf(s){if(ir)return ir.test(s);for(let t=0;t<s.length;t++){let e=s[t];if(/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Yf.test(e)))return!0}return!1}function Xf(s){return t=>{if(!/\S/.test(t))return G.Space;if(Qf(t))return G.Word;for(let e=0;e<s.length;e++)if(t.indexOf(s[e])>-1)return G.Word;return G.Other}}class V{constructor(t,e,i,n,r,o){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)wi(this,l<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(i==null){if(e)throw new RangeError("Field is not present in this state");return}return wi(this,i),Es(this,i)}update(...t){return _a(this,t,!0)}applyTransaction(t){let e=this.config,{base:i,compartments:n}=e;for(let l of t.effects)l.is(on.reconfigure)?(e&&(n=new Map,e.compartments.forEach((a,h)=>n.set(h,a)),e=null),n.set(l.value.compartment,l.value.extension)):l.is(B.reconfigure)?(e=null,i=l.value):l.is(B.appendConfig)&&(e=null,i=Ke(i).concat(l.value));let r;e?r=t.startState.values.slice():(e=Bs.resolve(i,n,this),r=new V(e,this.doc,this.selection,e.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=t.startState.facet(tr)?t.newSelection:t.newSelection.asSingle();new V(e,t.newDoc,o,r,(l,a)=>a.update(l,t),t)}replaceSelection(t){return typeof t=="string"&&(t=this.toText(t)),this.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:t},range:x.cursor(e.from+t.length)}))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],o=Ke(i.effects);for(let l=1;l<e.ranges.length;l++){let a=t(e.ranges[l]),h=this.changes(a.changes),c=h.map(n);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=n.mapDesc(h,!0);r.push(a.range.map(f)),n=n.compose(c),o=B.mapEffects(o,c).concat(B.mapEffects(Ke(a.effects),f))}return{changes:n,selection:x.create(r,e.mainIndex),effects:o}}changes(t=[]){return t instanceof tt?t:tt.of(t,this.doc.length,this.facet(V.lineSeparator))}toText(t){return z.of(t.split(this.facet(V.lineSeparator)||Gn))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return e==null?t.default:(wi(this,e),Es(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof it&&this.config.address[n.id]!=null&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||typeof t.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(t,r)){let o=i[r],l=t[r];n.push(o.init(a=>o.spec.fromJSON(l,a)))}}return V.create({doc:t.doc,selection:x.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=Bs.resolve(t.extensions||[],new Map),i=t.doc instanceof z?t.doc:z.of((t.doc||"").split(e.staticFacet(V.lineSeparator)||Gn)),n=t.selection?t.selection instanceof x?t.selection:x.single(t.selection.anchor,t.selection.head):x.single(0);return Na(n,i.length),e.staticFacet(tr)||(n=n.asSingle()),new V(e,i,n,e.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(V.tabSize)}get lineBreak(){return this.facet(V.lineSeparator)||`
`}get readOnly(){return this.facet(qa)}phrase(t,...e){for(let i of this.facet(V.phrases))if(Object.prototype.hasOwnProperty.call(i,t)){t=i[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,(i,n)=>{if(n=="$")return"$";let r=+(n||1);return!r||r>e.length?i:e[r-1]})),t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(za))for(let o of r(this,e,i))Object.prototype.hasOwnProperty.call(o,t)&&n.push(o[t]);return n}charCategorizer(t){return Xf(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),o=t-i,l=t-i;for(;o>0;){let a=at(e,o,!1);if(r(e.slice(a,o))!=G.Word)break;o=a}for(;l<n;){let a=at(e,l);if(r(e.slice(l,a))!=G.Word)break;l=a}return o==l?null:x.range(o+i,l+i)}}V.allowMultipleSelections=tr;V.tabSize=D.define({combine:s=>s.length?s[0]:4});V.lineSeparator=Va;V.readOnly=qa;V.phrases=D.define({compare(s,t){let e=Object.keys(s),i=Object.keys(t);return e.length==i.length&&e.every(n=>s[n]==t[n])}});V.languageData=za;V.changeFilter=Ha;V.transactionFilter=Wa;V.transactionExtender=$a;on.reconfigure=B.define();function Ht(s,t,e={}){let i={};for(let n of s)for(let r of Object.keys(n)){let o=n[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(e,r))i[r]=e[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let n in t)i[n]===void 0&&(i[n]=t[n]);return i}class Ee{eq(t){return this==t}range(t,e=t){return sr.create(t,e,this)}}Ee.prototype.startSide=Ee.prototype.endSide=0;Ee.prototype.point=!1;Ee.prototype.mapMode=pt.TrackDel;let sr=class Ka{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new Ka(t,e,i)}};function nr(s,t){return s.from-t.from||s.value.startSide-t.value.startSide}class Jr{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let o=n,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-t||(i?this.value[a].endSide:this.value[a].startSide)-e;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(n(this.from[r]+t,this.to[r]+t,this.value[r])===!1)return!1}map(t,e){let i=[],n=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+t,f=this.to[a]+t,u,d;if(c==f){let p=e.mapPos(c,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=e.mapPos(c,h.endSide),d<u)))continue}else if(u=e.mapPos(c,h.startSide),d=e.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),n.push(u-o),r.push(d-o))}return{mapped:i.length?new Jr(n,r,i,l):null,pos:o}}}class F{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new F(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,o=t.filter;if(e.length==0&&!o)return this;if(i&&(e=e.slice().sort(nr)),this.isEmpty)return e.length?F.of(e):this;let l=new Ua(this,null,-1).goto(0),a=0,h=[],c=new le;for(;l.value||a<e.length;)if(a<e.length&&(l.from-e[a].from||l.startSide-e[a].value.startSide)>=0){let f=e[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(l.chunkIndex)<e[a].from)&&(!o||n>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||n>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(sr.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?F.empty:this.nextLayer.update({add:h,filter:o,filterFrom:n,filterTo:r}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=t.touchesRange(l,l+a.length);if(h===!1)n=Math.max(n,a.maxPoint),e.push(a),i.push(t.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,t);c&&(n=Math.max(n,c.maxPoint),e.push(c),i.push(f))}}let r=this.nextLayer.map(t);return e.length==0?r:new F(i,e,r||F.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],o=this.chunk[n];if(e>=r&&t<=r+o.length&&o.between(r,t-r,e-r,i)===!1)return}this.nextLayer.between(t,e,i)}}iter(t=0){return Mi.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Mi.from(t).goto(e)}static compare(t,e,i,n,r=-1){let o=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=Io(o,l,i),h=new hi(o,a,r),c=new hi(l,a,r);i.iterGaps((f,u,d)=>No(h,f,c,u,d,n)),i.empty&&i.length==0&&No(h,0,c,0,0,n)}static eq(t,e,i=0,n){n==null&&(n=1e9-1);let r=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0),o=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=Io(r,o),a=new hi(r,l,0).goto(i),h=new hi(o,l,0).goto(i);for(;;){if(a.to!=h.to||!rr(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>n)return!0;a.next(),h.next()}}static spans(t,e,i,n,r=-1){let o=new hi(t,null,r).goto(e),l=e,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let c=o.activeForPoint(o.to),f=o.pointFrom<e?c.length+1:o.point.startSide<0?c.length:Math.min(c.length,a);n.point(l,h,o.point,c,f,o.pointRank),a=Math.min(o.openEnd(h),c.length)}else h>l&&(n.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next()}}static of(t,e=!1){let i=new le;for(let n of t instanceof sr?[t]:e?Zf(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return F.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=F.empty;n=n.nextLayer)e=new F(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}F.empty=new F([],[],null,-1);function Zf(s){if(s.length>1)for(let t=s[0],e=1;e<s.length;e++){let i=s[e];if(nr(t,i)>0)return s.slice().sort(nr);t=i}return s}F.empty.nextLayer=F.empty;class le{finishChunk(t){this.chunks.push(new Jr(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new le)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return n<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(F.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return t;let e=F.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Io(s,t,e){let i=new Map;for(let r of s)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let n=new Set;for(let r of t)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(e?e.mapPos(l):l)==r.chunkPos[o]&&!(e!=null&&e.touchesRange(l,l+r.chunk[o].length))&&n.add(r.chunk[o])}return n}class Ua{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let n=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(n)||this.layer.chunkEnd(this.chunkIndex)<t||n.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Mi{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let o=t[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&n.push(new Ua(o,e,i,r));return n.length==1?n[0]:new Mi(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let i=this.heap.length>>1;i>=0;i--)kn(this.heap,i);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let i=this.heap.length>>1;i>=0;i--)kn(this.heap,i);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),kn(this.heap,0)}}}function kn(s,t){for(let e=s[t];;){let i=(t<<1)+1;if(i>=s.length)break;let n=s[i];if(i+1<s.length&&n.compare(s[i+1])>=0&&(n=s[i+1],i++),e.compare(n)<0)break;s[i]=e,s[t]=n,t=i}}class hi{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Mi.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){Yi(this.active,t),Yi(this.activeTo,t),Yi(this.activeRank,t),this.minActive=Fo(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&(r-this.activeRank[e]||n-this.activeTo[e])>0;)e++;Qi(this.active,e,i),Qi(this.activeTo,e,n),Qi(this.activeRank,e,r),t&&Qi(t,e,this.cursor.from),this.minActive=Fo(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&Yi(i,n)}else if(this.cursor.value)if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let n=i.length-1;n>=0&&i[n]<t;n--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function No(s,t,e,i,n,r){s.goto(t),e.goto(i);let o=i+n,l=i,a=i-t;for(;;){let h=s.to+a-e.to,c=h||s.endSide-e.endSide,f=c<0?s.to+a:e.to,u=Math.min(f,o);if(s.point||e.point?s.point&&e.point&&(s.point==e.point||s.point.eq(e.point))&&rr(s.activeForPoint(s.to),e.activeForPoint(e.to))||r.comparePoint(l,u,s.point,e.point):u>l&&!rr(s.active,e.active)&&r.compareRange(l,u,s.active,e.active),f>o)break;(h||s.openEnd!=e.openEnd)&&r.boundChange&&r.boundChange(f),l=f,c<=0&&s.next(),c>=0&&e.next()}}function rr(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(s[e]!=t[e]&&!s[e].eq(t[e]))return!1;return!0}function Yi(s,t){for(let e=t,i=s.length-1;e<i;e++)s[e]=s[e+1];s.pop()}function Qi(s,t,e){for(let i=s.length-1;i>=t;i--)s[i+1]=s[i];s[t]=e}function Fo(s,t){let e=-1,i=1e9;for(let n=0;n<t.length;n++)(t[n]-i||s[n].endSide-s[e].endSide)<0&&(e=n,i=t[n]);return e}function ri(s,t,e=s.length){let i=0;for(let n=0;n<e&&n<s.length;)s.charCodeAt(n)==9?(i+=t-i%t,n++):(i++,n=at(s,n));return i}function or(s,t,e,i){for(let n=0,r=0;;){if(r>=t)return n;if(n==s.length)break;r+=s.charCodeAt(n)==9?e-r%e:1,n=at(s,n)}return i===!0?-1:s.length}const lr="ͼ",zo=typeof Symbol>"u"?"__"+lr:Symbol.for(lr),ar=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),Vo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class ye{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(m=>o.map(g=>m.replace(/&/,g))).reduce((m,g)=>m.concat(g)),p,a);else if(p&&typeof p=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(n(d),p,c,u)}else p!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,m=>"-"+m.toLowerCase())+": "+p+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in t)r(n(o),t[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let t=Vo[zo]||1;return Vo[zo]=t+1,lr+t.toString(36)}static mount(t,e,i){let n=t[ar],r=i&&i.nonce;n?r&&n.setNonce(r):n=new tu(t,r),n.mount(Array.isArray(e)?e:[e],t)}}let Ho=new Map;class tu{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let r=Ho.get(i);if(r)return t[ar]=r;this.sheet=new n.CSSStyleSheet,Ho.set(i,this)}else this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);this.modules=[],t[ar]=this}mount(t,e){let i=this.sheet,n=0,r=0;for(let o=0;o<t.length;o++){let l=t[o],a=this.modules.indexOf(l);if(a<r&&a>-1&&(this.modules.splice(a,1),r--,a=-1),a==-1){if(this.modules.splice(r++,0,l),i)for(let h=0;h<l.rules.length;h++)i.insertRule(l.rules[h],n++)}else{for(;r<a;)n+=this.modules[r++].rules.length;n+=l.rules.length,r++}}if(i)e.adoptedStyleSheets.indexOf(this.sheet)<0&&(e.adoptedStyleSheets=[this.sheet,...e.adoptedStyleSheets]);else{let o="";for(let a=0;a<this.modules.length;a++)o+=this.modules[a].getRules()+`
`;this.styleTag.textContent=o;let l=e.head||e;this.styleTag.parentNode!=l&&l.insertBefore(this.styleTag,l.firstChild)}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}var xe={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Oi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},eu=typeof navigator<"u"&&/Mac/.test(navigator.platform),iu=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var lt=0;lt<10;lt++)xe[48+lt]=xe[96+lt]=String(lt);for(var lt=1;lt<=24;lt++)xe[lt+111]="F"+lt;for(var lt=65;lt<=90;lt++)xe[lt]=String.fromCharCode(lt+32),Oi[lt]=String.fromCharCode(lt);for(var Sn in xe)Oi.hasOwnProperty(Sn)||(Oi[Sn]=xe[Sn]);function su(s){var t=eu&&s.metaKey&&s.shiftKey&&!s.ctrlKey&&!s.altKey||iu&&s.shiftKey&&s.key&&s.key.length==1||s.key=="Unidentified",e=!t&&s.key||(s.shiftKey?Oi:xe)[s.keyCode]||s.key||"Unidentified";return e=="Esc"&&(e="Escape"),e=="Del"&&(e="Delete"),e=="Left"&&(e="ArrowLeft"),e=="Up"&&(e="ArrowUp"),e=="Right"&&(e="ArrowRight"),e=="Down"&&(e="ArrowDown"),e}function Di(s){let t;return s.nodeType==11?t=s.getSelection?s:s.ownerDocument:t=s,t.getSelection()}function hr(s,t){return t?s==t||s.contains(t.nodeType!=1?t.parentNode:t):!1}function vs(s,t){if(!t.anchorNode)return!1;try{return hr(s,t.anchorNode)}catch{return!1}}function Ti(s){return s.nodeType==3?Ie(s,0,s.nodeValue.length).getClientRects():s.nodeType==1?s.getClientRects():[]}function vi(s,t,e,i){return e?Wo(s,t,e,i,-1)||Wo(s,t,e,i,1):!1}function Le(s){for(var t=0;;t++)if(s=s.previousSibling,!s)return t}function Ls(s){return s.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(s.nodeName)}function Wo(s,t,e,i,n){for(;;){if(s==e&&t==i)return!0;if(t==(n<0?0:te(s))){if(s.nodeName=="DIV")return!1;let r=s.parentNode;if(!r||r.nodeType!=1)return!1;t=Le(s)+(n<0?0:1),s=r}else if(s.nodeType==1){if(s=s.childNodes[t+(n<0?-1:0)],s.nodeType==1&&s.contentEditable=="false")return!1;t=n<0?te(s):0}else return!1}}function te(s){return s.nodeType==3?s.nodeValue.length:s.childNodes.length}function ln(s,t){let e=t?s.left:s.right;return{left:e,right:e,top:s.top,bottom:s.bottom}}function nu(s){let t=s.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:s.innerWidth,top:0,bottom:s.innerHeight}}function Ja(s,t){let e=t.width/s.offsetWidth,i=t.height/s.offsetHeight;return(e>.995&&e<1.005||!isFinite(e)||Math.abs(t.width-s.offsetWidth)<1)&&(e=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(t.height-s.offsetHeight)<1)&&(i=1),{scaleX:e,scaleY:i}}function ru(s,t,e,i,n,r,o,l){let a=s.ownerDocument,h=a.defaultView||window;for(let c=s,f=!1;c&&!f;)if(c.nodeType==1){let u,d=c==a.body,p=1,m=1;if(d)u=nu(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let w=c.getBoundingClientRect();({scaleX:p,scaleY:m}=Ja(c,w)),u={left:w.left,right:w.left+c.clientWidth*p,top:w.top,bottom:w.top+c.clientHeight*m}}let g=0,b=0;if(n=="nearest")t.top<u.top?(b=t.top-(u.top+o),e>0&&t.bottom>u.bottom+b&&(b=t.bottom-u.bottom+o)):t.bottom>u.bottom&&(b=t.bottom-u.bottom+o,e<0&&t.top-b<u.top&&(b=t.top-(u.top+o)));else{let w=t.bottom-t.top,k=u.bottom-u.top;b=(n=="center"&&w<=k?t.top+w/2-k/2:n=="start"||n=="center"&&e<0?t.top-o:t.bottom-k+o)-u.top}if(i=="nearest"?t.left<u.left?(g=t.left-(u.left+r),e>0&&t.right>u.right+g&&(g=t.right-u.right+r)):t.right>u.right&&(g=t.right-u.right+r,e<0&&t.left<u.left+g&&(g=t.left-(u.left+r))):g=(i=="center"?t.left+(t.right-t.left)/2-(u.right-u.left)/2:i=="start"==l?t.left-r:t.right-(u.right-u.left)+r)-u.left,g||b)if(d)h.scrollBy(g,b);else{let w=0,k=0;if(b){let S=c.scrollTop;c.scrollTop+=b/m,k=(c.scrollTop-S)*m}if(g){let S=c.scrollLeft;c.scrollLeft+=g/p,w=(c.scrollLeft-S)*p}t={left:t.left-w,top:t.top-k,right:t.right-w,bottom:t.bottom-k},w&&Math.abs(w-g)<1&&(i="nearest"),k&&Math.abs(k-b)<1&&(n="nearest")}if(d)break;(t.top<u.top||t.bottom>u.bottom||t.left<u.left||t.right>u.right)&&(t={left:Math.max(t.left,u.left),right:Math.min(t.right,u.right),top:Math.max(t.top,u.top),bottom:Math.min(t.bottom,u.bottom)}),c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}function ou(s){let t=s.ownerDocument,e,i;for(let n=s.parentNode;n&&!(n==t.body||e&&i);)if(n.nodeType==1)!i&&n.scrollHeight>n.clientHeight&&(i=n),!e&&n.scrollWidth>n.clientWidth&&(e=n),n=n.assignedSlot||n.parentNode;else if(n.nodeType==11)n=n.host;else break;return{x:e,y:i}}class lu{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?te(e):0),i,Math.min(t.focusOffset,i?te(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let He=null;function Ga(s){if(s.setActive)return s.setActive();if(He)return s.focus(He);let t=[];for(let e=s;e&&(t.push(e,e.scrollTop,e.scrollLeft),e!=e.ownerDocument);e=e.parentNode);if(s.focus(He==null?{get preventScroll(){return He={preventScroll:!0},!0}}:void 0),!He){He=!1;for(let e=0;e<t.length;){let i=t[e++],n=t[e++],r=t[e++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let $o;function Ie(s,t,e=t){let i=$o||($o=document.createRange());return i.setEnd(s,e),i.setStart(s,t),i}function Ue(s,t,e,i){let n={key:t,code:t,keyCode:e,which:e,cancelable:!0};i&&({altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey}=i);let r=new KeyboardEvent("keydown",n);r.synthetic=!0,s.dispatchEvent(r);let o=new KeyboardEvent("keyup",n);return o.synthetic=!0,s.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function au(s){for(;s;){if(s&&(s.nodeType==9||s.nodeType==11&&s.host))return s;s=s.assignedSlot||s.parentNode}return null}function Ya(s){for(;s.attributes.length;)s.removeAttributeNode(s.attributes[0])}function hu(s,t){let e=t.focusNode,i=t.focusOffset;if(!e||t.anchorNode!=e||t.anchorOffset!=i)return!1;for(i=Math.min(i,te(e));;)if(i){if(e.nodeType!=1)return!1;let n=e.childNodes[i-1];n.contentEditable=="false"?i--:(e=n,i=te(e))}else{if(e==s)return!0;i=Le(e),e=e.parentNode}}function Qa(s){return s.scrollTop>Math.max(1,s.scrollHeight-s.clientHeight-4)}function Xa(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i>0)return{node:e,offset:i};if(e.nodeType==1&&i>0){if(e.contentEditable=="false")return null;e=e.childNodes[i-1],i=te(e)}else if(e.parentNode&&!Ls(e))i=Le(e),e=e.parentNode;else return null}}function Za(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i<e.nodeValue.length)return{node:e,offset:i};if(e.nodeType==1&&i<e.childNodes.length){if(e.contentEditable=="false")return null;e=e.childNodes[i],i=0}else if(e.parentNode&&!Ls(e))i=Le(e)+1,e=e.parentNode;else return null}}class ct{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new ct(t.parentNode,Le(t),e)}static after(t,e){return new ct(t.parentNode,Le(t)+1,e)}}const Gr=[];class _{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(this.flags&2){let i=this.dom,n=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=n?n.nextSibling:i.firstChild)){let l=_.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r)}o.sync(t,e),o.flags&=-8}if(r=n?n.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&r!=o.dom&&(e.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=qo(r);else i.insertBefore(o.dom,r);n=o.dom}for(r=n?n.nextSibling:i.firstChild,r&&e&&e.node==i&&(e.written=!0);r;)r=qo(r)}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=te(t)==0?0:e==0?-1:1;for(;;){let r=t.parentNode;if(r==this.dom)break;n==0&&r.firstChild!=r.lastChild&&(t==r.firstChild?n=-1:n=1),t=r}n<0?i=t:i=t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!_.get(i);)i=i.nextSibling;if(!i)return this.length;for(let n=0,r=0;;n++){let o=this.children[n];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<t&&u>e)return f.domBoundsAround(t,e,h);if(u>=t&&n==-1&&(n=a,r=h),h>e&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),e.flags&1)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,this.flags&7&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=Gr){this.markDirty();for(let n=t;n<e;n++){let r=this.children[n];r.parent==this&&i.indexOf(r)<0&&r.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let n=0;n<i.length;n++)i[n].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new th(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,r,o){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}_.prototype.breakAfter=0;function qo(s){let t=s.nextSibling;return s.parentNode.removeChild(s),t}class th{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function eh(s,t,e,i,n,r,o,l,a){let{children:h}=s,c=h.length?h[t]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(t==i&&c&&!o&&!u&&r.length<2&&c.merge(e,n,r.length?f:null,e==0,l,a))){if(i<h.length){let d=h[i];d&&(n<d.length||d.breakAfter&&(f!=null&&f.breakAfter))?(t==i&&(d=d.split(n),n=0),!u&&f&&d.merge(0,n,f,!0,0,a)?r[r.length-1]=d:((n||d.children.length&&!d.children[0].length)&&d.merge(0,n,null,!1,0,a),r.push(d))):d!=null&&d.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,e>0&&(!o&&r.length&&c.merge(e,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(e<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(e,c.length,null,!1,l,0),t++));t<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[t].become(r[0]))t++,r.shift(),l=r.length?0:a;else break;!r.length&&t&&i<h.length&&!h[t-1].breakAfter&&h[i].merge(0,0,h[t-1],!1,l,a)&&t--,(t<i||r.length)&&s.replaceChildren(t,i,r)}}function ih(s,t,e,i,n,r){let o=s.childCursor(),{i:l,off:a}=o.findPos(e,1),{i:h,off:c}=o.findPos(t,-1),f=t-e;for(let u of i)f+=u.length;s.length+=f,eh(s,h,c,l,a,i,0,n,r)}let yt=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},cr=typeof document<"u"?document:{documentElement:{style:{}}};const fr=/Edge\/(\d+)/.exec(yt.userAgent),sh=/MSIE \d/.test(yt.userAgent),ur=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(yt.userAgent),an=!!(sh||ur||fr),jo=!an&&/gecko\/(\d+)/i.test(yt.userAgent),Cn=!an&&/Chrome\/(\d+)/.exec(yt.userAgent),_o="webkitFontSmoothing"in cr.documentElement.style,nh=!an&&/Apple Computer/.test(yt.vendor),Ko=nh&&(/Mobile\/\w+/.test(yt.userAgent)||yt.maxTouchPoints>2);var T={mac:Ko||/Mac/.test(yt.platform),windows:/Win/.test(yt.platform),linux:/Linux|X11/.test(yt.platform),ie:an,ie_version:sh?cr.documentMode||6:ur?+ur[1]:fr?+fr[1]:0,gecko:jo,gecko_version:jo?+(/Firefox\/(\d+)/.exec(yt.userAgent)||[0,0])[1]:0,chrome:!!Cn,chrome_version:Cn?+Cn[1]:0,ios:Ko,android:/Android\b/.test(yt.userAgent),webkit:_o,safari:nh,webkit_version:_o?+(/\bAppleWebKit\/(\d+)/.exec(yt.userAgent)||[0,0])[1]:0,tabSize:cr.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const cu=256;class zt extends _{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){t.nodeType==3&&this.createDOM(t)}merge(t,e,i){return this.flags&8||i&&(!(i instanceof zt)||this.length-(e-t)+i.length>cu||i.flags&8)?!1:(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new zt(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=this.flags&8,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new ct(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return fu(this.dom,t,e)}}class ae extends _{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let n of e)n.setParent(this)}setAttrs(t){if(Ya(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,r,o){return i&&(!(i instanceof ae&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&o<=0)?!1:(ih(this,t,e,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,r=0;for(let l of this.children){let a=i+l.length;a>t&&e.push(i<t?l.split(t-i):l),n<0&&i>=t&&(n=r),i=a,r++}let o=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new ae(this.mark,e,o)}domAtPos(t){return rh(this,t)}coordsAt(t,e){return lh(this,t,e)}}function fu(s,t,e){let i=s.nodeValue.length;t>i&&(t=i);let n=t,r=t,o=0;t==0&&e<0||t==i&&e>=0?T.chrome||T.gecko||(t?(n--,o=1):r<i&&(r++,o=-1)):e<0?n--:r<i&&r++;let l=Ie(s,n,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:e>=0)?0:l.length-1];return T.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?ln(a,o<0):a||null}class pe extends _{static create(t,e,i){return new pe(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=pe.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,r,o){return i&&(!(i instanceof pe)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof pe&&t.side==this.side&&this.widget.constructor==t.widget.constructor?(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return z.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):z.empty}domAtPos(t){return(this.length?t==0:this.side>0)?ct.before(this.dom):ct.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),r=null;if(!n.length)return null;let o=this.side?this.side<0:t>0;for(let l=o?n.length-1:0;r=n[l],!(t>0?l==0:l==n.length-1||r.top<r.bottom);l+=o?-1:1);return ln(r,!o)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Ze extends _{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof Ze&&t.side==this.side}split(){return new Ze(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?ct.before(this.dom):ct.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return z.empty}get isHidden(){return!0}}zt.prototype.children=pe.prototype.children=Ze.prototype.children=Gr;function rh(s,t){let e=s.dom,{children:i}=s,n=0;for(let r=0;n<i.length;n++){let o=i[n],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(t>r&&t<l&&o.dom.parentNode==e)return o.domAtPos(t-r);if(t<=r)break;r=l}}for(let r=n;r>0;r--){let o=i[r-1];if(o.dom.parentNode==e)return o.domAtPos(o.length)}for(let r=n;r<i.length;r++){let o=i[r];if(o.dom.parentNode==e)return o.domAtPos(0)}return new ct(e,0)}function oh(s,t,e){let i,{children:n}=s;e>0&&t instanceof ae&&n.length&&(i=n[n.length-1])instanceof ae&&i.mark.eq(t.mark)?oh(i,t.children[0],e-1):(n.push(t),t.setParent(s)),s.length+=t.length}function lh(s,t,e){let i=null,n=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],p=u+d.length;p>=c&&(d.children.length?l(d,c-u):(!r||r.isHidden&&(e>0||du(r,d)))&&(p>c||u==p&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==p&&d.getSide()<0&&!d.isHidden)&&(i=d,n=c-u)),u=p}}l(s,t);let a=(e<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?n:o),e):uu(s)}function uu(s){let t=s.dom.lastChild;if(!t)return s.dom.getBoundingClientRect();let e=Ti(t);return e[e.length-1]||null}function du(s,t){let e=s.coordsAt(0,1),i=t.coordsAt(0,1);return e&&i&&i.top<e.bottom}function dr(s,t){for(let e in s)e=="class"&&t.class?t.class+=" "+s.class:e=="style"&&t.style?t.style+=";"+s.style:t[e]=s[e];return t}const Uo=Object.create(null);function Is(s,t,e){if(s==t)return!0;s||(s=Uo),t||(t=Uo);let i=Object.keys(s),n=Object.keys(t);if(i.length-(e&&i.indexOf(e)>-1?1:0)!=n.length-(e&&n.indexOf(e)>-1?1:0))return!1;for(let r of i)if(r!=e&&(n.indexOf(r)==-1||s[r]!==t[r]))return!1;return!0}function pr(s,t,e){let i=!1;if(t)for(let n in t)e&&n in e||(i=!0,n=="style"?s.style.cssText="":s.removeAttribute(n));if(e)for(let n in e)t&&t[n]==e[n]||(i=!0,n=="style"?s.style.cssText=e[n]:s.setAttribute(n,e[n]));return i}function pu(s){let t=Object.create(null);for(let e=0;e<s.attributes.length;e++){let i=s.attributes[e];t[i.name]=i.value}return t}class Se{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var mt=function(s){return s[s.Text=0]="Text",s[s.WidgetBefore=1]="WidgetBefore",s[s.WidgetAfter=2]="WidgetAfter",s[s.WidgetRange=3]="WidgetRange",s}(mt||(mt={}));class P extends Ee{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new Wi(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new we(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,n;if(t.isBlockGap)i=-5e8,n=4e8;else{let{start:r,end:o}=ah(t,e);i=(r?e?-3e8:-1:5e8)-1,n=(o?e?2e8:1:-6e8)+1}return new we(t,i,n,e,t.widget||null,!0)}static line(t){return new $i(t)}static set(t,e=!1){return F.of(t,e)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}P.none=F.empty;class Wi extends P{constructor(t){let{start:e,end:i}=ah(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof Wi&&this.tagName==t.tagName&&(this.class||((e=this.attrs)===null||e===void 0?void 0:e.class))==(t.class||((i=t.attrs)===null||i===void 0?void 0:i.class))&&Is(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Wi.prototype.point=!1;class $i extends P{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof $i&&this.spec.class==t.spec.class&&Is(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}$i.prototype.mapMode=pt.TrackBefore;$i.prototype.point=!0;class we extends P{constructor(t,e,i,n,r,o){super(e,i,r,t),this.block=n,this.isReplace=o,this.mapMode=n?e<=0?pt.TrackBefore:pt.TrackAfter:pt.TrackDel}get type(){return this.startSide!=this.endSide?mt.WidgetRange:this.startSide<=0?mt.WidgetBefore:mt.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof we&&mu(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}we.prototype.point=!0;function ah(s,t=!1){let{inclusiveStart:e,inclusiveEnd:i}=s;return e==null&&(e=s.inclusive),i==null&&(i=s.inclusive),{start:e??t,end:i??t}}function mu(s,t){return s==t||!!(s&&t&&s.compare(t))}function ks(s,t,e,i=0){let n=e.length-1;n>=0&&e[n]+i>=s?e[n]=Math.max(e[n],t):e.push(s,t)}class Q extends _{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,r,o){if(i){if(!(i instanceof Q))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),ih(this,t,e,i?i.children.slice():[],r,o),!0}split(t){let e=new Q;if(e.breakAfter=this.breakAfter,this.length==0)return e;let{i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)e.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Is(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){oh(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=dr(e,this.attrs||{})),i&&(this.attrs=dr({class:i},this.attrs||{}))}domAtPos(t){return rh(this,t)}reuseDOM(t){t.nodeName=="DIV"&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?this.flags&4&&(Ya(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(pr(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&_.get(n)instanceof ae;)n=n.lastChild;if(!n||!this.length||n.nodeName!="BR"&&((i=_.get(n))===null||i===void 0?void 0:i.isEditable)==!1&&(!T.ios||!this.children.some(r=>r instanceof zt))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof zt)||/[^ -~]/.test(i.text))return null;let n=Ti(i.dom);if(n.length!=1)return null;t+=n[0].width,e=n[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=lh(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:n}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-n.lineHeight)<2&&n.textHeight<r){let o=(r-n.textHeight)/2;return{top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(t){return t instanceof Q&&this.children.length==0&&t.children.length==0&&Is(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],o=n+r.length;if(o>=e){if(r instanceof Q)return r;if(o>e)break}n=o+r.breakAfter}return null}}class oe extends _{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,r,o){return i&&(!(i instanceof oe)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return t==0?ct.before(this.dom):ct.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new oe(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Gr}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):z.empty}domBoundsAround(){return null}become(t){return t instanceof oe&&t.widget.constructor==this.widget.constructor?(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);return i||(this.widget instanceof mr?null:ln(this.dom.getBoundingClientRect(),this.length?t==0:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e==i?!1:t<0?e<0:i>0}}class mr extends Se{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class ki{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof oe&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new Q),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(Xi(new Ze(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(t&&this.content.length&&this.content[this.content.length-1]instanceof oe)&&this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}else this.text=r,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(Xi(new zt(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof we){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=e-t;if(i instanceof we)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new oe(i.widget||ti.block,l,i));else{let a=pe.create(i.widget||ti.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=n.length&&(t<e||i.startSide>0),c=!a.isEditable&&(t<e||r>n.length||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(n),h&&(f.append(Xi(new Ze(1),n),r),r=n.length+Math.max(0,r-n.length)),f.append(Xi(a,n),r),this.atCursorPos=c,this.pendingBuffer=c?t<e||r>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r)}static build(t,e,i,n,r){let o=new ki(t,e,i,r);return o.openEnd=F.spans(n,e,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function Xi(s,t){for(let e of t)s=new ae(e,[s],s.length);return s}class ti extends Se{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}ti.inline=new ti("span");ti.block=new ti("div");var J=function(s){return s[s.LTR=0]="LTR",s[s.RTL=1]="RTL",s}(J||(J={}));const Ne=J.LTR,Yr=J.RTL;function hh(s){let t=[];for(let e=0;e<s.length;e++)t.push(1<<+s[e]);return t}const gu=hh("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),bu=hh("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),gr=Object.create(null),jt=[];for(let s of["()","[]","{}"]){let t=s.charCodeAt(0),e=s.charCodeAt(1);gr[t]=e,gr[e]=-t}function ch(s){return s<=247?gu[s]:1424<=s&&s<=1524?2:1536<=s&&s<=1785?bu[s-1536]:1774<=s&&s<=2220?4:8192<=s&&s<=8204?256:64336<=s&&s<=65023?4:1}const yu=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class me{get dir(){return this.level%2?Yr:Ne}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let r=-1;for(let o=0;o<t.length;o++){let l=t[o];if(l.from<=e&&l.to>=e){if(l.level==i)return o;(r<0||(n!=0?n<0?l.from<e:l.to>e:t[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}function fh(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++){let i=s[e],n=t[e];if(i.from!=n.from||i.to!=n.to||i.direction!=n.direction||!fh(i.inner,n.inner))return!1}return!0}const j=[];function xu(s,t,e,i,n){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:t,l=r<i.length?i[r].from:e,a=r?256:n;for(let h=o,c=a,f=a;h<l;h++){let u=ch(s.charCodeAt(h));u==512?u=c:u==8&&f==4&&(u=16),j[h]=u==4?2:u,u&7&&(f=u),c=u}for(let h=o,c=a,f=a;h<l;h++){let u=j[h];if(u==128)h<l-1&&c==j[h+1]&&c&24?u=j[h]=c:j[h]=256;else if(u==64){let d=h+1;for(;d<l&&j[d]==64;)d++;let p=h&&c==8||d<e&&j[d]==8?f==1?1:8:256;for(let m=h;m<d;m++)j[m]=p;h=d-1}else u==8&&f==1&&(j[h]=1);c=u,u&7&&(f=u)}}}function wu(s,t,e,i,n){let r=n==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:t,c=o<i.length?i[o].from:e;for(let f=h,u,d,p;f<c;f++)if(d=gr[u=s.charCodeAt(f)])if(d<0){for(let m=l-3;m>=0;m-=3)if(jt[m+1]==-d){let g=jt[m+2],b=g&2?n:g&4?g&1?r:n:0;b&&(j[f]=j[jt[m]]=b),l=m;break}}else{if(jt.length==189)break;jt[l++]=f,jt[l++]=u,jt[l++]=a}else if((p=j[f])==2||p==1){let m=p==n;a=m?0:1;for(let g=l-3;g>=0;g-=3){let b=jt[g+2];if(b&2)break;if(m)jt[g+2]|=2;else{if(b&4)break;jt[g+2]|=4}}}}}function vu(s,t,e,i){for(let n=0,r=i;n<=e.length;n++){let o=n?e[n-1].to:s,l=n<e.length?e[n].from:t;for(let a=o;a<l;){let h=j[a];if(h==256){let c=a+1;for(;;)if(c==l){if(n==e.length)break;c=e[n++].to,l=n<e.length?e[n].from:t}else if(j[c]==256)c++;else break;let f=r==1,u=(c<t?j[c]:i)==1,d=f==u?f?1:2:i;for(let p=c,m=n,g=m?e[m-1].to:s;p>a;)p==g&&(p=e[--m].from,g=m?e[m-1].to:s),j[--p]=d;a=c}else r=h,a++}}}function br(s,t,e,i,n,r,o){let l=i%2?2:1;if(i%2==n%2)for(let a=t,h=0;a<e;){let c=!0,f=!1;if(h==r.length||a<r[h].from){let m=j[a];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h<r.length&&p==r[h].from){if(f)break t;let m=r[h];if(!c)for(let g=m.to,b=h+1;;){if(g==e)break t;if(b<r.length&&r[b].from==g)g=r[b++].to;else{if(j[g]==l)break t;break}}if(h++,u)u.push(m);else{m.from>a&&o.push(new me(a,m.from,d));let g=m.direction==Ne!=!(d%2);yr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.to}p=m.to}else{if(p==e||(c?j[p]!=l:j[p]==l))break;p++}u?br(s,a,p,i+1,n,u,o):a<p&&o.push(new me(a,p,d)),a=p}else for(let a=e,h=r.length;a>t;){let c=!0,f=!1;if(!h||a>r[h-1].to){let m=j[a-1];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h&&p==r[h-1].to){if(f)break t;let m=r[--h];if(!c)for(let g=m.from,b=h;;){if(g==t)break t;if(b&&r[b-1].to==g)g=r[--b].from;else{if(j[g-1]==l)break t;break}}if(u)u.push(m);else{m.to<a&&o.push(new me(m.to,a,d));let g=m.direction==Ne!=!(d%2);yr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.from}p=m.from}else{if(p==t||(c?j[p-1]!=l:j[p-1]==l))break;p--}u?br(s,p,a,i+1,n,u,o):p<a&&o.push(new me(p,a,d)),a=p}}function yr(s,t,e,i,n,r,o){let l=t%2?2:1;xu(s,n,r,i,l),wu(s,n,r,i,l),vu(n,r,i,l),br(s,n,r,t,e,i,o)}function ku(s,t,e){if(!s)return[new me(0,0,t==Yr?1:0)];if(t==Ne&&!e.length&&!yu.test(s))return uh(s.length);if(e.length)for(;s.length>j.length;)j[j.length]=256;let i=[],n=t==Ne?0:1;return yr(s,n,n,e,0,s.length,i),i}function uh(s){return[new me(0,s,0)]}let dh="";function Su(s,t,e,i,n){var r;let o=i.head-s.from,l=me.find(t,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=t[l],h=a.side(n,e);if(o==h){let u=l+=n?1:-1;if(u<0||u>=t.length)return null;a=t[l=u],o=a.side(!n,e),h=a.side(n,e)}let c=at(s.text,o,a.forward(n,e));(c<a.from||c>a.to)&&(c=h),dh=s.text.slice(Math.min(o,c),Math.max(o,c));let f=l==(n?t.length-1:0)?null:t[l+(n?1:-1)];return f&&c==h&&f.level+(n?0:1)<a.level?x.cursor(f.side(!n,e)+s.from,f.forward(n,e)?1:-1,f.level):x.cursor(c+s.from,a.forward(n,e)?-1:1,a.level)}function Cu(s,t,e){for(let i=t;i<e;i++){let n=ch(s.charCodeAt(i));if(n==1)return Ne;if(n==2||n==4)return Yr}return Ne}const ph=D.define(),mh=D.define(),gh=D.define(),bh=D.define(),xr=D.define(),yh=D.define(),xh=D.define(),Qr=D.define(),Xr=D.define(),wh=D.define({combine:s=>s.some(t=>t)}),vh=D.define({combine:s=>s.some(t=>t)}),kh=D.define();class Je{constructor(t,e="nearest",i="nearest",n=5,r=5,o=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r,this.isSnapshot=o}map(t){return t.empty?this:new Je(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Je(x.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const Zi=B.define({map:(s,t)=>s.map(t)}),Sh=B.define();function wt(s,t,e){let i=s.facet(bh);i.length?i[0](t):window.onerror?window.onerror(String(t),e,void 0,void 0,t):e?console.error(e+":",t):console.error(t)}const re=D.define({combine:s=>s.length?s[0]:!0});let Au=0;const pi=D.define();class Z{constructor(t,e,i,n,r){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=r(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:r,decorations:o}=e||{};return new Z(Au++,t,i,n,l=>{let a=[pi.of(l)];return o&&a.push(Pi.of(h=>{let c=h.plugin(l);return c?o(c):P.none})),r&&a.push(r(l)),a})}static fromClass(t,e){return Z.define(i=>new t(i),e)}}class An{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let e=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(e)}catch(i){if(wt(e.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){wt(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(!((e=this.value)===null||e===void 0)&&e.destroy)try{this.value.destroy()}catch(i){wt(t.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const Ch=D.define(),Zr=D.define(),Pi=D.define(),Ah=D.define(),to=D.define(),Mh=D.define();function Jo(s,t){let e=s.state.facet(Mh);if(!e.length)return e;let i=e.map(r=>r instanceof Function?r(s):r),n=[];return F.spans(i,t.from,t.to,{point(){},span(r,o,l,a){let h=r-t.from,c=o-t.from,f=n;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,p;if(d==null&&(d=Cu(t.text,h,c)),a>0&&f.length&&(p=f[f.length-1]).to==h&&p.direction==d)p.to=c,f=p.inner;else{let m={from:h,to:c,direction:d,inner:[]};f.push(m),f=m.inner}}}}),n}const Oh=D.define();function eo(s){let t=0,e=0,i=0,n=0;for(let r of s.state.facet(Oh)){let o=r(s);o&&(o.left!=null&&(t=Math.max(t,o.left)),o.right!=null&&(e=Math.max(e,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(n=Math.max(n,o.bottom)))}return{left:t,right:e,top:i,bottom:n}}const mi=D.define();class Et{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new Et(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(e.length==0)return t;let i=[];for(let n=0,r=0,o=0,l=0;;n++){let a=n==t.length?null:t[n],h=o-l,c=a?a.fromB:1e9;for(;r<e.length&&e[r]<c;){let f=e[r],u=e[r+1],d=Math.max(l,f),p=Math.min(c,u);if(d<=p&&new Et(d+h,p+h,d,p).addToSet(i),u>c)break;r+=2}if(!a)return i;new Et(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class Ns{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=tt.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let n=[];this.changes.iterChangedRanges((r,o,l,a)=>n.push(new Et(r,o,l,a))),this.changedRanges=n}static create(t,e,i){return new Ns(t,e,i)}get viewportChanged(){return(this.flags&4)>0}get viewportMoved(){return(this.flags&8)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class Go extends _{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=P.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Q],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new Et(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:c})=>c<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let n=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((e=this.domChanged)===null||e===void 0)&&e.newSel?n=this.domChanged.newSel.head:!Bu(t.changes,this.hasComposition)&&!t.selectionSet&&(n=t.state.selection.main.head));let r=n>-1?Ou(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:c}=this.hasComposition;i=new Et(h,c,t.changes.mapPos(h,-1),t.changes.mapPos(c,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(T.ie||T.chrome)&&!r&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=Pu(o,l,t.changes);return i=Et.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,t.startState.doc.length,r),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=T.chrome||T.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||n.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof oe&&o.widget instanceof mr&&r.push(o.dom);n.updateGaps(r)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,r=this.childCursor(e);for(let o=n.length-1;;o--){let l=o>=0?n[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:c,toB:f}=l,u,d,p,m;if(i&&i.range.fromB<f&&i.range.toB>c){let S=ki.build(this.view.state.doc,c,i.range.fromB,this.decorations,this.dynamicDecorationMap),v=ki.build(this.view.state.doc,i.range.toB,f,this.decorations,this.dynamicDecorationMap);d=S.breakAtStart,p=S.openStart,m=v.openEnd;let C=this.compositionView(i);v.breakAtStart?C.breakAfter=1:v.content.length&&C.merge(C.length,C.length,v.content[0],!1,v.openStart,0)&&(C.breakAfter=v.content[0].breakAfter,v.content.shift()),S.content.length&&C.merge(0,0,S.content[S.content.length-1],!0,0,S.openEnd)&&S.content.pop(),u=S.content.concat(C).concat(v.content)}else({content:u,breakAtStart:d,openStart:p,openEnd:m}=ki.build(this.view.state.doc,c,f,this.decorations,this.dynamicDecorationMap));let{i:g,off:b}=r.findPos(h,1),{i:w,off:k}=r.findPos(a,-1);eh(this,w,k,g,b,u,d,p,m)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let e of t.transactions)for(let i of e.effects)i.is(Sh)&&(this.editContextFormatting=i.value)}compositionView(t){let e=new zt(t.text.nodeValue);e.flags|=8;for(let{deco:n}of t.marks)e=new ae(n,[e],e.length);let i=new Q;return i.append(e,0),i}fixCompositionDOM(t){let e=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=_.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let r=t.marks.length-1;r>=-1;r--)i=n.childPos(i.off,1),n=n.children[i.i],e(r>=0?t.marks[r].node:t.text,n)}updateSelection(t=!1,e=!1){(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,r=!n&&!(this.view.state.facet(re)||this.dom.tabIndex>-1)&&vs(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(T.gecko&&l.empty&&!this.hasComposition&&Mu(a)){let f=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(f,a.node.childNodes[a.offset]||null)),a=h=new ct(f,0),o=!0}let c=this.view.observer.selectionRange;(o||!c.focusNode||(!vi(a.node,a.offset,c.anchorNode,c.anchorOffset)||!vi(h.node,h.offset,c.focusNode,c.focusOffset))&&!this.suppressWidgetCursorChange(c,l))&&(this.view.observer.ignore(()=>{T.android&&T.chrome&&this.dom.contains(c.focusNode)&&Ru(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let f=Di(this.view.root);if(f)if(l.empty){if(T.gecko){let u=Du(a.node,a.offset);if(u&&u!=3){let d=(u==1?Xa:Za)(a.node,a.offset);d&&(a=new ct(d.node,d.offset))}}f.collapse(a.node,a.offset),l.bidiLevel!=null&&f.caretBidiLevel!==void 0&&(f.caretBidiLevel=l.bidiLevel)}else if(f.extend){f.collapse(a.node,a.offset);try{f.extend(h.node,h.offset)}catch{}}else{let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),f.removeAllRanges(),f.addRange(u)}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new ct(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new ct(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&vi(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Di(t.root),{anchorNode:n,anchorOffset:r}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let o=Q.find(this,e.head);if(!o)return;let l=o.posAtStart;if(e.head==l||e.head==l+o.length)return;let a=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(e.head+e.assoc);i.collapse(c.node,c.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let f=t.observer.selectionRange;t.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=e.from&&i.collapse(n,r)}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let n=t.offset;!i&&n<e.childNodes.length;n++){let r=_.get(e.childNodes[n]);r instanceof Q&&(i=r.domAtPos(0))}for(let n=t.offset-1;!i&&n>=0;n--){let r=_.get(e.childNodes[n]);r instanceof Q&&(i=r.domAtPos(r.length))}return i?new ct(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let i=_.get(e);if(i&&i.rootView==this)return i;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let n=this.children[e];if(i<n.length||n instanceof Q)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<t)break;if(h<=t&&(h<t||l.covers(-1))&&(a>t||l.covers(1))&&(!i||l instanceof Q&&!(i instanceof Q&&e>=0)))i=l,n=h;else if(i&&h==t&&a==t&&l instanceof oe&&Math.abs(e)<2){if(l.deco.startSide<0)break;o&&(i=null)}r=h}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof Q))return null;for(;n.children.length;){let{i:l,off:a}=n.childPos(i,1);for(;;l++){if(l==n.children.length)return null;if((n=n.children[l]).length)break}i=a}if(!(n instanceof zt))return null;let r=at(n.text,i);if(r==i)return null;let o=Ie(n.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==J.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>n)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(e.push(d.height),o){let p=f.dom.lastChild,m=p?Ti(p):[];if(m.length){let g=m[m.length-1],b=a?g.right-d.left:d.right-g.left;b>l&&(l=b,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return getComputedStyle(this.children[e].dom).direction=="rtl"?J.RTL:J.LTR}measureTextSize(){for(let r of this.children)if(r instanceof Q){let o=r.measureTextSize();if(o)return o}let t=document.createElement("div"),e,i,n;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let r=Ti(t.firstChild)[0];e=t.getBoundingClientRect().height,i=r?r.width/27:7,n=r?r.height:e,t.remove()}),{lineHeight:e,charWidth:i,textHeight:n}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new th(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],o=r?r.from-1:this.length;if(o>i){let l=(e.lineBlockAt(o).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(P.replace({widget:new mr(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return P.set(t)}updateDeco(){let t=1,e=this.view.state.facet(Pi).map(r=>(this.dynamicDecorationMap[t++]=typeof r=="function")?r(this.view):r),i=!1,n=this.view.state.facet(Ah).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(F.join(n))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let h=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=h.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let h of this.view.state.facet(kh))try{if(h(this.view,t.range,t))return!0}catch(c){wt(this.view.state,c,"scroll handler")}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),n;if(!i)return;!e.empty&&(n=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,n.left),top:Math.min(i.top,n.top),right:Math.max(i.right,n.right),bottom:Math.max(i.bottom,n.bottom)});let r=eo(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;ru(this.view.scrollDOM,o,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,l),-l),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==J.LTR)}}function Mu(s){return s.node.nodeType==1&&s.node.firstChild&&(s.offset==0||s.node.childNodes[s.offset-1].contentEditable=="false")&&(s.offset==s.node.childNodes.length||s.node.childNodes[s.offset].contentEditable=="false")}function Dh(s,t){let e=s.observer.selectionRange;if(!e.focusNode)return null;let i=Xa(e.focusNode,e.focusOffset),n=Za(e.focusNode,e.focusOffset),r=i||n;if(n&&i&&n.node!=i.node){let l=_.get(n.node);if(!l||l instanceof zt&&l.text!=n.node.nodeValue)r=n;else if(s.docView.lastCompositionAfterCursor){let a=_.get(i.node);!a||a instanceof zt&&a.text!=i.node.nodeValue||(r=n)}}if(s.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=t-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}function Ou(s,t,e){let i=Dh(s,e);if(!i)return null;let{node:n,from:r,to:o}=i,l=n.nodeValue;if(/[\n\r]/.test(l)||s.state.doc.sliceString(i.from,i.to)!=l)return null;let a=t.invertedDesc,h=new Et(a.mapPos(r),a.mapPos(o),r,o),c=[];for(let f=n.parentNode;;f=f.parentNode){let u=_.get(f);if(u instanceof ae)c.push({node:f,deco:u.mark});else{if(u instanceof Q||f.nodeName=="DIV"&&f.parentNode==s.contentDOM)return{range:h,text:n,marks:c,line:f};if(f!=s.contentDOM)c.push({node:f,deco:new Wi({inclusive:!0,attributes:pu(f),tagName:f.tagName.toLowerCase()})});else return null}}}function Du(s,t){return s.nodeType!=1?0:(t&&s.childNodes[t-1].contentEditable=="false"?1:0)|(t<s.childNodes.length&&s.childNodes[t].contentEditable=="false"?2:0)}let Tu=class{constructor(){this.changes=[]}compareRange(t,e){ks(t,e,this.changes)}comparePoint(t,e){ks(t,e,this.changes)}boundChange(t){ks(t,t,this.changes)}};function Pu(s,t,e){let i=new Tu;return F.compare(s,t,e,i),i.changes}function Ru(s,t){for(let e=s;e&&e!=t;e=e.assignedSlot||e.parentNode)if(e.nodeType==1&&e.contentEditable=="false")return!0;return!1}function Bu(s,t){let e=!1;return t&&s.iterChangedRanges((i,n)=>{i<t.to&&n>t.from&&(e=!0)}),e}function Eu(s,t,e=1){let i=s.charCategorizer(t),n=s.doc.lineAt(t),r=t-n.from;if(n.length==0)return x.cursor(t);r==0?e=1:r==n.length&&(e=-1);let o=r,l=r;e<0?o=at(n.text,r,!1):l=at(n.text,r);let a=i(n.text.slice(o,l));for(;o>0;){let h=at(n.text,o,!1);if(i(n.text.slice(h,o))!=a)break;o=h}for(;l<n.length;){let h=at(n.text,l);if(i(n.text.slice(l,h))!=a)break;l=h}return x.range(o+n.from,l+n.from)}function Lu(s,t){return t.left>s?t.left-s:Math.max(0,s-t.right)}function Iu(s,t){return t.top>s?t.top-s:Math.max(0,s-t.bottom)}function Mn(s,t){return s.top<t.bottom-1&&s.bottom>t.top+1}function Yo(s,t){return t<s.top?{top:t,left:s.left,right:s.right,bottom:s.bottom}:s}function Qo(s,t){return t>s.bottom?{top:s.top,left:s.left,right:s.right,bottom:t}:s}function wr(s,t,e){let i,n,r,o,l=!1,a,h,c,f;for(let p=s.firstChild;p;p=p.nextSibling){let m=Ti(p);for(let g=0;g<m.length;g++){let b=m[g];n&&Mn(n,b)&&(b=Yo(Qo(b,n.bottom),n.top));let w=Lu(t,b),k=Iu(e,b);if(w==0&&k==0)return p.nodeType==3?Xo(p,t,e):wr(p,t,e);if(!i||o>k||o==k&&r>w){i=p,n=b,r=w,o=k;let S=k?e<b.top?-1:1:w?t<b.left?-1:1:0;l=!S||(S>0?g<m.length-1:g>0)}w==0?e>b.bottom&&(!c||c.bottom<b.bottom)?(a=p,c=b):e<b.top&&(!f||f.top>b.top)&&(h=p,f=b):c&&Mn(c,b)?c=Qo(c,b.bottom):f&&Mn(f,b)&&(f=Yo(f,b.top))}}if(c&&c.bottom>=e?(i=a,n=c):f&&f.top<=e&&(i=h,n=f),!i)return{node:s,offset:0};let u=Math.max(n.left,Math.min(n.right,t));if(i.nodeType==3)return Xo(i,u,e);if(l&&i.contentEditable!="false")return wr(i,u,e);let d=Array.prototype.indexOf.call(s.childNodes,i)+(t>=(n.left+n.right)/2?1:0);return{node:s,offset:d}}function Xo(s,t,e){let i=s.nodeValue.length,n=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=Ie(s,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=t-c.left);let f=(c.top>e?c.top-e:e-c.bottom)-1;if(c.left-1<=t&&c.right+1>=t&&f<r){let u=t>=(c.left+c.right)/2,d=u;if((T.chrome||T.gecko)&&Ie(s,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:s,offset:l+(d?1:0)};n=l+(d?1:0),r=f}}}return{node:s,offset:n>-1?n:o>0?s.nodeValue.length:0}}function Th(s,t,e,i=-1){var n,r;let o=s.contentDOM.getBoundingClientRect(),l=o.top+s.viewState.paddingTop,a,{docHeight:h}=s.viewState,{x:c,y:f}=t,u=f-l;if(u<0)return 0;if(u>h)return s.state.doc.length;for(let S=s.viewState.heightOracle.textHeight/2,v=!1;a=s.elementAtHeight(u),a.type!=mt.Text;)for(;u=i>0?a.bottom+S:a.top-S,!(u>=0&&u<=h);){if(v)return e?null:0;v=!0,i=-i}f=l+u;let d=a.from;if(d<s.viewport.from)return s.viewport.from==0?0:e?null:Zo(s,o,a,c,f);if(d>s.viewport.to)return s.viewport.to==s.state.doc.length?s.state.doc.length:e?null:Zo(s,o,a,c,f);let p=s.dom.ownerDocument,m=s.root.elementFromPoint?s.root:p,g=m.elementFromPoint(c,f);g&&!s.contentDOM.contains(g)&&(g=null),g||(c=Math.max(o.left+1,Math.min(o.right-1,c)),g=m.elementFromPoint(c,f),g&&!s.contentDOM.contains(g)&&(g=null));let b,w=-1;if(g&&((n=s.docView.nearest(g))===null||n===void 0?void 0:n.isEditable)!=!1){if(p.caretPositionFromPoint){let S=p.caretPositionFromPoint(c,f);S&&({offsetNode:b,offset:w}=S)}else if(p.caretRangeFromPoint){let S=p.caretRangeFromPoint(c,f);S&&({startContainer:b,startOffset:w}=S,(!s.contentDOM.contains(b)||T.safari&&Nu(b,w,c)||T.chrome&&Fu(b,w,c))&&(b=void 0))}b&&(w=Math.min(te(b),w))}if(!b||!s.docView.dom.contains(b)){let S=Q.find(s.docView,d);if(!S)return u>a.top+a.height/2?a.to:a.from;({node:b,offset:w}=wr(S.dom,c,f))}let k=s.docView.nearest(b);if(!k)return null;if(k.isWidget&&((r=k.dom)===null||r===void 0?void 0:r.nodeType)==1){let S=k.dom.getBoundingClientRect();return t.y<S.top||t.y<=S.bottom&&t.x<=(S.left+S.right)/2?k.posAtStart:k.posAtEnd}else return k.localPosFromDOM(b,w)+k.posAtStart}function Zo(s,t,e,i,n){let r=Math.round((i-t.left)*s.defaultCharacterWidth);if(s.lineWrapping&&e.height>s.defaultLineHeight*1.5){let l=s.viewState.heightOracle.textHeight,a=Math.floor((n-e.top-(s.defaultLineHeight-l)*.5)/l);r+=a*s.viewState.heightOracle.lineLength}let o=s.state.sliceDoc(e.from,e.to);return e.from+or(o,r,s.state.tabSize)}function Nu(s,t,e){let i;if(s.nodeType!=3||t!=(i=s.nodeValue.length))return!1;for(let n=s.nextSibling;n;n=n.nextSibling)if(n.nodeType!=1||n.nodeName!="BR")return!1;return Ie(s,i-1,i).getBoundingClientRect().left>e}function Fu(s,t,e){if(t!=0)return!1;for(let n=s;;){let r=n.parentNode;if(!r||r.nodeType!=1||r.firstChild!=n)return!1;if(r.classList.contains("cm-line"))break;n=r}let i=s.nodeType==1?s.getBoundingClientRect():Ie(s,0,Math.max(s.nodeValue.length,1)).getBoundingClientRect();return e-i.left>5}function vr(s,t){let e=s.lineBlockAt(t);if(Array.isArray(e.type)){for(let i of e.type)if(i.to>t||i.to==t&&(i.to==e.to||i.type==mt.Text))return i}return e}function zu(s,t,e,i){let n=vr(s,t.head),r=!i||n.type!=mt.Text||!(s.lineWrapping||n.widgetLineBreaks)?null:s.coordsAtPos(t.assoc<0&&t.head>n.from?t.head-1:t.head);if(r){let o=s.dom.getBoundingClientRect(),l=s.textDirectionAt(n.from),a=s.posAtCoords({x:e==(l==J.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return x.cursor(a,e?-1:1)}return x.cursor(e?n.to:n.from,e?-1:1)}function tl(s,t,e,i){let n=s.state.doc.lineAt(t.head),r=s.bidiSpans(n),o=s.textDirectionAt(n.from);for(let l=t,a=null;;){let h=Su(n,r,o,l,e),c=dh;if(!h){if(n.number==(e?s.state.doc.lines:1))return l;c=`
`,n=s.state.doc.line(n.number+(e?1:-1)),r=s.bidiSpans(n),h=s.visualLineSide(n,!e)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function Vu(s,t,e){let i=s.state.charCategorizer(t),n=i(e);return r=>{let o=i(r);return n==G.Space&&(n=o),n==o}}function Hu(s,t,e,i){let n=t.head,r=e?1:-1;if(n==(e?s.state.doc.length:0))return x.cursor(n,t.assoc);let o=t.goalColumn,l,a=s.contentDOM.getBoundingClientRect(),h=s.coordsAtPos(n,t.assoc||-1),c=s.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=s.viewState.lineBlockAt(n);o==null&&(o=Math.min(a.right-a.left,s.defaultCharacterWidth*(n-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??s.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,m=Th(s,{x:f,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?m<n:m>n)){let g=s.docView.coordsForChar(m),b=!g||p<g.top?-1:1;return x.cursor(m,b,void 0,o)}}}function Ss(s,t,e){for(;;){let i=0;for(let n of s)n.between(t-1,t+1,(r,o,l)=>{if(t>r&&t<o){let a=i||e||(t-r<o-t?-1:1);t=a<0?r:o,i=a}});if(!i)return t}}function On(s,t,e){let i=Ss(s.state.facet(to).map(n=>n(s)),e.from,t.head>e.from?-1:1);return i==e.from?e:x.cursor(i,i<e.from?1:-1)}const gi="￿";class Wu{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(V.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=gi}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let r=this.text.length;this.readNode(n);let o=n.nextSibling;if(o==e)break;let l=_.get(n),a=_.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:Ls(n))||Ls(o)&&(n.nodeName!="BR"||n.cmIgnore)&&this.text.length>r)&&this.lineBreak(),n=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=n.exec(e))&&(r=l.index,o=l[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==t&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=_.get(t),i=e&&e.overrideDOMText;if(i!=null){this.findPointInside(t,i.length);for(let n=i.iter();!n.next().done;)n.lineBreak?this.lineBreak():this.append(n.value)}else t.nodeType==3?this.readTextNode(t):t.nodeName=="BR"?t.nextSibling&&this.lineBreak():t.nodeType==1&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(t.nodeType==3?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+($u(t,i.node,i.offset)?e:0))}}function $u(s,t,e){for(;;){if(!t||e<te(t))return!1;if(t==s)return!0;e=Le(t)+1,t=t.parentNode}}class el{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class qu{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:r,impreciseAnchor:o}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let l=r||o?[]:Ku(t),a=new Wu(l,t.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=Uu(l,this.bounds.from)}else{let l=t.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!hr(t.contentDOM,l.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!hr(t.contentDOM,l.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(l.anchorNode,l.anchorOffset),c=t.viewport;if((T.ios||T.chrome)&&t.state.selection.main.empty&&a!=h&&(c.from>0||c.to<t.state.doc.length)){let f=Math.min(a,h),u=Math.max(a,h),d=c.from-f,p=c.to-u;(d==0||d==1||f==0)&&(p==0||p==-1||u==t.state.doc.length)&&(a=0,h=t.state.doc.length)}this.newSel=x.single(h,a)}}}function Ph(s,t){let e,{newSel:i}=t,n=s.state.selection.main,r=s.inputState.lastKeyTime>Date.now()-100?s.inputState.lastKeyCode:-1;if(t.bounds){let{from:o,to:l}=t.bounds,a=n.from,h=null;(r===8||T.android&&t.text.length<l-o)&&(a=n.to,h="end");let c=_u(s.state.doc.sliceString(o,l,gi),t.text,a-o,h);c&&(T.chrome&&r==13&&c.toB==c.from+2&&t.text.slice(c.from,c.toB)==gi+gi&&c.toB--,e={from:o+c.from,to:o+c.toA,insert:z.of(t.text.slice(c.from,c.toB).split(gi))})}else i&&(!s.hasFocus&&s.state.facet(re)||i.main.eq(n))&&(i=null);if(!e&&!i)return!1;if(!e&&t.typeOver&&!n.empty&&i&&i.main.empty?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,n.to)}:(T.mac||T.android)&&e&&e.from==e.to&&e.from==n.head-1&&/^\. ?$/.test(e.insert.toString())&&s.contentDOM.getAttribute("autocorrect")=="off"?(i&&e.insert.length==2&&(i=x.single(i.main.anchor-1,i.main.head-1)),e={from:e.from,to:e.to,insert:z.of([e.insert.toString().replace("."," ")])}):e&&e.from>=n.from&&e.to<=n.to&&(e.from!=n.from||e.to!=n.to)&&n.to-n.from-(e.to-e.from)<=4?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,e.from).append(e.insert).append(s.state.doc.slice(e.to,n.to))}:T.chrome&&e&&e.from==e.to&&e.from==n.head&&e.insert.toString()==`
 `&&s.lineWrapping&&(i&&(i=x.single(i.main.anchor-1,i.main.head-1)),e={from:n.from,to:n.to,insert:z.of([" "])}),e)return io(s,e,i,r);if(i&&!i.main.eq(n)){let o=!1,l="select";return s.inputState.lastSelectionTime>Date.now()-50&&(s.inputState.lastSelectionOrigin=="select"&&(o=!0),l=s.inputState.lastSelectionOrigin),s.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return!1}function io(s,t,e,i=-1){if(T.ios&&s.inputState.flushIOSKey(t))return!0;let n=s.state.selection.main;if(T.android&&(t.to==n.to&&(t.from==n.from||t.from==n.from-1&&s.state.sliceDoc(t.from,n.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&Ue(s.contentDOM,"Enter",13)||(t.from==n.from-1&&t.to==n.to&&t.insert.length==0||i==8&&t.insert.length<t.to-t.from&&t.to>n.head)&&Ue(s.contentDOM,"Backspace",8)||t.from==n.from&&t.to==n.to+1&&t.insert.length==0&&Ue(s.contentDOM,"Delete",46)))return!0;let r=t.insert.toString();s.inputState.composing>=0&&s.inputState.composing++;let o,l=()=>o||(o=ju(s,t,e));return s.state.facet(yh).some(a=>a(s,t.from,t.to,r,l))||s.dispatch(l()),!0}function ju(s,t,e){let i,n=s.state,r=n.selection.main;if(t.from>=r.from&&t.to<=r.to&&t.to-t.from>=(r.to-r.from)/3&&(!e||e.main.empty&&e.main.from==t.from+t.insert.length)&&s.inputState.composing<0){let l=r.from<t.from?n.sliceDoc(r.from,t.from):"",a=r.to>t.to?n.sliceDoc(t.to,r.to):"";i=n.replaceSelection(s.state.toText(l+t.insert.sliceString(0,void 0,s.state.lineBreak)+a))}else{let l=n.changes(t),a=e&&e.main.to<=l.newLength?e.main:void 0;if(n.selection.ranges.length>1&&s.inputState.composing>=0&&t.to<=r.to&&t.to>=r.to-10){let h=s.state.sliceDoc(t.from,t.to),c,f=e&&Dh(s,e.main.head);if(f){let p=t.insert.length-(t.to-t.from);c={from:f.from,to:f.to-p}}else c=s.state.doc.lineAt(r.head);let u=r.to-t.to,d=r.to-r.from;i=n.changeByRange(p=>{if(p.from==r.from&&p.to==r.to)return{changes:l,range:a||p.map(l)};let m=p.to-u,g=m-h.length;if(p.to-p.from!=d||s.state.sliceDoc(g,m)!=h||p.to>=c.from&&p.from<=c.to)return{range:p};let b=n.changes({from:g,to:m,insert:t.insert}),w=p.to-r.to;return{changes:b,range:a?x.range(Math.max(0,a.anchor+w),Math.max(0,a.head+w)):p.map(b)}})}else i={changes:l,selection:a&&n.selection.replaceRange(a)}}let o="input.type";return(s.composing||s.inputState.compositionPendingChange&&s.inputState.compositionEndedAt>Date.now()-50)&&(s.inputState.compositionPendingChange=!1,o+=".compose",s.inputState.compositionFirstChange&&(o+=".start",s.inputState.compositionFirstChange=!1)),n.update(i,{userEvent:o,scrollIntoView:!0})}function _u(s,t,e,i){let n=Math.min(s.length,t.length),r=0;for(;r<n&&s.charCodeAt(r)==t.charCodeAt(r);)r++;if(r==n&&s.length==t.length)return null;let o=s.length,l=t.length;for(;o>0&&l>0&&s.charCodeAt(o-1)==t.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));e-=o+a-r}if(o<r&&s.length<t.length){let a=e<=r&&e>=o?r-e:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=e<=r&&e>=l?r-e:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function Ku(s){let t=[];if(s.root.activeElement!=s.contentDOM)return t;let{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}=s.observer.selectionRange;return e&&(t.push(new el(e,i)),(n!=e||r!=i)&&t.push(new el(n,r))),t}function Uu(s,t){if(s.length==0)return null;let e=s[0].pos,i=s.length==2?s[1].pos:e;return e>-1&&i>-1?x.single(e+t,i+t):null}class Ju{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,T.safari&&t.contentDOM.addEventListener("input",()=>null),T.gecko&&cd(t.contentDOM.ownerDocument)}handleEvent(t){!id(this.view,t)||this.ignoreDuringComposition(t)||t.type=="keydown"&&this.keydown(t)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let n of i.observers)n(this.view,e);for(let n of i.handlers){if(e.defaultPrevented)break;if(n(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=Gu(t),i=this.handlers,n=this.view.contentDOM;for(let r in e)if(r!="scroll"){let o=!e[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(n.removeEventListener(r,this.handleEvent),l=null),l||n.addEventListener(r,this.handleEvent,{passive:o})}for(let r in i)r!="scroll"&&!e[r]&&n.removeEventListener(r,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&t.keyCode!=27&&Bh.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),T.android&&T.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return T.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=Rh.find(i=>i.keyCode==t.keyCode))&&!t.ctrlKey||Yu.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(t.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(t){let e=this.pendingIOSKey;return!e||e.key=="Enter"&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString())?!1:(this.pendingIOSKey=void 0,Ue(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return/^key/.test(t.type)?this.composing>0?!0:T.safari&&!T.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function il(s,t){return(e,i)=>{try{return t.call(s,i,e)}catch(n){wt(e.state,n)}}}function Gu(s){let t=Object.create(null);function e(i){return t[i]||(t[i]={observers:[],handlers:[]})}for(let i of s){let n=i.spec;if(n&&n.domEventHandlers)for(let r in n.domEventHandlers){let o=n.domEventHandlers[r];o&&e(r).handlers.push(il(i.value,o))}if(n&&n.domEventObservers)for(let r in n.domEventObservers){let o=n.domEventObservers[r];o&&e(r).observers.push(il(i.value,o))}}for(let i in Vt)e(i).handlers.push(Vt[i]);for(let i in It)e(i).observers.push(It[i]);return t}const Rh=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Yu="dthko",Bh=[16,17,18,20,91,92,224,225],ts=6;function es(s){return Math.max(0,s)*.7+8}function Qu(s,t){return Math.max(Math.abs(s.clientX-t.clientX),Math.abs(s.clientY-t.clientY))}class Xu{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=ou(t.contentDOM),this.atoms=t.state.facet(to).map(o=>o(t));let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(V.allowMultipleSelections)&&Zu(t,e),this.dragging=ed(t,e)&&Ih(e)==1?null:!1}start(t){this.dragging===!1&&this.select(t)}move(t){if(t.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&Qu(this.startEvent,t)<10)return;this.select(this.lastEvent=t);let e=0,i=0,n=0,r=0,o=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:n,right:o}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:r,bottom:l}=this.scrollParents.y.getBoundingClientRect());let a=eo(this.view);t.clientX-a.left<=n+ts?e=-es(n-t.clientX):t.clientX+a.right>=o-ts&&(e=es(t.clientX-o)),t.clientY-a.top<=r+ts?i=-es(r-t.clientY):t.clientY+a.bottom>=l-ts&&(i=es(t.clientY-l)),this.setScrollSpeed(e,i)}up(t){this.dragging==null&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],r=null;if(n.empty){let o=Ss(this.atoms,n.from,0);o!=n.from&&(r=x.cursor(o,-1))}else{let o=Ss(this.atoms,n.from,-1),l=Ss(this.atoms,n.to,1);(o!=n.from||l!=n.to)&&(r=x.range(n.from==n.anchor?o:l,n.from==n.head?o:l))}r&&(e||(e=t.ranges.slice()),e[i]=r)}return e?x.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(e=>e.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Zu(s,t){let e=s.state.facet(ph);return e.length?e[0](t):T.mac?t.metaKey:t.ctrlKey}function td(s,t){let e=s.state.facet(mh);return e.length?e[0](t):T.mac?!t.altKey:!t.ctrlKey}function ed(s,t){let{main:e}=s.state.selection;if(e.empty)return!1;let i=Di(s.root);if(!i||i.rangeCount==0)return!0;let n=i.getRangeAt(0).getClientRects();for(let r=0;r<n.length;r++){let o=n[r];if(o.left<=t.clientX&&o.right>=t.clientX&&o.top<=t.clientY&&o.bottom>=t.clientY)return!0}return!1}function id(s,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let e=t.target,i;e!=s.contentDOM;e=e.parentNode)if(!e||e.nodeType==11||(i=_.get(e))&&i.ignoreEvent(t))return!1;return!0}const Vt=Object.create(null),It=Object.create(null),Eh=T.ie&&T.ie_version<15||T.ios&&T.webkit_version<604;function sd(s){let t=s.dom.parentNode;if(!t)return;let e=t.appendChild(document.createElement("textarea"));e.style.cssText="position: fixed; left: -10000px; top: 10px",e.focus(),setTimeout(()=>{s.focus(),e.remove(),Lh(s,e.value)},50)}function hn(s,t,e){for(let i of s.facet(t))e=i(e,s);return e}function Lh(s,t){t=hn(s.state,Qr,t);let{state:e}=s,i,n=1,r=e.toText(t),o=r.lines==e.selection.ranges.length;if(kr!=null&&e.selection.ranges.every(a=>a.empty)&&kr==r.toString()){let a=-1;i=e.changeByRange(h=>{let c=e.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=e.toText((o?r.line(n++).text:t)+e.lineBreak);return{changes:{from:c.from,insert:f},range:x.cursor(h.from+f.length)}})}else o?i=e.changeByRange(a=>{let h=r.line(n++);return{changes:{from:a.from,to:a.to,insert:h.text},range:x.cursor(a.from+h.length)}}):i=e.replaceSelection(r);s.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}It.scroll=s=>{s.inputState.lastScrollTop=s.scrollDOM.scrollTop,s.inputState.lastScrollLeft=s.scrollDOM.scrollLeft};Vt.keydown=(s,t)=>(s.inputState.setSelectionOrigin("select"),t.keyCode==27&&s.inputState.tabFocusMode!=0&&(s.inputState.tabFocusMode=Date.now()+2e3),!1);It.touchstart=(s,t)=>{s.inputState.lastTouchTime=Date.now(),s.inputState.setSelectionOrigin("select.pointer")};It.touchmove=s=>{s.inputState.setSelectionOrigin("select.pointer")};Vt.mousedown=(s,t)=>{if(s.observer.flush(),s.inputState.lastTouchTime>Date.now()-2e3)return!1;let e=null;for(let i of s.state.facet(gh))if(e=i(s,t),e)break;if(!e&&t.button==0&&(e=od(s,t)),e){let i=!s.hasFocus;s.inputState.startMouseSelection(new Xu(s,t,e,i)),i&&s.observer.ignore(()=>{Ga(s.contentDOM);let r=s.root.activeElement;r&&!r.contains(s.contentDOM)&&r.blur()});let n=s.inputState.mouseSelection;if(n)return n.start(t),n.dragging===!1}return!1};function sl(s,t,e,i){if(i==1)return x.cursor(t,e);if(i==2)return Eu(s.state,t,e);{let n=Q.find(s.docView,t),r=s.state.doc.lineAt(n?n.posAtEnd:t),o=n?n.posAtStart:r.from,l=n?n.posAtEnd:r.to;return l<s.state.doc.length&&l==r.to&&l++,x.range(o,l)}}let nl=(s,t,e)=>t>=e.top&&t<=e.bottom&&s>=e.left&&s<=e.right;function nd(s,t,e,i){let n=Q.find(s.docView,t);if(!n)return 1;let r=t-n.posAtStart;if(r==0)return 1;if(r==n.length)return-1;let o=n.coordsAt(r,-1);if(o&&nl(e,i,o))return-1;let l=n.coordsAt(r,1);return l&&nl(e,i,l)?1:o&&o.bottom>=i?-1:1}function rl(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1);return{pos:e,bias:nd(s,e,t.clientX,t.clientY)}}const rd=T.ie&&T.ie_version<=11;let ol=null,ll=0,al=0;function Ih(s){if(!rd)return s.detail;let t=ol,e=al;return ol=s,al=Date.now(),ll=!t||e>Date.now()-400&&Math.abs(t.clientX-s.clientX)<2&&Math.abs(t.clientY-s.clientY)<2?(ll+1)%3:1}function od(s,t){let e=rl(s,t),i=Ih(t),n=s.state.selection;return{update(r){r.docChanged&&(e.pos=r.changes.mapPos(e.pos),n=n.map(r.changes))},get(r,o,l){let a=rl(s,r),h,c=sl(s,a.pos,a.bias,i);if(e.pos!=a.pos&&!o){let f=sl(s,e.pos,e.bias,i),u=Math.min(f.from,c.from),d=Math.max(f.to,c.to);c=u<c.from?x.range(u,d):x.range(d,u)}return o?n.replaceRange(n.main.extend(c.from,c.to)):l&&i==1&&n.ranges.length>1&&(h=ld(n,a.pos))?h:l?n.addRange(c):x.create([c])}}}function ld(s,t){for(let e=0;e<s.ranges.length;e++){let{from:i,to:n}=s.ranges[e];if(i<=t&&n>=t)return x.create(s.ranges.slice(0,e).concat(s.ranges.slice(e+1)),s.mainIndex==e?0:s.mainIndex-(s.mainIndex>e?1:0))}return null}Vt.dragstart=(s,t)=>{let{selection:{main:e}}=s.state;if(t.target.draggable){let n=s.docView.nearest(t.target);if(n&&n.isWidget){let r=n.posAtStart,o=r+n.length;(r>=e.to||o<=e.from)&&(e=x.range(r,o))}}let{inputState:i}=s;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=e,t.dataTransfer&&(t.dataTransfer.setData("Text",hn(s.state,Xr,s.state.sliceDoc(e.from,e.to))),t.dataTransfer.effectAllowed="copyMove"),!1};Vt.dragend=s=>(s.inputState.draggedContent=null,!1);function hl(s,t,e,i){if(e=hn(s.state,Qr,e),!e)return;let n=s.posAtCoords({x:t.clientX,y:t.clientY},!1),{draggedContent:r}=s.inputState,o=i&&r&&td(s,t)?{from:r.from,to:r.to}:null,l={from:n,insert:e},a=s.state.changes(o?[o,l]:l);s.focus(),s.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:o?"move.drop":"input.drop"}),s.inputState.draggedContent=null}Vt.drop=(s,t)=>{if(!t.dataTransfer)return!1;if(s.state.readOnly)return!0;let e=t.dataTransfer.files;if(e&&e.length){let i=Array(e.length),n=0,r=()=>{++n==e.length&&hl(s,t,i.filter(o=>o!=null).join(s.state.lineBreak),!1)};for(let o=0;o<e.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(e[o])}return!0}else{let i=t.dataTransfer.getData("Text");if(i)return hl(s,t,i,!0),!0}return!1};Vt.paste=(s,t)=>{if(s.state.readOnly)return!0;s.observer.flush();let e=Eh?null:t.clipboardData;return e?(Lh(s,e.getData("text/plain")||e.getData("text/uri-list")),!0):(sd(s),!1)};function ad(s,t){let e=s.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=t,i.focus(),i.selectionEnd=t.length,i.selectionStart=0,setTimeout(()=>{i.remove(),s.focus()},50)}function hd(s){let t=[],e=[],i=!1;for(let n of s.selection.ranges)n.empty||(t.push(s.sliceDoc(n.from,n.to)),e.push(n));if(!t.length){let n=-1;for(let{from:r}of s.selection.ranges){let o=s.doc.lineAt(r);o.number>n&&(t.push(o.text),e.push({from:o.from,to:Math.min(s.doc.length,o.to+1)})),n=o.number}i=!0}return{text:hn(s,Xr,t.join(s.lineBreak)),ranges:e,linewise:i}}let kr=null;Vt.copy=Vt.cut=(s,t)=>{let{text:e,ranges:i,linewise:n}=hd(s.state);if(!e&&!n)return!1;kr=n?e:null,t.type=="cut"&&!s.state.readOnly&&s.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Eh?null:t.clipboardData;return r?(r.clearData(),r.setData("text/plain",e),!0):(ad(s,e),!1)};const Nh=he.define();function Fh(s,t){let e=[];for(let i of s.facet(xh)){let n=i(s,t);n&&e.push(n)}return e?s.update({effects:e,annotations:Nh.of(!0)}):null}function zh(s){setTimeout(()=>{let t=s.hasFocus;if(t!=s.inputState.notifiedFocused){let e=Fh(s.state,t);e?s.dispatch(e):s.update([])}},10)}It.focus=s=>{s.inputState.lastFocusTime=Date.now(),!s.scrollDOM.scrollTop&&(s.inputState.lastScrollTop||s.inputState.lastScrollLeft)&&(s.scrollDOM.scrollTop=s.inputState.lastScrollTop,s.scrollDOM.scrollLeft=s.inputState.lastScrollLeft),zh(s)};It.blur=s=>{s.observer.clearSelectionRange(),zh(s)};It.compositionstart=It.compositionupdate=s=>{s.observer.editContext||(s.inputState.compositionFirstChange==null&&(s.inputState.compositionFirstChange=!0),s.inputState.composing<0&&(s.inputState.composing=0))};It.compositionend=s=>{s.observer.editContext||(s.inputState.composing=-1,s.inputState.compositionEndedAt=Date.now(),s.inputState.compositionPendingKey=!0,s.inputState.compositionPendingChange=s.observer.pendingRecords().length>0,s.inputState.compositionFirstChange=null,T.chrome&&T.android?s.observer.flushSoon():s.inputState.compositionPendingChange?Promise.resolve().then(()=>s.observer.flush()):setTimeout(()=>{s.inputState.composing<0&&s.docView.hasComposition&&s.update([])},50))};It.contextmenu=s=>{s.inputState.lastContextMenu=Date.now()};Vt.beforeinput=(s,t)=>{var e,i;if(t.inputType=="insertReplacementText"&&s.observer.editContext){let r=(e=t.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain"),o=t.getTargetRanges();if(r&&o.length){let l=o[0],a=s.posAtDOM(l.startContainer,l.startOffset),h=s.posAtDOM(l.endContainer,l.endOffset);return io(s,{from:a,to:h,insert:s.state.toText(r)},null),!0}}let n;if(T.chrome&&T.android&&(n=Rh.find(r=>r.inputType==t.inputType))&&(s.observer.delayAndroidKey(n.key,n.keyCode),n.key=="Backspace"||n.key=="Delete")){let r=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout(()=>{var o;(((o=window.visualViewport)===null||o===void 0?void 0:o.height)||0)>r+10&&s.hasFocus&&(s.contentDOM.blur(),s.focus())},100)}return T.ios&&t.inputType=="deleteContentForward"&&s.observer.flushSoon(),T.safari&&t.inputType=="insertText"&&s.inputState.composing>=0&&setTimeout(()=>It.compositionend(s,t),20),!1};const cl=new Set;function cd(s){cl.has(s)||(cl.add(s),s.addEventListener("copy",()=>{}),s.addEventListener("cut",()=>{}))}const fl=["pre-wrap","normal","pre-line","break-spaces"];let ei=!1;function ul(){ei=!1}class fd{constructor(t){this.lineWrapping=t,this.doc=z.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return fl.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(n*10)]||(e=!0,this.heightSamples[Math.floor(n*10)]=!0)}return e}refresh(t,e,i,n,r,o){let l=fl.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let c=o[h];c<0?h++:this.heightSamples[Math.floor(c*10)]=!0}}return a}}class ud{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class Yt{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this._content=r}get type(){return typeof this._content=="number"?mt.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof we?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new Yt(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var U=function(s){return s[s.ByPos=0]="ByPos",s[s.ByHeight=1]="ByHeight",s[s.ByPosNoHeight=2]="ByPosNoHeight",s}(U||(U={}));const Cs=.001;class gt{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&-3}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>Cs&&(ei=!0),this.height=t)}replace(t,e,i){return gt.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let r=this,o=i.doc;for(let l=n.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:c,toB:f}=n[l],u=r.lineAt(a,U.ByPosNoHeight,i.setDoc(e),0,0),d=u.to>=h?u:r.lineAt(h,U.ByPosNoHeight,i,0,0);for(f+=d.to-h,h=d.to;l>0&&u.from<=n[l-1].toA;)a=n[l-1].fromA,c=n[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,U.ByPosNoHeight,i,0,0));c+=u.from-a,a=u.from;let p=so.build(i.setDoc(o),t,c,f);r=Fs(r,r.replace(a,h,p))}return r.updateHeight(i,0)}static empty(){return new Mt(0,0)}static of(t){if(t.length==1)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>r*2){let l=t[e-1];l.break?t.splice(--e,1,l.left,null,l.right):t.splice(--e,1,l.left,l.right),i+=1+l.break,n-=l.size}else if(r>n*2){let l=t[i];l.break?t.splice(i,1,l.left,null,l.right):t.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(n<r){let l=t[e++];l&&(n+=l.size)}else{let l=t[--i];l&&(r+=l.size)}let o=0;return t[e-1]==null?(o=1,e--):t[e]==null&&(o=1,i++),new dd(gt.of(t.slice(0,e)),o,gt.of(t.slice(i)))}}function Fs(s,t){return s==t?s:(s.constructor!=t.constructor&&(ei=!0),t)}gt.prototype.size=1;class Vh extends gt{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new Yt(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,o){t<=r+this.length&&e>=r&&o(this.blockAt(0,i,n,r))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Mt extends Vh{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new Yt(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return i.length==1&&(n instanceof Mt||n instanceof ot&&n.flags&4)&&Math.abs(this.length-n.length)<10?(n instanceof ot?n=new Mt(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):gt.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(n.heights[n.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ot extends gt{constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,n=t.doc.lineAt(e+this.length).number,r=n-i+1,o,l=0;if(t.lineWrapping){let a=Math.min(this.height,t.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1))}else o=this.height/r;return{firstLine:i,lastLine:n,perLine:o,perChar:l}}blockAt(t,e,i,n){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let h=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),c=e.doc.lineAt(h),f=l+c.length*a,u=Math.max(i,t-f/2);return new Yt(c.from,c.length,u,f,0)}else{let h=Math.max(0,Math.min(o-r,Math.floor((t-i)/l))),{from:c,length:f}=e.doc.line(r+h);return new Yt(c,f,i+l*h,l,0)}}lineAt(t,e,i,n,r){if(e==U.ByHeight)return this.blockAt(t,i,n,r);if(e==U.ByPosNoHeight){let{from:d,to:p}=i.doc.lineAt(t);return new Yt(d,p-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(t),c=l+h.length*a,f=h.number-o,u=n+l*f+a*(h.from-r-f);return new Yt(h.from,h.length,Math.max(n,Math.min(u,n+this.height-c)),c,0)}forEachLine(t,e,i,n,r,o){t=Math.max(t,r),e=Math.min(e,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let c=t,f=n;c<=e;){let u=i.doc.lineAt(c);if(c==t){let p=u.number-l;f+=a*p+h*(t-r-p)}let d=a+h*u.length;o(new Yt(u.from,u.length,f,d,0)),f+=d,c=u.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let r=i[i.length-1];r instanceof ot?i[i.length-1]=new ot(r.length+n):i.push(null,new ot(n-1))}if(t>0){let r=i[0];r instanceof ot?i[0]=new ot(t+r.length):i.unshift(new ot(t-1),null)}return gt.of(i)}decomposeLeft(t,e){e.push(new ot(t-1),null)}decomposeRight(t,e){e.push(null,new ot(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let o=[],l=Math.max(e,n.from),a=-1;for(n.from>e&&o.push(new ot(n.from-e-1).updateHeight(t,e));l<=r&&n.more;){let c=t.doc.lineAt(l).length;o.length&&o.push(null);let f=n.heights[n.index++];a==-1?a=f:Math.abs(f-a)>=Cs&&(a=-2);let u=new Mt(c,f);u.outdated=!1,o.push(u),l+=c+1}l<=r&&o.push(null,new ot(r-l).updateHeight(t,l));let h=gt.of(o);return(a<0||Math.abs(h.height-this.height)>=Cs||Math.abs(a-this.heightMetrics(t,e).perLine)>=Cs)&&(ei=!0),Fs(this,h)}else(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class dd extends gt{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return this.flags&1}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let o=n+this.left.height,l=r+this.left.length+this.break,a=e==U.ByHeight?t<o:t<l,h=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=e==U.ByPosNoHeight?U.ByPosNoHeight:U.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,n,r).join(h)}forEachLine(t,e,i,n,r,o){let l=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,o),e>=a&&this.right.forEachLine(t,e,i,l,a,o);else{let h=this.lineAt(a,U.ByPos,i,n,r);t<h.from&&this.left.forEachLine(t,h.from-1,i,n,r,o),h.to>=t&&h.from<=e&&o(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,l,a,o)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let o=r.length;for(let l of i)r.push(l);if(t>0&&dl(r,o-1),e<this.length){let l=r.length;this.decomposeRight(e,r),dl(r,l)}return gt.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?gt.of(this.break?[t,null,e]:[t,e]):(this.left=Fs(this.left,t),this.right=Fs(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:o}=this,l=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=l+o.length&&n.more?a=o=o.updateHeight(t,l,i,n):o.updateHeight(t,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function dl(s,t){let e,i;s[t]==null&&(e=s[t-1])instanceof ot&&(i=s[t+1])instanceof ot&&s.splice(t-1,3,new ot(e.length+1+i.length))}const pd=5;class so{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let i=Math.min(e,this.lineEnd),n=this.nodes[this.nodes.length-1];n instanceof Mt?n.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Mt(i-this.pos,-1)),this.writtenTo=i,e>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let o=e-t;i.block?this.addBlock(new Vh(o,n,i)):(o||r||n>=pd)&&this.addLineDeco(n,r,o)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Mt(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new ot(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Mt)return t;let e=new Mt(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(e instanceof Mt)&&!this.isCovered?this.nodes.push(new Mt(0,-1)):(this.writtenTo<this.pos||e==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=t;for(let n of this.nodes)n instanceof Mt&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(t,e,i,n){let r=new so(i,t);return F.spans(e,i,n,r,0),r.finish(i)}}function md(s,t,e){let i=new gd;return F.compare(s,t,e,i,0),i.changes}class gd{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&ks(t,e,this.changes,5)}}function bd(s,t){let e=s.getBoundingClientRect(),i=s.ownerDocument,n=i.defaultView||window,r=Math.max(0,e.left),o=Math.min(n.innerWidth,e.right),l=Math.max(0,e.top),a=Math.min(n.innerHeight,e.bottom);for(let h=s.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=Math.min(h==s.parentNode?n.innerHeight:a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-e.left,right:Math.max(r,o)-e.left,top:l-(e.top+t),bottom:Math.max(l,a)-(e.top+t)}}function yd(s){let t=s.getBoundingClientRect(),e=s.ownerDocument.defaultView||window;return t.left<e.innerWidth&&t.right>0&&t.top<e.innerHeight&&t.bottom>0}function xd(s,t){let e=s.getBoundingClientRect();return{left:0,right:e.right-e.left,top:t,bottom:e.bottom-(e.top+t)}}class Dn{constructor(t,e,i,n){this.from=t,this.to=e,this.size=i,this.displaySize=n}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(t,e){return P.replace({widget:new wd(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class wd extends Se{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class pl{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=ml,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=J.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(Zr).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new fd(e),this.stateDeco=t.facet(Pi).filter(i=>typeof i!="function"),this.heightMap=gt.empty().applyChanges(this.stateDeco,z.empty,this.heightOracle.setDoc(t.doc),[new Et(0,0,0,t.doc.length)]);for(let i=0;i<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());i++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=P.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some(({from:r,to:o})=>n>=r&&n<=o)){let{from:r,to:o}=this.lineBlockAt(n);t.push(new is(r,o))}}return this.viewports=t.sort((i,n)=>i.from-n.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?ml:new no(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(bi(t,this.scaler))})}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Pi).filter(c=>typeof c!="function");let n=t.changedRanges,r=Et.extendWithRanges(n,md(i,this.stateDeco,t?t.changes:tt.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);ul(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=o||ei)&&(t.flags|=2),l?(this.scrollAnchorPos=t.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=r.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let h=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,t.flags|=this.updateForViewport(),(h||!t.changes.empty||t.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(vh)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?J.RTL:J.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=e.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,c=0;if(l.width&&l.height){let{scaleX:S,scaleY:v}=Ja(e,l);(S>.005&&Math.abs(this.scaleX-S)>.005||v>.005&&Math.abs(this.scaleY-v)>.005)&&(this.scaleX=S,this.scaleY=v,h|=16,o=a=!0)}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=f||this.paddingBottom!=u)&&(this.paddingTop=f,this.paddingBottom=u,h|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,h|=16);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=Qa(t.scrollDOM);let p=(this.printing?xd:bd)(e,this.paddingTop),m=p.top-this.pixelViewport.top,g=p.bottom-this.pixelViewport.bottom;this.pixelViewport=p;let b=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(b!=this.inView&&(this.inView=b,b&&(a=!0)),!this.inView&&!this.scrollTarget&&!yd(t.dom))return 0;let w=l.width;if((this.contentDOMWidth!=w||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=t.scrollDOM.clientHeight,h|=16),a){let S=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(S)&&(o=!0),o||n.lineWrapping&&Math.abs(w-this.contentDOMWidth)>n.charWidth){let{lineHeight:v,charWidth:C,textHeight:M}=t.docView.measureTextSize();o=v>0&&n.refresh(r,v,C,M,w/C,S),o&&(t.docView.minWidth=0,h|=16)}m>0&&g>0?c=Math.max(m,g):m<0&&g<0&&(c=Math.min(m,g)),ul();for(let v of this.viewports){let C=v.from==this.viewport.from?S:t.docView.measureVisibleLineHeights(v);this.heightMap=(o?gt.empty().applyChanges(this.stateDeco,z.empty,this.heightOracle,[new Et(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,o,new ud(v.from,C))}ei&&(h|=2)}let k=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return k&&(h&2&&(h|=this.updateScaler()),this.viewport=this.getViewport(c,this.scrollTarget),h|=this.updateForViewport()),(h&2||k)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,t)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new is(n.lineAt(o-i*1e3,U.ByHeight,r,0,0).from,n.lineAt(l+(1-i)*1e3,U.ByHeight,r,0,0).to);if(e){let{head:h}=e.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=n.lineAt(h,U.ByPos,r,0,0),u;e.y=="center"?u=(f.top+f.bottom)/2-c/2:e.y=="start"||e.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new is(n.lineAt(u-1e3/2,U.ByHeight,r,0,0).from,n.lineAt(u+c+1e3/2,U.ByHeight,r,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new is(this.heightMap.lineAt(i,U.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,U.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,U.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(e,U.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return(t==0||n<=o-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&n>o-2*1e3&&r<l+2*1e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new Dn(e.mapPos(n.from),e.mapPos(n.to),n.size,n.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,r=n>>1,o=n<<1;if(this.defaultTextDirection!=J.LTR&&!i)return[];let l=[],a=(c,f,u,d)=>{if(f-c<r)return;let p=this.state.selection.main,m=[p.from];p.empty||m.push(p.to);for(let b of m)if(b>c&&b<f){a(c,b-10,u,d),a(b+10,f,u,d);return}let g=kd(t,b=>b.from>=u.from&&b.to<=u.to&&Math.abs(b.from-c)<r&&Math.abs(b.to-f)<r&&!m.some(w=>b.from<w&&b.to>w));if(!g){if(f<u.to&&e&&i&&e.visibleRanges.some(k=>k.from<=f&&k.to>=f)){let k=e.moveToLineBoundary(x.cursor(f),!1,!0).head;k>c&&(f=k)}let b=this.gapSize(u,c,f,d),w=i||b<2e6?b:2e6;g=new Dn(c,f,b,w)}l.push(g)},h=c=>{if(c.length<o||c.type!=mt.Text)return;let f=vd(c.from,c.to,this.stateDeco);if(f.total<o)return;let u=this.scrollTarget?this.scrollTarget.range.head:null,d,p;if(i){let m=n/this.heightOracle.lineLength*this.heightOracle.lineHeight,g,b;if(u!=null){let w=ns(f,u),k=((this.visibleBottom-this.visibleTop)/2+m)/c.height;g=w-k,b=w+k}else g=(this.visibleTop-c.top-m)/c.height,b=(this.visibleBottom-c.top+m)/c.height;d=ss(f,g),p=ss(f,b)}else{let m=f.total*this.heightOracle.charWidth,g=n*this.heightOracle.charWidth,b=0;if(m>2e6)for(let C of t)C.from>=c.from&&C.from<c.to&&C.size!=C.displaySize&&C.from*this.heightOracle.charWidth+b<this.pixelViewport.left&&(b=C.size-C.displaySize);let w=this.pixelViewport.left+b,k=this.pixelViewport.right+b,S,v;if(u!=null){let C=ns(f,u),M=((k-w)/2+g)/m;S=C-M,v=C+M}else S=(w-g)/m,v=(k+g)/m;d=ss(f,S),p=ss(f,v)}d>c.from&&a(c.from,d,c,f),p<c.to&&a(p,c.to,c,f)};for(let c of this.viewportLines)Array.isArray(c.type)?c.type.forEach(h):h(c);return l}gapSize(t,e,i,n){let r=ns(n,i)-ns(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){Dn.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=P.set(t.map(e=>e.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];F.spans(e,this.viewport.from,this.viewport.to,{span(r,o){i.push({from:r,to:o})},point(){}},20);let n=0;if(i.length!=this.visibleRanges.length)n=12;else for(let r=0;r<i.length&&!(n&8);r++){let o=this.visibleRanges[r],l=i[r];(o.from!=l.from||o.to!=l.to)&&(n|=4,t&&t.mapPos(o.from,-1)==l.from&&t.mapPos(o.to,1)==l.to||(n|=8))}return this.visibleRanges=i,n}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||bi(this.heightMap.lineAt(t,U.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||bi(this.heightMap.lineAt(this.scaler.fromDOM(t),U.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return bi(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class is{constructor(t,e){this.from=t,this.to=e}}function vd(s,t,e){let i=[],n=s,r=0;return F.spans(e,s,t,{span(){},point(o,l){o>n&&(i.push({from:n,to:o}),r+=o-n),n=l}},20),n<t&&(i.push({from:n,to:t}),r+=t-n),{total:r,ranges:i}}function ss({total:s,ranges:t},e){if(e<=0)return t[0].from;if(e>=1)return t[t.length-1].to;let i=Math.floor(s*e);for(let n=0;;n++){let{from:r,to:o}=t[n],l=o-r;if(i<=l)return r+i;i-=l}}function ns(s,t){let e=0;for(let{from:i,to:n}of s.ranges){if(t<=n){e+=t-i;break}e+=n-i}return e/s.total}function kd(s,t){for(let e of s)if(t(e))return e}const ml={toDOM(s){return s},fromDOM(s){return s},scale:1,eq(s){return s==this}};class no{constructor(t,e,i){let n=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=e.lineAt(l,U.ByPos,t,0,0).top,c=e.lineAt(a,U.ByPos,t,0,0).bottom;return n+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(e.height-n);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom}}eq(t){return t instanceof no?this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to):!1}}function bi(s,t){if(t.scale==1)return s;let e=t.toDOM(s.top),i=t.toDOM(s.bottom);return new Yt(s.from,s.length,e,i-e,Array.isArray(s._content)?s._content.map(n=>bi(n,t)):s._content)}const rs=D.define({combine:s=>s.join(" ")}),Sr=D.define({combine:s=>s.indexOf(!0)>-1}),Cr=ye.newName(),Hh=ye.newName(),Wh=ye.newName(),$h={"&light":"."+Hh,"&dark":"."+Wh};function Ar(s,t,e){return new ye(t,{finish(i){return/&/.test(i)?i.replace(/&\w*/,n=>{if(n=="&")return s;if(!e||!e[n])throw new RangeError(`Unsupported selector: ${n}`);return e[n]}):s+" "+i}})}const Sd=Ar("."+Cr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},$h),Cd={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},Tn=T.ie&&T.ie_version<=11;class Ad{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new lu,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let i of e)this.queue.push(i);(T.ie&&T.ie_version<=11||T.ios&&t.composing)&&e.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&t.constructor.EDIT_CONTEXT!==!1&&!(T.chrome&&T.chrome_version<126)&&(this.editContext=new Od(t),t.state.facet(re)&&(t.contentDOM.editContext=this.editContext.editContext)),Tn&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var e;((e=this.view.docView)===null||e===void 0?void 0:e.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(e=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),e.length>0&&e[e.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(e=>{e.length>0&&e[e.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){(t.type=="change"||!t.type)&&!t.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(re)?i.root.activeElement!=this.dom:!vs(this.dom,n))return;let r=n.anchorNode&&i.docView.nearest(n.anchorNode);if(r&&r.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(T.ie&&T.ie_version<=11||T.android&&T.chrome)&&!i.state.selection.main.empty&&n.focusNode&&vi(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Di(t.root);if(!e)return!1;let i=T.safari&&t.root.nodeType==11&&t.root.activeElement==this.dom&&Md(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let n=vs(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&hu(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(i.nodeType==1)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=e)i.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,Cd),Tn&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),Tn&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let n=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&Ue(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(n)}(!this.delayedAndroidKey||t=="Enter")&&(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let o=this.readMutation(r);o&&(o.typeOver&&(n=!0),e==-1?{from:e,to:i}=o:(e=Math.min(o.from,e),i=Math.max(o.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&vs(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new qu(this.view,t,e,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Ph(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty(t.type=="attributes"),t.type=="attributes"&&(e.flags|=4),t.type=="childList"){let i=gl(e,t.previousSibling||t.target.previousSibling,-1),n=gl(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}else return t.type=="characterData"?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(re)!=t.state.facet(re)&&(t.view.contentDOM.editContext=t.state.facet(re)?this.editContext.editContext:null))}destroy(){var t,e,i;this.stop(),(t=this.intersection)===null||t===void 0||t.disconnect(),(e=this.gapIntersection)===null||e===void 0||e.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function gl(s,t,e){for(;t;){let i=_.get(t);if(i&&i.parent==s)return i;let n=t.parentNode;t=n!=s.dom?n:e>0?t.nextSibling:t.previousSibling}return null}function bl(s,t){let e=t.startContainer,i=t.startOffset,n=t.endContainer,r=t.endOffset,o=s.docView.domAtPos(s.state.selection.main.anchor);return vi(o.node,o.offset,n,r)&&([e,i,n,r]=[n,r,e,i]),{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}}function Md(s,t){if(t.getComposedRanges){let n=t.getComposedRanges(s.root)[0];if(n)return bl(s,n)}let e=null;function i(n){n.preventDefault(),n.stopImmediatePropagation(),e=n.getTargetRanges()[0]}return s.contentDOM.addEventListener("beforeinput",i,!0),s.dom.ownerDocument.execCommand("indent"),s.contentDOM.removeEventListener("beforeinput",i,!0),e?bl(s,e):null}class Od{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=i=>{let n=t.state.selection.main,{anchor:r,head:o}=n,l=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:l,drifted:!1});let h={from:l,to:a,insert:z.of(i.text.split(`
`))};if(h.from==this.from&&r<this.from?h.from=r:h.to==this.to&&r>this.to&&(h.to=r),h.from==h.to&&!h.insert.length){let c=x.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));c.main.eq(n)||t.dispatch({selection:c,userEvent:"select"});return}if((T.mac||T.android)&&h.from==o-1&&/^\. ?$/.test(i.text)&&t.contentDOM.getAttribute("autocorrect")=="off"&&(h={from:l,to:a,insert:z.of([i.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let c=this.to-this.from+(h.to-h.from+h.insert.length);io(t,h,x.single(this.toEditorPos(i.selectionStart,c),this.toEditorPos(i.selectionEnd,c)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=i=>{let n=[],r=null;for(let o=this.toEditorPos(i.rangeStart),l=this.toEditorPos(i.rangeEnd);o<l;o++){let a=t.coordsForChar(o);r=a&&new DOMRect(a.left,a.top,a.right-a.left,a.bottom-a.top)||r||new DOMRect,n.push(r)}e.updateCharacterBounds(i.rangeStart,n)},this.handlers.textformatupdate=i=>{let n=[];for(let r of i.getTextFormats()){let o=r.underlineStyle,l=r.underlineThickness;if(o!="None"&&l!="None"){let a=this.toEditorPos(r.rangeStart),h=this.toEditorPos(r.rangeEnd);if(a<h){let c=`text-decoration: underline ${o=="Dashed"?"dashed ":o=="Squiggle"?"wavy ":""}${l=="Thin"?1:2}px`;n.push(P.mark({attributes:{style:c}}).range(a,h))}}}t.dispatch({effects:Sh.of(P.set(n))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:i}=this.composing;this.composing=null,i&&this.reset(t.state)}};for(let i in this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:i=>{this.editContext.updateControlBounds(i.contentDOM.getBoundingClientRect());let n=Di(i.root);n&&n.rangeCount&&this.editContext.updateSelectionBounds(n.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,n=this.pendingContextChange;return t.changes.iterChanges((r,o,l,a,h)=>{if(i)return;let c=h.length-(o-r);if(n&&o>=n.to)if(n.from==r&&n.to==o&&n.insert.eq(h)){n=this.pendingContextChange=null,e+=c,this.to+=c;return}else n=null,this.revertPending(t.state);if(r+=e,o+=e,o<=this.from)this.from+=c,this.to+=c;else if(r<this.to){if(r<this.from||o>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(r),this.toContextPos(o),h.toString()),this.to+=c}e+=c}),n&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(n=>!n.isUserEvent("input.type")&&n.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):!this.applyEdits(t)||!this.rangeIsValid(t.state)?(this.pendingContextChange=null,this.reset(t.state)):(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),n=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=n)&&this.editContext.updateSelection(i,n)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>1e4*3)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class O{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;this.dispatchTransactions=t.dispatchTransactions||i&&(n=>n.forEach(r=>i(r,this)))||(n=>this.update(n)),this.dispatch=this.dispatch.bind(this),this._root=t.root||au(t.parent)||document,this.viewState=new pl(t.state||V.create(t)),t.scrollTo&&t.scrollTo.is(Zi)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(pi).map(n=>new An(n));for(let n of this.plugins)n.update(this);this.observer=new Ad(this),this.inputState=new Ju(this),this.inputState.ensureHandlers(this.plugins),this.docView=new Go(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((e=document.fonts)===null||e===void 0)&&e.ready&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=t.length==1&&t[0]instanceof et?t:t.length==1&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,n,r=this.state;for(let u of t){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;t.some(u=>u.annotation(Nh))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=Fh(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,c=null;if(h?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(c=null)):this.observer.clear(),r.facet(V.phrases)!=this.state.facet(V.phrases))return this.setState(r);n=Ns.create(this,r,t),n.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let u of t){if(f&&(f=f.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;f=new Je(d.empty?d:x.cursor(d.head,d.head>d.anchor?-1:1))}for(let d of u.effects)d.is(Zi)&&(f=d.value.clip(this.state))}this.viewState.update(n,f),this.bidiCache=zs.update(this.bidiCache,n.changes),n.empty||(this.updatePlugins(n),this.inputState.update(n)),e=this.docView.update(n),this.state.facet(mi)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(u=>u.isUserEvent("select.pointer")))}finally{this.updateState=0}if(n.startState.facet(rs)!=n.state.facet(rs)&&(this.viewState.mustMeasureContent=!0),(e||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!n.empty)for(let u of this.state.facet(xr))try{u(n)}catch(d){wt(this.state,d,"update listener")}(a||c)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),c&&!Ph(this,c)&&h.force&&Ue(this.contentDOM,h.key,h.keyCode)})}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new pl(t),this.plugins=t.facet(pi).map(i=>new An(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new Go(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(pi),i=t.state.facet(pi);if(e!=i){let n=[];for(let r of i){let o=e.indexOf(r);if(o<0)n.push(new An(r));else{let l=this.plugins[o];l.mustUpdate=t,n.push(l)}}for(let r of this.plugins)r.mustUpdate!=t&&r.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let n of this.plugins)n.mustUpdate=t;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(i){wt(this.state,i,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(Qa(i))r=-1,o=this.viewState.heightMap.height;else{let d=this.viewState.scrollAnchorAt(n);r=d.from,o=d.top}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let c=h.map(d=>{try{return d.read(this)}catch(p){return wt(this.state,p),yl}}),f=Ns.create(this,this.state,[]),u=!1;f.flags|=a,e?e.flags|=a:e=f,this.updateState=2,f.empty||(this.updatePlugins(f),this.inputState.update(f),this.updateAttrs(),u=this.docView.update(f),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(c[d]!=yl)try{let p=h[d];p.write&&p.write(c[d],this)}catch(p){wt(this.state,p)}if(u&&this.docView.updateSelection(!0),!f.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else{let p=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(p>1||p<-1){n=n+p,i.scrollTop=n/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let l of this.state.facet(xr))l(e)}get themeClasses(){return Cr+" "+(this.state.facet(Sr)?Wh:Hh)+" "+this.state.facet(rs)}updateAttrs(){let t=xl(this,Ch,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(re)?"true":"false",class:"cm-content",style:`${T.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),xl(this,Zr,e);let i=this.observer.ignore(()=>{let n=pr(this.contentDOM,this.contentAttrs,e),r=pr(this.dom,this.editorAttrs,t);return n||r});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let n of i.effects)if(n.is(O.announce)){e&&(this.announceDOM.textContent=""),e=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=n.value}}mountStyles(){this.styleModules=this.state.facet(mi);let t=this.state.facet(O.cspNonce);ye.mount(this.root,this.styleModules.concat(Sd).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(e===void 0||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find(i=>i.spec==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return On(this,t,tl(this,t,e,i))}moveByGroup(t,e){return On(this,t,tl(this,t,e,i=>Vu(this,t.head,i)))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),r=i[e?i.length-1:0];return x.cursor(r.side(e,n)+t.from,r.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return zu(this,t,e,i)}moveVertically(t,e,i){return On(this,t,Hu(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Th(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n),o=r[me.find(r,t-n.from,-1,e)];return ln(i,o.dir==J.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(wh)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Dd)return uh(t.length);let e=this.textDirectionAt(t.from),i;for(let r of this.bidiCache)if(r.from==t.from&&r.dir==e&&(r.fresh||fh(r.isolates,i=Jo(this,t))))return r.order;i||(i=Jo(this,t));let n=ku(t.text,e,i);return this.bidiCache.push(new zs(t.from,t.to,e,i,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||T.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{Ga(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return Zi.of(new Je(typeof t=="number"?x.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return Zi.of(new Je(x.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){t==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof t=="boolean"?this.inputState.tabFocusMode=t?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return Z.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return Z.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=ye.newName(),n=[rs.of(i),mi.of(Ar(`.${i}`,t))];return e&&e.dark&&n.push(Sr.of(!0)),n}static baseTheme(t){return ze.lowest(mi.of(Ar("."+Cr,t,$h)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&_.get(i)||_.get(t);return((e=n==null?void 0:n.rootView)===null||e===void 0?void 0:e.view)||null}}O.styleModule=mi;O.inputHandler=yh;O.clipboardInputFilter=Qr;O.clipboardOutputFilter=Xr;O.scrollHandler=kh;O.focusChangeEffect=xh;O.perLineTextDirection=wh;O.exceptionSink=bh;O.updateListener=xr;O.editable=re;O.mouseSelectionStyle=gh;O.dragMovesSelection=mh;O.clickAddsSelectionRange=ph;O.decorations=Pi;O.outerDecorations=Ah;O.atomicRanges=to;O.bidiIsolatedRanges=Mh;O.scrollMargins=Oh;O.darkTheme=Sr;O.cspNonce=D.define({combine:s=>s.length?s[0]:""});O.contentAttributes=Zr;O.editorAttributes=Ch;O.lineWrapping=O.contentAttributes.of({class:"cm-lineWrapping"});O.announce=B.define();const Dd=4096,yl={};class zs{constructor(t,e,i,n,r,o){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=r,this.order=o}static update(t,e){if(e.empty&&!t.some(r=>r.fresh))return t;let i=[],n=t.length?t[t.length-1].dir:J.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let o=t[r];o.dir==n&&!e.touchesRange(o.from,o.to)&&i.push(new zs(e.mapPos(o.from,1),e.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order))}return i}}function xl(s,t,e){for(let i=s.state.facet(t),n=i.length-1;n>=0;n--){let r=i[n],o=typeof r=="function"?r(s):r;o&&dr(o,e)}return e}const Td=T.mac?"mac":T.windows?"win":T.linux?"linux":"key";function Pd(s,t){const e=s.split(/-(?!$)/);let i=e[e.length-1];i=="Space"&&(i=" ");let n,r,o,l;for(let a=0;a<e.length-1;++a){const h=e[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))n=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))t=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return n&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function os(s,t,e){return t.altKey&&(s="Alt-"+s),t.ctrlKey&&(s="Ctrl-"+s),t.metaKey&&(s="Meta-"+s),e!==!1&&t.shiftKey&&(s="Shift-"+s),s}const Rd=ze.default(O.domEventHandlers({keydown(s,t){return jh(qh(t.state),s,t,"editor")}})),ro=D.define({enables:Rd}),wl=new WeakMap;function qh(s){let t=s.facet(ro),e=wl.get(t);return e||wl.set(t,e=Ld(t.reduce((i,n)=>i.concat(n),[]))),e}function Bd(s,t,e){return jh(qh(s.state),t,s,e)}let ue=null;const Ed=4e3;function Ld(s,t=Td){let e=Object.create(null),i=Object.create(null),n=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,c)=>{var f,u;let d=e[o]||(e[o]=Object.create(null)),p=l.split(/ (?!$)/).map(b=>Pd(b,t));for(let b=1;b<p.length;b++){let w=p.slice(0,b).join(" ");n(w,!0),d[w]||(d[w]={preventDefault:!0,stopPropagation:!1,run:[k=>{let S=ue={view:k,prefix:w,scope:o};return setTimeout(()=>{ue==S&&(ue=null)},Ed),!0}]})}let m=p.join(" ");n(m,!1);let g=d[m]||(d[m]={preventDefault:!1,stopPropagation:!1,run:((u=(f=d._any)===null||f===void 0?void 0:f.run)===null||u===void 0?void 0:u.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0),c&&(g.stopPropagation=!0)};for(let o of s){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=e[h]||(e[h]=Object.create(null));c._any||(c._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:f}=o;for(let u in c)c[u].run.push(d=>f(d,Mr))}let a=o[t]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation)}return e}let Mr=null;function jh(s,t,e,i){Mr=t;let n=su(t),r=bt(n,0),o=Gt(r)==n.length&&n!=" ",l="",a=!1,h=!1,c=!1;ue&&ue.view==e&&ue.scope==i&&(l=ue.prefix+" ",Bh.indexOf(t.keyCode)<0&&(h=!0,ue=null));let f=new Set,u=g=>{if(g){for(let b of g.run)if(!f.has(b)&&(f.add(b),b(e)))return g.stopPropagation&&(c=!0),!0;g.preventDefault&&(g.stopPropagation&&(c=!0),h=!0)}return!1},d=s[i],p,m;return d&&(u(d[l+os(n,t,!o)])?a=!0:o&&(t.altKey||t.metaKey||t.ctrlKey)&&!(T.windows&&t.ctrlKey&&t.altKey)&&(p=xe[t.keyCode])&&p!=n?(u(d[l+os(p,t,!0)])||t.shiftKey&&(m=Oi[t.keyCode])!=n&&m!=p&&u(d[l+os(m,t,!1)]))&&(a=!0):o&&t.shiftKey&&u(d[l+os(n,t,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&c&&t.stopPropagation(),Mr=null,a}class qi{constructor(t,e,i,n,r){this.className=t,this.left=e,this.top=i,this.width=n,this.height=r}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className!=this.className?!1:(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",this.width!=null&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let r=_h(t);return[new qi(e,n.left-r.left,n.top-r.top,null,n.bottom-n.top)]}else return Id(t,e,i)}}function _h(s){let t=s.scrollDOM.getBoundingClientRect();return{left:(s.textDirection==J.LTR?t.left:t.right-s.scrollDOM.clientWidth*s.scaleX)-s.scrollDOM.scrollLeft*s.scaleX,top:t.top-s.scrollDOM.scrollTop*s.scaleY}}function vl(s,t,e,i){let n=s.coordsAtPos(t,e*2);if(!n)return i;let r=s.dom.getBoundingClientRect(),o=(n.top+n.bottom)/2,l=s.posAtCoords({x:r.left+1,y:o}),a=s.posAtCoords({x:r.right-1,y:o});return l==null||a==null?i:{from:Math.max(i.from,Math.min(l,a)),to:Math.min(i.to,Math.max(l,a))}}function Id(s,t,e){if(e.to<=s.viewport.from||e.from>=s.viewport.to)return[];let i=Math.max(e.from,s.viewport.from),n=Math.min(e.to,s.viewport.to),r=s.textDirection==J.LTR,o=s.contentDOM,l=o.getBoundingClientRect(),a=_h(s),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),d=vr(s,i),p=vr(s,n),m=d.type==mt.Text?d:null,g=p.type==mt.Text?p:null;if(m&&(s.lineWrapping||d.widgetLineBreaks)&&(m=vl(s,i,1,m)),g&&(s.lineWrapping||p.widgetLineBreaks)&&(g=vl(s,n,-1,g)),m&&g&&m.from==g.from&&m.to==g.to)return w(k(e.from,e.to,m));{let v=m?k(e.from,null,m):S(d,!1),C=g?k(null,e.to,g):S(p,!0),M=[];return(m||d).to<(g||p).from-(m&&g?1:0)||d.widgetLineBreaks>1&&v.bottom+s.defaultLineHeight/2<C.top?M.push(b(f,v.bottom,u,C.top)):v.bottom<C.top&&s.elementAtHeight((v.bottom+C.top)/2).type==mt.Text&&(v.bottom=C.top=(v.bottom+C.top)/2),w(v).concat(M).concat(w(C))}function b(v,C,M,E){return new qi(t,v-a.left,C-a.top,M-v,E-C)}function w({top:v,bottom:C,horizontal:M}){let E=[];for(let N=0;N<M.length;N+=2)E.push(b(M[N],v,M[N+1],C));return E}function k(v,C,M){let E=1e9,N=-1e9,$=[];function I(H,K,dt,St,$t){let rt=s.coordsAtPos(H,H==M.to?-2:2),Dt=s.coordsAtPos(dt,dt==M.from?2:-2);!rt||!Dt||(E=Math.min(rt.top,Dt.top,E),N=Math.max(rt.bottom,Dt.bottom,N),$t==J.LTR?$.push(r&&K?f:rt.left,r&&St?u:Dt.right):$.push(!r&&St?f:Dt.left,!r&&K?u:rt.right))}let R=v??M.from,W=C??M.to;for(let H of s.visibleRanges)if(H.to>R&&H.from<W)for(let K=Math.max(H.from,R),dt=Math.min(H.to,W);;){let St=s.state.doc.lineAt(K);for(let $t of s.bidiSpans(St)){let rt=$t.from+St.from,Dt=$t.to+St.from;if(rt>=dt)break;Dt>K&&I(Math.max(rt,K),v==null&&rt<=R,Math.min(Dt,dt),C==null&&Dt>=W,$t.dir)}if(K=St.to+1,K>=dt)break}return $.length==0&&I(R,v==null,W,C==null,s.textDirection),{top:E,bottom:N,horizontal:$}}function S(v,C){let M=l.top+(C?v.top:v.bottom);return{top:M,bottom:M,horizontal:[]}}}function Nd(s,t){return s.constructor==t.constructor&&s.eq(t)}class Fd{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(As)!=t.state.facet(As)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){this.layer.updateOnDocViewUpdate!==!1&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(As);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some((e,i)=>!Nd(e,this.drawn[i]))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let n=e.nextSibling;e.remove(),e=n}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const As=D.define();function Kh(s){return[Z.define(t=>new Fd(t,s)),As.of(s)]}const Ri=D.define({combine(s){return Ht(s,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})}});function zd(s={}){return[Ri.of(s),Vd,Hd,Wd,vh.of(!0)]}function Uh(s){return s.startState.facet(Ri)!=s.state.facet(Ri)}const Vd=Kh({above:!0,markers(s){let{state:t}=s,e=t.facet(Ri),i=[];for(let n of t.selection.ranges){let r=n==t.selection.main;if(n.empty||e.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=n.empty?n:x.cursor(n.head,n.head>n.anchor?-1:1);for(let a of qi.forRange(s,o,l))i.push(a)}}return i},update(s,t){s.transactions.some(i=>i.selection)&&(t.style.animationName=t.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let e=Uh(s);return e&&kl(s.state,t),s.docChanged||s.selectionSet||e},mount(s,t){kl(t.state,s)},class:"cm-cursorLayer"});function kl(s,t){t.style.animationDuration=s.facet(Ri).cursorBlinkRate+"ms"}const Hd=Kh({above:!1,markers(s){return s.state.selection.ranges.map(t=>t.empty?[]:qi.forRange(s,"cm-selectionBackground",t)).reduce((t,e)=>t.concat(e))},update(s,t){return s.docChanged||s.selectionSet||s.viewportChanged||Uh(s)},class:"cm-selectionLayer"}),Wd=ze.highest(O.theme({".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"},caretColor:"transparent !important"},".cm-content":{caretColor:"transparent !important","& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}})),Jh=B.define({map(s,t){return s==null?null:t.mapPos(s)}}),yi=it.define({create(){return null},update(s,t){return s!=null&&(s=t.changes.mapPos(s)),t.effects.reduce((e,i)=>i.is(Jh)?i.value:e,s)}}),$d=Z.fromClass(class{constructor(s){this.view=s,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(s){var t;let e=s.state.field(yi);e==null?this.cursor!=null&&((t=this.cursor)===null||t===void 0||t.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(s.startState.field(yi)!=e||s.docChanged||s.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:s}=this,t=s.state.field(yi),e=t!=null&&s.coordsAtPos(t);if(!e)return null;let i=s.scrollDOM.getBoundingClientRect();return{left:e.left-i.left+s.scrollDOM.scrollLeft*s.scaleX,top:e.top-i.top+s.scrollDOM.scrollTop*s.scaleY,height:e.bottom-e.top}}drawCursor(s){if(this.cursor){let{scaleX:t,scaleY:e}=this.view;s?(this.cursor.style.left=s.left/t+"px",this.cursor.style.top=s.top/e+"px",this.cursor.style.height=s.height/e+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(s){this.view.state.field(yi)!=s&&this.view.dispatch({effects:Jh.of(s)})}},{eventObservers:{dragover(s){this.setDropPos(this.view.posAtCoords({x:s.clientX,y:s.clientY}))},dragleave(s){(s.target==this.view.contentDOM||!this.view.contentDOM.contains(s.relatedTarget))&&this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function qd(){return[yi,$d]}function Sl(s,t,e,i,n){t.lastIndex=0;for(let r=s.iterRange(e,i),o=e,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=t.exec(r.value);)n(o+l.index,l)}function jd(s,t){let e=s.visibleRanges;if(e.length==1&&e[0].from==s.viewport.from&&e[0].to==s.viewport.to)return e;let i=[];for(let{from:n,to:r}of e)n=Math.max(s.state.doc.lineAt(n).from,n-t),r=Math.min(s.state.doc.lineAt(r).to,r+t),i.length&&i[i.length-1].to>=n?i[i.length-1].to=r:i.push({from:n,to:r});return i}class _d{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:r,maxLength:o=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(l,a,h,c)=>n(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(t){let e=new le,i=e.add.bind(e);for(let{from:n,to:r}of jd(t,this.maxLength))Sl(t.state.doc,this.regexp,n,r,(o,l)=>this.addMatch(l,t,o,i));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges((r,o,l,a)=>{a>=t.view.viewport.from&&l<=t.view.viewport.to&&(i=Math.min(l,i),n=Math.max(a,n))}),t.viewportMoved||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let r of t.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,n);if(l>o){let a=t.state.doc.lineAt(o),h=a.to<l?t.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,p=(m,g,b)=>u.push(b.range(m,g));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,t,d.index+a.from,p);else Sl(t.state.doc,this.regexp,c,f,(m,g)=>this.addMatch(g,t,m,p));e=e.update({filterFrom:c,filterTo:f,filter:(m,g)=>m<c||g>f,add:u})}}return e}}const Or=/x/.unicode!=null?"gu":"g",Kd=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,Or),Ud={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Pn=null;function Jd(){var s;if(Pn==null&&typeof document<"u"&&document.body){let t=document.body.style;Pn=((s=t.tabSize)!==null&&s!==void 0?s:t.MozTabSize)!=null}return Pn||!1}const Ms=D.define({combine(s){let t=Ht(s,{render:null,specialChars:Kd,addSpecialChars:null});return(t.replaceTabs=!Jd())&&(t.specialChars=new RegExp("	|"+t.specialChars.source,Or)),t.addSpecialChars&&(t.specialChars=new RegExp(t.specialChars.source+"|"+t.addSpecialChars.source,Or)),t}});function Gd(s={}){return[Ms.of(s),Yd()]}let Cl=null;function Yd(){return Cl||(Cl=Z.fromClass(class{constructor(s){this.view=s,this.decorations=P.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(s.state.facet(Ms)),this.decorations=this.decorator.createDeco(s)}makeDecorator(s){return new _d({regexp:s.specialChars,decoration:(t,e,i)=>{let{doc:n}=e.state,r=bt(t[0],0);if(r==9){let o=n.lineAt(i),l=e.state.tabSize,a=ri(o.text,l,i-o.from);return P.replace({widget:new tp((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=P.replace({widget:new Zd(s,r)}))},boundary:s.replaceTabs?void 0:/[^]/})}update(s){let t=s.state.facet(Ms);s.startState.facet(Ms)!=t?(this.decorator=this.makeDecorator(t),this.decorations=this.decorator.createDeco(s.view)):this.decorations=this.decorator.updateDeco(s,this.decorations)}},{decorations:s=>s.decorations}))}const Qd="•";function Xd(s){return s>=32?Qd:s==10?"␤":String.fromCharCode(9216+s)}class Zd extends Se{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=Xd(this.code),i=t.state.phrase("Control character")+" "+(Ud[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let r=document.createElement("span");return r.textContent=e,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class tp extends Se{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}function ep(){return sp}const ip=P.line({class:"cm-activeLine"}),sp=Z.fromClass(class{constructor(s){this.decorations=this.getDeco(s)}update(s){(s.docChanged||s.selectionSet)&&(this.decorations=this.getDeco(s.view))}getDeco(s){let t=-1,e=[];for(let i of s.state.selection.ranges){let n=s.lineBlockAt(i.head);n.from>t&&(e.push(ip.range(n.from)),t=n.from)}return P.set(e)}},{decorations:s=>s.decorations}),Dr=2e3;function np(s,t,e){let i=Math.min(t.line,e.line),n=Math.max(t.line,e.line),r=[];if(t.off>Dr||e.off>Dr||t.col<0||e.col<0){let o=Math.min(t.off,e.off),l=Math.max(t.off,e.off);for(let a=i;a<=n;a++){let h=s.doc.line(a);h.length<=l&&r.push(x.range(h.from+o,h.to+l))}}else{let o=Math.min(t.col,e.col),l=Math.max(t.col,e.col);for(let a=i;a<=n;a++){let h=s.doc.line(a),c=or(h.text,o,s.tabSize,!0);if(c<0)r.push(x.cursor(h.to));else{let f=or(h.text,l,s.tabSize);r.push(x.range(h.from+c,h.from+f))}}}return r}function rp(s,t){let e=s.coordsAtPos(s.viewport.from);return e?Math.round(Math.abs((e.left-t)/s.defaultCharacterWidth)):-1}function Al(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1),i=s.state.doc.lineAt(e),n=e-i.from,r=n>Dr?-1:n==i.length?rp(s,t.clientX):ri(i.text,s.state.tabSize,e-i.from);return{line:i.number,col:r,off:n}}function op(s,t){let e=Al(s,t),i=s.state.selection;return e?{update(n){if(n.docChanged){let r=n.changes.mapPos(n.startState.doc.line(e.line).from),o=n.state.doc.lineAt(r);e={line:o.number,col:e.col,off:Math.min(e.off,o.length)},i=i.map(n.changes)}},get(n,r,o){let l=Al(s,n);if(!l)return i;let a=np(s.state,e,l);return a.length?o?x.create(a.concat(i.ranges)):x.create(a):i}}:null}function lp(s){let t=(s==null?void 0:s.eventFilter)||(e=>e.altKey&&e.button==0);return O.mouseSelectionStyle.of((e,i)=>t(i)?op(e,i):null)}const ap={Alt:[18,s=>!!s.altKey],Control:[17,s=>!!s.ctrlKey],Shift:[16,s=>!!s.shiftKey],Meta:[91,s=>!!s.metaKey]},hp={style:"cursor: crosshair"};function cp(s={}){let[t,e]=ap[s.key||"Alt"],i=Z.fromClass(class{constructor(n){this.view=n,this.isDown=!1}set(n){this.isDown!=n&&(this.isDown=n,this.view.update([]))}},{eventObservers:{keydown(n){this.set(n.keyCode==t||e(n))},keyup(n){(n.keyCode==t||!e(n))&&this.set(!1)},mousemove(n){this.set(e(n))}}});return[i,O.contentAttributes.of(n=>{var r;return!((r=n.plugin(i))===null||r===void 0)&&r.isDown?hp:null})]}const ci="-10000px";class Gh{constructor(t,e,i,n){this.facet=e,this.createTooltipView=i,this.removeTooltipView=n,this.input=t.state.facet(e),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r))}update(t,e){var i;let n=t.state.facet(this.facet),r=n.filter(a=>a);if(n===this.input){for(let a of this.tooltipViews)a.update&&a.update(t);return!1}let o=[],l=e?[]:null;for(let a=0;a<r.length;a++){let h=r[a],c=-1;if(h){for(let f=0;f<this.tooltips.length;f++){let u=this.tooltips[f];u&&u.create==h.create&&(c=f)}if(c<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else{let f=o[a]=this.tooltipViews[c];l&&(l[a]=e[c]),f.update&&f.update(t)}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return e&&(l.forEach((a,h)=>e[h]=a),e.length=l.length),this.input=n,this.tooltips=r,this.tooltipViews=o,!0}}function fp(s){let t=s.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:t.clientHeight,right:t.clientWidth}}const Rn=D.define({combine:s=>{var t,e,i;return{position:T.ios?"absolute":((t=s.find(n=>n.position))===null||t===void 0?void 0:t.position)||"fixed",parent:((e=s.find(n=>n.parent))===null||e===void 0?void 0:e.parent)||null,tooltipSpace:((i=s.find(n=>n.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||fp}}}),Ml=new WeakMap,oo=Z.fromClass(class{constructor(s){this.view=s,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let t=s.state.facet(Rn);this.position=t.position,this.parent=t.parent,this.classes=s.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new Gh(s,cn,(e,i)=>this.createTooltip(e,i),e=>{this.resizeObserver&&this.resizeObserver.unobserve(e.dom),e.dom.remove()}),this.above=this.manager.tooltips.map(e=>!!e.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(e=>{Date.now()>this.lastTransaction-50&&e.length>0&&e[e.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),s.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let s of this.manager.tooltipViews)this.intersectionObserver.observe(s.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(s){s.transactions.length&&(this.lastTransaction=Date.now());let t=this.manager.update(s,this.above);t&&this.observeIntersection();let e=t||s.geometryChanged,i=s.state.facet(Rn);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let n of this.manager.tooltipViews)n.dom.style.position=this.position;e=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let n of this.manager.tooltipViews)this.container.appendChild(n.dom);e=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);e&&this.maybeMeasure()}createTooltip(s,t){let e=s.create(this.view),i=t?t.dom:null;if(e.dom.classList.add("cm-tooltip"),s.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let n=document.createElement("div");n.className="cm-tooltip-arrow",e.dom.appendChild(n)}return e.dom.style.position=this.position,e.dom.style.top=ci,e.dom.style.left="0px",this.container.insertBefore(e.dom,i),e.mount&&e.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(e.dom),e}destroy(){var s,t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(s=i.destroy)===null||s===void 0||s.call(i);this.parent&&this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let s=1,t=1,e=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:r}=this.manager.tooltipViews[0];if(T.gecko)e=r.offsetParent!=this.container.ownerDocument.body;else if(r.style.top==ci&&r.style.left=="0px"){let o=r.getBoundingClientRect();e=Math.abs(o.top+1e4)>1||Math.abs(o.left)>1}}if(e||this.position=="absolute")if(this.parent){let r=this.parent.getBoundingClientRect();r.width&&r.height&&(s=r.width/this.parent.offsetWidth,t=r.height/this.parent.offsetHeight)}else({scaleX:s,scaleY:t}=this.view.viewState);let i=this.view.scrollDOM.getBoundingClientRect(),n=eo(this.view);return{visible:{left:i.left+n.left,top:i.top+n.top,right:i.right-n.right,bottom:i.bottom-n.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((r,o)=>{let l=this.manager.tooltipViews[o];return l.getCoords?l.getCoords(r.pos):this.view.coordsAtPos(r.pos)}),size:this.manager.tooltipViews.map(({dom:r})=>r.getBoundingClientRect()),space:this.view.state.facet(Rn).tooltipSpace(this.view),scaleX:s,scaleY:t,makeAbsolute:e}}writeMeasure(s){var t;if(s.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute"}let{visible:e,space:i,scaleX:n,scaleY:r}=s,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:c}=h,f=s.pos[l],u=s.size[l];if(!f||a.clip!==!1&&(f.bottom<=Math.max(e.top,i.top)||f.top>=Math.min(e.bottom,i.bottom)||f.right<Math.max(e.left,i.left)-.1||f.left>Math.min(e.right,i.right)+.1)){c.style.top=ci;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,p=d?7:0,m=u.right-u.left,g=(t=Ml.get(h))!==null&&t!==void 0?t:u.bottom-u.top,b=h.offset||dp,w=this.view.textDirection==J.LTR,k=u.width>i.right-i.left?w?i.left:i.right-u.width:w?Math.max(i.left,Math.min(f.left-(d?14:0)+b.x,i.right-m)):Math.min(Math.max(i.left,f.left-m+(d?14:0)-b.x),i.right-m),S=this.above[l];!a.strictSide&&(S?f.top-g-p-b.y<i.top:f.bottom+g+p+b.y>i.bottom)&&S==i.bottom-f.bottom>f.top-i.top&&(S=this.above[l]=!S);let v=(S?f.top-i.top:i.bottom-f.bottom)-p;if(v<g&&h.resize!==!1){if(v<this.view.defaultLineHeight){c.style.top=ci;continue}Ml.set(h,g),c.style.height=(g=v)/r+"px"}else c.style.height&&(c.style.height="");let C=S?f.top-g-p-b.y:f.bottom+p+b.y,M=k+m;if(h.overlap!==!0)for(let E of o)E.left<M&&E.right>k&&E.top<C+g&&E.bottom>C&&(C=S?E.top-g-2-p:E.bottom+p+2);if(this.position=="absolute"?(c.style.top=(C-s.parent.top)/r+"px",Ol(c,(k-s.parent.left)/n)):(c.style.top=C/r+"px",Ol(c,k/n)),d){let E=f.left+(w?b.x:-b.x)-(k+14-7);d.style.left=E/n+"px"}h.overlap!==!0&&o.push({left:k,top:C,right:M,bottom:C+g}),c.classList.toggle("cm-tooltip-above",S),c.classList.toggle("cm-tooltip-below",!S),h.positioned&&h.positioned(s.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let s of this.manager.tooltipViews)s.dom.style.top=ci}},{eventObservers:{scroll(){this.maybeMeasure()}}});function Ol(s,t){let e=parseInt(s.style.left,10);(isNaN(e)||Math.abs(t-e)>1)&&(s.style.left=t+"px")}const up=O.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),dp={x:0,y:0},cn=D.define({enables:[oo,up]}),Vs=D.define({combine:s=>s.reduce((t,e)=>t.concat(e),[])});class fn{static create(t){return new fn(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new Gh(t,Vs,(e,i)=>this.createHostedView(e,i),e=>e.dom.remove())}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)(t=e.destroy)===null||t===void 0||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let n=i[t];if(n!==void 0){if(e===void 0)e=n;else if(e!==n)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const pp=cn.compute([Vs],s=>{let t=s.facet(Vs);return t.length===0?null:{pos:Math.min(...t.map(e=>e.pos)),end:Math.max(...t.map(e=>{var i;return(i=e.end)!==null&&i!==void 0?i:e.pos})),create:fn.create,above:t[0].above,arrow:t.some(e=>e.arrow)}});class mp{constructor(t,e,i,n,r){this.view=t,this.source=e,this.field=i,this.setHover=n,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let n,r=1;if(i instanceof pe)n=i.posAtStart;else{if(n=t.posAtCoords(e),n==null)return;let l=t.coordsAtPos(n);if(!l||e.y<l.top||e.y>l.bottom||e.x<l.left-t.defaultCharacterWidth||e.x>l.right+t.defaultCharacterWidth)return;let a=t.bidiSpans(t.state.doc.lineAt(n)).find(c=>c.from<=n&&c.to>=n),h=a&&a.dir==J.RTL?-1:1;r=e.x<l.left?-h:h}let o=this.source(t,n,r);if(o!=null&&o.then){let l=this.pending={pos:n};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}))},a=>wt(t.state,a,"hover tooltip"))}else o&&!(Array.isArray(o)&&!o.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])})}get tooltip(){let t=this.view.plugin(oo),e=t?t.manager.tooltips.findIndex(i=>i.create==fn.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:n,tooltip:r}=this;if(n.length&&r&&!gp(r.dom,t)||this.pending){let{pos:o}=n[0]||this.pending,l=(i=(e=n[0])===null||e===void 0?void 0:e.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!bp(this.view,o,l,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:i}=this;i&&i.dom.contains(t.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const ls=4;function gp(s,t){let{left:e,right:i,top:n,bottom:r}=s.getBoundingClientRect(),o;if(o=s.querySelector(".cm-tooltip-arrow")){let l=o.getBoundingClientRect();n=Math.min(l.top,n),r=Math.max(l.bottom,r)}return t.clientX>=e-ls&&t.clientX<=i+ls&&t.clientY>=n-ls&&t.clientY<=r+ls}function bp(s,t,e,i,n,r){let o=s.scrollDOM.getBoundingClientRect(),l=s.documentTop+s.documentPadding.top+s.contentHeight;if(o.left>i||o.right<i||o.top>n||Math.min(o.bottom,l)<n)return!1;let a=s.posAtCoords({x:i,y:n},!1);return a>=t&&a<=e}function yp(s,t={}){let e=B.define(),i=it.define({create(){return[]},update(n,r){if(n.length&&(t.hideOnChange&&(r.docChanged||r.selection)?n=[]:t.hideOn&&(n=n.filter(o=>!t.hideOn(r,o))),r.docChanged)){let o=[];for(let l of n){let a=r.changes.mapPos(l.pos,-1,pt.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h)}}n=o}for(let o of r.effects)o.is(e)&&(n=o.value),o.is(xp)&&(n=[]);return n},provide:n=>Vs.from(n)});return{active:i,extension:[i,Z.define(n=>new mp(n,s,i,e,t.hoverTime||300)),pp]}}function Yh(s,t){let e=s.plugin(oo);if(!e)return null;let i=e.manager.tooltips.indexOf(t);return i<0?null:e.manager.tooltipViews[i]}const xp=B.define(),Dl=D.define({combine(s){let t,e;for(let i of s)t=t||i.topContainer,e=e||i.bottomContainer;return{topContainer:t,bottomContainer:e}}});function Bi(s,t){let e=s.plugin(Qh),i=e?e.specs.indexOf(t):-1;return i>-1?e.panels[i]:null}const Qh=Z.fromClass(class{constructor(s){this.input=s.state.facet(Ei),this.specs=this.input.filter(e=>e),this.panels=this.specs.map(e=>e(s));let t=s.state.facet(Dl);this.top=new as(s,!0,t.topContainer),this.bottom=new as(s,!1,t.bottomContainer),this.top.sync(this.panels.filter(e=>e.top)),this.bottom.sync(this.panels.filter(e=>!e.top));for(let e of this.panels)e.dom.classList.add("cm-panel"),e.mount&&e.mount()}update(s){let t=s.state.facet(Dl);this.top.container!=t.topContainer&&(this.top.sync([]),this.top=new as(s.view,!0,t.topContainer)),this.bottom.container!=t.bottomContainer&&(this.bottom.sync([]),this.bottom=new as(s.view,!1,t.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let e=s.state.facet(Ei);if(e!=this.input){let i=e.filter(a=>a),n=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(s.view),l.push(c)):(c=this.panels[h],c.update&&c.update(s)),n.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=n,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(s)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:s=>O.scrollMargins.of(t=>{let e=t.plugin(s);return e&&{top:e.top.scrollMargin(),bottom:e.bottom.scrollMargin()}})});class as{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let e=this.container||this.view.dom;e.insertBefore(this.dom,this.top?e.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=Tl(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=Tl(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function Tl(s){let t=s.nextSibling;return s.remove(),t}const Ei=D.define({enables:Qh});class ee extends Ee{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}ee.prototype.elementClass="";ee.prototype.toDOM=void 0;ee.prototype.mapMode=pt.TrackBefore;ee.prototype.startSide=ee.prototype.endSide=-1;ee.prototype.point=!0;const Os=D.define(),wp=D.define(),vp={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>F.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Si=D.define();function Xh(s){return[Zh(),Si.of(Object.assign(Object.assign({},vp),s))]}const Tr=D.define({combine:s=>s.some(t=>t)});function Zh(s){let t=[kp];return s&&s.fixed===!1&&t.push(Tr.of(!0)),t}const kp=Z.fromClass(class{constructor(s){this.view=s,this.prevViewport=s.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=s.state.facet(Si).map(t=>new Rl(s,t));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!s.state.facet(Tr),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),s.scrollDOM.insertBefore(this.dom,s.contentDOM)}update(s){if(this.updateGutters(s)){let t=this.prevViewport,e=s.view.viewport,i=Math.min(t.to,e.to)-Math.max(t.from,e.from);this.syncGutters(i<(e.to-e.from)*.8)}s.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(Tr)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=s.view.viewport}syncGutters(s){let t=this.dom.nextSibling;s&&this.dom.remove();let e=F.iter(this.view.state.facet(Os),this.view.viewport.from),i=[],n=this.gutters.map(r=>new Sp(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==mt.Text&&o){Pr(e,i,l.from);for(let a of n)a.line(this.view,l,i);o=!1}else if(l.widget)for(let a of n)a.widget(this.view,l)}else if(r.type==mt.Text){Pr(e,i,r.from);for(let o of n)o.line(this.view,r,i)}else if(r.widget)for(let o of n)o.widget(this.view,r);for(let r of n)r.finish();s&&this.view.scrollDOM.insertBefore(this.dom,t)}updateGutters(s){let t=s.startState.facet(Si),e=s.state.facet(Si),i=s.docChanged||s.heightChanged||s.viewportChanged||!F.eq(s.startState.facet(Os),s.state.facet(Os),s.view.viewport.from,s.view.viewport.to);if(t==e)for(let n of this.gutters)n.update(s)&&(i=!0);else{i=!0;let n=[];for(let r of e){let o=t.indexOf(r);o<0?n.push(new Rl(this.view,r)):(this.gutters[o].update(s),n.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),n.indexOf(r)<0&&r.destroy();for(let r of n)this.dom.appendChild(r.dom);this.gutters=n}return i}destroy(){for(let s of this.gutters)s.destroy();this.dom.remove()}},{provide:s=>O.scrollMargins.of(t=>{let e=t.plugin(s);return!e||e.gutters.length==0||!e.fixed?null:t.textDirection==J.LTR?{left:e.dom.offsetWidth*t.scaleX}:{right:e.dom.offsetWidth*t.scaleX}})});function Pl(s){return Array.isArray(s)?s:[s]}function Pr(s,t,e){for(;s.value&&s.from<=e;)s.from==e&&t.push(s.value),s.next()}class Sp{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=F.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,r=(e.top-this.height)/t.scaleY,o=e.height/t.scaleY;if(this.i==n.elements.length){let l=new tc(t,o,r,i);n.elements.push(l),n.dom.appendChild(l.dom)}else n.elements[this.i].update(t,o,r,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Pr(this.cursor,n,e.from),i.length&&(n=n.concat(i));let r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let o=this.gutter;n.length==0&&!o.config.renderEmptyElements||this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),n=i?[i]:null;for(let r of t.state.facet(wp)){let o=r(t,e.widget,e);o&&(n||(n=[])).push(o)}n&&this.addElement(t,e,n)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Rl{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,n=>{let r=n.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2}else o=n.clientY;let l=t.lineBlockAtHeight(o-t.documentTop);e.domEventHandlers[i](t,l,n)&&n.preventDefault()});this.markers=Pl(e.markers(t)),e.initialSpacer&&(this.spacer=new tc(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Pl(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let n=this.config.updateSpacer(this.spacer.markers[0],t);n!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[n])}let i=t.view.viewport;return!F.eq(this.markers,e,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(t):!1)}destroy(){for(let t of this.elements)t.destroy()}}class tc{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),Cp(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<e.length?e[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(n);let f=n.nextSibling;n.remove(),n=f}}if(!a)break;a.toDOM&&(h?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),h&&o++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}function Cp(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(!s[e].compare(t[e]))return!1;return!0}const Ap=D.define(),Mp=D.define(),qe=D.define({combine(s){return Ht(s,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let n in e){let r=i[n],o=e[n];i[n]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class Bn extends ee{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function En(s,t){return s.state.facet(qe).formatNumber(t,s.state)}const Op=Si.compute([qe],s=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(t){return t.state.facet(Ap)},lineMarker(t,e,i){return i.some(n=>n.toDOM)?null:new Bn(En(t,t.state.doc.lineAt(e.from).number))},widgetMarker:(t,e,i)=>{for(let n of t.state.facet(Mp)){let r=n(t,e,i);if(r)return r}return null},lineMarkerChange:t=>t.startState.facet(qe)!=t.state.facet(qe),initialSpacer(t){return new Bn(En(t,Bl(t.state.doc.lines)))},updateSpacer(t,e){let i=En(e.view,Bl(e.view.state.doc.lines));return i==t.number?t:new Bn(i)},domEventHandlers:s.facet(qe).domEventHandlers}));function Dp(s={}){return[qe.of(s),Zh(),Op]}function Bl(s){let t=9;for(;t<s;)t=t*10+9;return t}const Tp=new class extends ee{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},Pp=Os.compute(["selection"],s=>{let t=[],e=-1;for(let i of s.selection.ranges){let n=s.doc.lineAt(i.head).from;n>e&&(e=n,t.push(Tp.range(n)))}return F.of(t)});function Rp(){return Pp}const ec=1024;let Bp=0;class Ln{constructor(t,e){this.from=t,this.to=e}}class L{constructor(t={}){this.id=Bp++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof t!="function"&&(t=kt.match(t)),e=>{let i=t(e);return i===void 0?null:[this,i]}}}L.closedBy=new L({deserialize:s=>s.split(" ")});L.openedBy=new L({deserialize:s=>s.split(" ")});L.group=new L({deserialize:s=>s.split(" ")});L.isolate=new L({deserialize:s=>{if(s&&s!="rtl"&&s!="ltr"&&s!="auto")throw new RangeError("Invalid value for isolate: "+s);return s||"auto"}});L.contextHash=new L({perNode:!0});L.lookAhead=new L({perNode:!0});L.mounted=new L({perNode:!0});class Hs{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[L.mounted.id]}}const Ep=Object.create(null);class kt{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):Ep,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(t.name==null?8:0),n=new kt(t.name||"",e,t.id,i);if(t.props){for(let r of t.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[r[0].id]=r[1]}}return n}prop(t){return this.props[t.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(t){if(typeof t=="string"){if(this.name==t)return!0;let e=this.prop(L.group);return e?e.indexOf(t)>-1:!1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return i=>{for(let n=i.prop(L.group),r=-1;r<(n?n.length:0);r++){let o=e[r<0?i.name:n[r]];if(o)return o}}}}kt.none=new kt("",Object.create(null),0,8);class lo{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let r of t){let o=r(i);o&&(n||(n=Object.assign({},i.props)),n[o[0].id]=o[1])}e.push(n?new kt(i.name,n,i.id,i.flags):i)}return new lo(e)}}const hs=new WeakMap,El=new WeakMap;var st;(function(s){s[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays"})(st||(st={}));class X{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let t=Hs.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let i of this.children){let n=i.toString();n&&(e&&(e+=","),e+=n)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new Br(this.topNode,t)}cursorAt(t,e=0,i=0){let n=hs.get(this)||this.topNode,r=new Br(n);return r.moveTo(t,e),hs.set(this,r._tree),r}get topNode(){return new Lt(this,0,0,null)}resolve(t,e=0){let i=Li(hs.get(this)||this.topNode,t,e,!1);return hs.set(this,i),i}resolveInner(t,e=0){let i=Li(El.get(this)||this.topNode,t,e,!0);return El.set(this,i),i}resolveStack(t,e=0){return Np(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t,o=t.mode||0,l=(o&st.IncludeAnonymous)>0;for(let a=this.cursor(o|st.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=n&&(!l&&a.type.isAnonymous||e(a)!==!1)){if(a.firstChild())continue;h=!0}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:co(kt.none,this.children,this.positions,0,this.children.length,0,this.length,(e,i,n)=>new X(this.type,e,i,n,this.propValues),t.makeTree||((e,i,n)=>new X(kt.none,e,i,n)))}static build(t){return Fp(t)}}X.empty=new X(kt.none,[],[],0);class ao{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new ao(this.buffer,this.index)}}class ve{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return kt.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),t+=4,i==t)return r;let o=[];for(;t<i;)o.push(this.childString(t)),t=this.buffer[t+3];return r+"("+o.join(",")+")"}findChild(t,e,i,n,r){let{buffer:o}=this,l=-1;for(let a=t;a!=e&&!(ic(r,n,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(t,e,i){let n=this.buffer,r=new Uint16Array(e-t),o=0;for(let l=t,a=0;l<e;){r[a++]=n[l++],r[a++]=n[l++]-i;let h=r[a++]=n[l++]-i;r[a++]=n[l++]-t,o=Math.max(o,h)}return new ve(r,o,this.set)}}function ic(s,t,e,i){switch(s){case-2:return e<t;case-1:return i>=t&&e<t;case 0:return e<t&&i>t;case 1:return e<=t&&i>t;case 2:return i>t;case 4:return!0}}function Li(s,t,e,i){for(var n;s.from==s.to||(e<1?s.from>=t:s.from>t)||(e>-1?s.to<=t:s.to<t);){let o=!i&&s instanceof Lt&&s.index<0?null:s.parent;if(!o)return s;s=o}let r=i?0:st.IgnoreOverlays;if(i)for(let o=s,l=o.parent;l;o=l,l=o.parent)o instanceof Lt&&o.index<0&&((n=l.enter(t,e,r))===null||n===void 0?void 0:n.from)!=o.from&&(s=l);for(;;){let o=s.enter(t,e,r);if(!o)return s;s=o}}class sc{cursor(t=0){return new Br(this,t)}getChild(t,e=null,i=null){let n=Ll(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return Ll(this,t,e,i)}resolve(t,e=0){return Li(this,t,e,!1)}resolveInner(t,e=0){return Li(this,t,e,!0)}matchContext(t){return Rr(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let n=e.lastChild;if(!n||n.to!=e.to)break;n.type.isError&&n.from==n.to?(i=e,e=n.prevSibling):e=n}return i}get node(){return this}get next(){return this.parent}}class Lt extends sc{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=e>0?l.length:-1;t!=h;t+=e){let c=l[t],f=a[t]+o.from;if(ic(n,i,f,f+c.length)){if(c instanceof ve){if(r&st.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,e,i-f,n);if(u>-1)return new ge(new Lp(o,c,t,f),null,u)}else if(r&st.IncludeAnonymous||!c.type.isAnonymous||ho(c)){let u;if(!(r&st.IgnoreMounts)&&(u=Hs.get(c))&&!u.overlay)return new Lt(u.tree,f,t,o);let d=new Lt(c,f,t,o);return r&st.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(e<0?c.children.length-1:0,e,i,n)}}}if(r&st.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?t=o.index+e:t=e<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&st.IgnoreOverlays)&&(n=Hs.get(this._tree))&&n.overlay){let r=t-this.from;for(let{from:o,to:l}of n.overlay)if((e>0?o<=r:o<r)&&(e<0?l>=r:l>r))return new Lt(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Ll(s,t,e,i){let n=s.cursor(),r=[];if(!n.firstChild())return r;if(e!=null){for(let o=!1;!o;)if(o=n.type.is(e),!n.nextSibling())return r}for(;;){if(i!=null&&n.type.is(i))return r;if(n.type.is(t)&&r.push(n.node),!n.nextSibling())return i==null?r:[]}}function Rr(s,t,e=t.length-1){for(let i=s;e>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(t[e]&&t[e]!=i.name)return!1;e--}}return!0}class Lp{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class ge extends sc{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new ge(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&st.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return r<0?null:new ge(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new ge(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new ge(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let o=i.buffer[this.index+1];t.push(i.slice(n,r,o)),e.push(0)}return new X(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function nc(s){if(!s.length)return null;let t=0,e=s[0];for(let r=1;r<s.length;r++){let o=s[r];(o.from>e.from||o.to<e.to)&&(e=o,t=r)}let i=e instanceof Lt&&e.index<0?null:e.parent,n=s.slice();return i?n[t]=i:n.splice(t,1),new Ip(n,e)}class Ip{constructor(t,e){this.heads=t,this.node=e}get next(){return nc(this.heads)}}function Np(s,t,e){let i=s.resolveInner(t,e),n=null;for(let r=i instanceof Lt?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(n||(n=[i])).push(o.resolve(t,e)),r=o}else{let o=Hs.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=t&&o.overlay[o.overlay.length-1].to>=t){let l=new Lt(o.tree,o.overlay[0].from+r.from,-1,r);(n||(n=[i])).push(Li(l,t,e,!1))}}return n?nc(n):i}class Br{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof Lt)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let i=t._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return t?(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0):!1}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return t?t instanceof Lt?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?i&st.ExcludeBuffers?!1:this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&st.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&st.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode)):!1;let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let n=i<0?0:this.stack[i]+4;if(this.index!=n)return this.yieldBuf(e.findChild(n,this.index,-1,0,4))}else{let n=e.buffer[this.index+3];if(n<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(n)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(n.buffer.buffer[r+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;{index:e,_parent:i}=i)if(e>-1)for(let r=e+t,o=t<0?-1:i._tree.children.length;r!=o;r+=t){let l=i._tree.children[r];if(this.mode&st.IncludeAnonymous||l instanceof ve||!l.type.isAnonymous||ho(l))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let o=t;o;o=o._parent)if(o.index==n){if(n==this.index)return o;e=o,i=r+1;break t}n=this.stack[--r]}for(let n=i;n<this.stack.length;n++)e=new ge(this.buffer,e,this.stack[n]);return this.bufferNode=new ge(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||t(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return Rr(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return Rr(this._tree,t,n);let o=i[e.buffer[this.stack[r]]];if(!o.isAnonymous){if(t[n]&&t[n]!=o.name)return!1;n--}}return!0}}function ho(s){return s.children.some(t=>t instanceof ve||!t.type.isAnonymous||ho(t))}function Fp(s){var t;let{buffer:e,nodeSet:i,maxBufferLength:n=ec,reused:r=[],minRepeatType:o=i.types.length}=s,l=Array.isArray(e)?new ao(e,e.length):e,a=i.types,h=0,c=0;function f(v,C,M,E,N,$){let{id:I,start:R,end:W,size:H}=l,K=c,dt=h;for(;H<0;)if(l.next(),H==-1){let se=r[I];M.push(se),E.push(R-v);return}else if(H==-3){h=I;return}else if(H==-4){c=I;return}else throw new RangeError(`Unrecognized record size: ${H}`);let St=a[I],$t,rt,Dt=R-v;if(W-R<=n&&(rt=g(l.pos-C,N))){let se=new Uint16Array(rt.size-rt.skip),Tt=l.pos-rt.size,qt=se.length;for(;l.pos>Tt;)qt=b(rt.start,se,qt);$t=new ve(se,W-rt.start,i),Dt=rt.start-v}else{let se=l.pos-H;l.next();let Tt=[],qt=[],Ae=I>=o?I:-1,Ve=0,Ji=W;for(;l.pos>se;)Ae>=0&&l.id==Ae&&l.size>=0?(l.end<=Ji-n&&(p(Tt,qt,R,Ve,l.end,Ji,Ae,K,dt),Ve=Tt.length,Ji=l.end),l.next()):$>2500?u(R,se,Tt,qt):f(R,se,Tt,qt,Ae,$+1);if(Ae>=0&&Ve>0&&Ve<Tt.length&&p(Tt,qt,R,Ve,R,Ji,Ae,K,dt),Tt.reverse(),qt.reverse(),Ae>-1&&Ve>0){let Do=d(St,dt);$t=co(St,Tt,qt,0,Tt.length,0,W-R,Do,Do)}else $t=m(St,Tt,qt,W-R,K-W,dt)}M.push($t),E.push(Dt)}function u(v,C,M,E){let N=[],$=0,I=-1;for(;l.pos>C;){let{id:R,start:W,end:H,size:K}=l;if(K>4)l.next();else{if(I>-1&&W<I)break;I<0&&(I=H-n),N.push(R,W,H),$++,l.next()}}if($){let R=new Uint16Array($*4),W=N[N.length-2];for(let H=N.length-3,K=0;H>=0;H-=3)R[K++]=N[H],R[K++]=N[H+1]-W,R[K++]=N[H+2]-W,R[K++]=K;M.push(new ve(R,N[2]-W,i)),E.push(W-v)}}function d(v,C){return(M,E,N)=>{let $=0,I=M.length-1,R,W;if(I>=0&&(R=M[I])instanceof X){if(!I&&R.type==v&&R.length==N)return R;(W=R.prop(L.lookAhead))&&($=E[I]+R.length+W)}return m(v,M,E,N,$,C)}}function p(v,C,M,E,N,$,I,R,W){let H=[],K=[];for(;v.length>E;)H.push(v.pop()),K.push(C.pop()+M-N);v.push(m(i.types[I],H,K,$-N,R-$,W)),C.push(N-M)}function m(v,C,M,E,N,$,I){if($){let R=[L.contextHash,$];I=I?[R].concat(I):[R]}if(N>25){let R=[L.lookAhead,N];I=I?[R].concat(I):[R]}return new X(v,C,M,E,I)}function g(v,C){let M=l.fork(),E=0,N=0,$=0,I=M.end-n,R={size:0,start:0,skip:0};t:for(let W=M.pos-v;M.pos>W;){let H=M.size;if(M.id==C&&H>=0){R.size=E,R.start=N,R.skip=$,$+=4,E+=4,M.next();continue}let K=M.pos-H;if(H<0||K<W||M.start<I)break;let dt=M.id>=o?4:0,St=M.start;for(M.next();M.pos>K;){if(M.size<0)if(M.size==-3)dt+=4;else break t;else M.id>=o&&(dt+=4);M.next()}N=St,E+=H,$+=dt}return(C<0||E==v)&&(R.size=E,R.start=N,R.skip=$),R.size>4?R:void 0}function b(v,C,M){let{id:E,start:N,end:$,size:I}=l;if(l.next(),I>=0&&E<o){let R=M;if(I>4){let W=l.pos-(I-4);for(;l.pos>W;)M=b(v,C,M)}C[--M]=R,C[--M]=$-v,C[--M]=N-v,C[--M]=E}else I==-3?h=E:I==-4&&(c=E);return M}let w=[],k=[];for(;l.pos>0;)f(s.start||0,s.bufferStart||0,w,k,-1,0);let S=(t=s.length)!==null&&t!==void 0?t:w.length?k[0]+w[0].length:0;return new X(a[s.topID],w.reverse(),k.reverse(),S)}const Il=new WeakMap;function Ds(s,t){if(!s.isAnonymous||t instanceof ve||t.type!=s)return 1;let e=Il.get(t);if(e==null){e=1;for(let i of t.children){if(i.type!=s||!(i instanceof X)){e=1;break}e+=Ds(s,i)}Il.set(t,e)}return e}function co(s,t,e,i,n,r,o,l,a){let h=0;for(let p=i;p<n;p++)h+=Ds(s,t[p]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(p,m,g,b,w){for(let k=g;k<b;){let S=k,v=m[k],C=Ds(s,p[k]);for(k++;k<b;k++){let M=Ds(s,p[k]);if(C+M>=c)break;C+=M}if(k==S+1){if(C>c){let M=p[S];d(M.children,M.positions,0,M.children.length,m[S]+w);continue}f.push(p[S])}else{let M=m[k-1]+p[k-1].length-v;f.push(co(s,p,m,S,k,v,M,null,a))}u.push(v+w-r)}}return d(t,e,i,n,0),(l||a)(f,u,o)}class Re{constructor(t,e,i,n,r=!1,o=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(t,e=[],i=!1){let n=[new Re(0,t.length,t,0,!1,i)];for(let r of e)r.to>t.length&&n.push(r);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,o=t.length?t[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<e.length?e[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,f)-h;u=d>=p?null:new Re(d,p,u.tree,u.offset+h,l>0,!!c)}if(u&&n.push(u),o.to>f)break;o=r<t.length?t[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return n}}class rc{startParse(t,e,i){return typeof t=="string"&&(t=new zp(t)),i=i?i.length?i.map(n=>new Ln(n.from,n.to)):[new Ln(0,0)]:[new Ln(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let r=n.advance();if(r)return r}}}class zp{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}new L({perNode:!0});let Vp=0;class Pt{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=Vp++}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i=typeof t=="string"?t:"?";if(t instanceof Pt&&(e=t),e!=null&&e.base)throw new Error("Can not derive from a modified tag");let n=new Pt(i,[],null,[]);if(n.set.push(n),e)for(let r of e.set)n.set.push(r);return n}static defineModifier(t){let e=new Ws(t);return i=>i.modified.indexOf(e)>-1?i:Ws.get(i.base||i,i.modified.concat(e).sort((n,r)=>n.id-r.id))}}let Hp=0;class Ws{constructor(t){this.name=t,this.instances=[],this.id=Hp++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find(l=>l.base==t&&Wp(e,l.modified));if(i)return i;let n=[],r=new Pt(t.name,n,t,e);for(let l of e)l.instances.push(r);let o=$p(e);for(let l of t.set)if(!l.modified.length)for(let a of o)n.push(Ws.get(l,a));return r}}function Wp(s,t){return s.length==t.length&&s.every((e,i)=>e==t[i])}function $p(s){let t=[[]];for(let e=0;e<s.length;e++)for(let i=0,n=t.length;i<n;i++)t.push(t[i].concat(s[e]));return t.sort((e,i)=>i.length-e.length)}function oc(s){let t=Object.create(null);for(let e in s){let i=s[e];Array.isArray(i)||(i=[i]);for(let n of e.split(" "))if(n){let r=[],o=2,l=n;for(let f=0;;){if(l=="..."&&f>0&&f+3==n.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+n);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==n.length)break;let d=n[f++];if(f==n.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+n);l=n.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+n);let c=new $s(i,o,a>0?r.slice(0,a):null);t[h]=c.sort(t[h])}}return lc.add(t)}const lc=new L;class $s{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}$s.empty=new $s([],2,null);function ac(s,t){let e=Object.create(null);for(let r of s)if(!Array.isArray(r.tag))e[r.tag.id]=r.class;else for(let o of r.tag)e[o.id]=r.class;let{scope:i,all:n=null}=t||{};return{style:r=>{let o=n;for(let l of r)for(let a of l.set){let h=e[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function qp(s,t){let e=null;for(let i of s){let n=i.style(t);n&&(e=e?e+" "+n:n)}return e}function jp(s,t,e,i=0,n=s.length){let r=new _p(i,Array.isArray(t)?t:[t],e);r.highlightRange(s.cursor(),i,n,"",r.highlighters),r.flush(n)}class _p{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,r){let{type:o,from:l,to:a}=t;if(l>=i||a<=e)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=n,c=Kp(t)||$s.empty,f=qp(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(n+=(n?" ":"")+f)),this.startSpan(Math.max(e,l),h),c.opaque)return;let u=t.tree&&t.tree.prop(L.mounted);if(u&&u.overlay){let d=t.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),m=t.firstChild();for(let g=0,b=l;;g++){let w=g<u.overlay.length?u.overlay[g]:null,k=w?w.from+l:a,S=Math.max(e,b),v=Math.min(i,k);if(S<v&&m)for(;t.from<v&&(this.highlightRange(t,S,v,n,r),this.startSpan(Math.min(v,t.to),h),!(t.to>=k||!t.nextSibling())););if(!w||k>i)break;b=w.to+l,b>e&&(this.highlightRange(d.cursor(),Math.max(e,w.from+l),Math.min(i,b),"",p),this.startSpan(Math.min(i,b),h))}m&&t.parent()}else if(t.firstChild()){u&&(n="");do if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r),this.startSpan(Math.min(i,t.to),h)}while(t.nextSibling());t.parent()}}}function Kp(s){let t=s.type.prop(lc);for(;t&&t.context&&!s.matchContext(t.context);)t=t.next;return t||null}const A=Pt.define,cs=A(),ce=A(),Nl=A(ce),Fl=A(ce),fe=A(),fs=A(fe),In=A(fe),Ut=A(),Me=A(Ut),_t=A(),Kt=A(),Er=A(),fi=A(Er),us=A(),y={comment:cs,lineComment:A(cs),blockComment:A(cs),docComment:A(cs),name:ce,variableName:A(ce),typeName:Nl,tagName:A(Nl),propertyName:Fl,attributeName:A(Fl),className:A(ce),labelName:A(ce),namespace:A(ce),macroName:A(ce),literal:fe,string:fs,docString:A(fs),character:A(fs),attributeValue:A(fs),number:In,integer:A(In),float:A(In),bool:A(fe),regexp:A(fe),escape:A(fe),color:A(fe),url:A(fe),keyword:_t,self:A(_t),null:A(_t),atom:A(_t),unit:A(_t),modifier:A(_t),operatorKeyword:A(_t),controlKeyword:A(_t),definitionKeyword:A(_t),moduleKeyword:A(_t),operator:Kt,derefOperator:A(Kt),arithmeticOperator:A(Kt),logicOperator:A(Kt),bitwiseOperator:A(Kt),compareOperator:A(Kt),updateOperator:A(Kt),definitionOperator:A(Kt),typeOperator:A(Kt),controlOperator:A(Kt),punctuation:Er,separator:A(Er),bracket:fi,angleBracket:A(fi),squareBracket:A(fi),paren:A(fi),brace:A(fi),content:Ut,heading:Me,heading1:A(Me),heading2:A(Me),heading3:A(Me),heading4:A(Me),heading5:A(Me),heading6:A(Me),contentSeparator:A(Ut),list:A(Ut),quote:A(Ut),emphasis:A(Ut),strong:A(Ut),link:A(Ut),monospace:A(Ut),strikethrough:A(Ut),inserted:A(),deleted:A(),changed:A(),invalid:A(),meta:us,documentMeta:A(us),annotation:A(us),processingInstruction:A(us),definition:Pt.defineModifier("definition"),constant:Pt.defineModifier("constant"),function:Pt.defineModifier("function"),standard:Pt.defineModifier("standard"),local:Pt.defineModifier("local"),special:Pt.defineModifier("special")};for(let s in y){let t=y[s];t instanceof Pt&&(t.name=s)}ac([{tag:y.link,class:"tok-link"},{tag:y.heading,class:"tok-heading"},{tag:y.emphasis,class:"tok-emphasis"},{tag:y.strong,class:"tok-strong"},{tag:y.keyword,class:"tok-keyword"},{tag:y.atom,class:"tok-atom"},{tag:y.bool,class:"tok-bool"},{tag:y.url,class:"tok-url"},{tag:y.labelName,class:"tok-labelName"},{tag:y.inserted,class:"tok-inserted"},{tag:y.deleted,class:"tok-deleted"},{tag:y.literal,class:"tok-literal"},{tag:y.string,class:"tok-string"},{tag:y.number,class:"tok-number"},{tag:[y.regexp,y.escape,y.special(y.string)],class:"tok-string2"},{tag:y.variableName,class:"tok-variableName"},{tag:y.local(y.variableName),class:"tok-variableName tok-local"},{tag:y.definition(y.variableName),class:"tok-variableName tok-definition"},{tag:y.special(y.variableName),class:"tok-variableName2"},{tag:y.definition(y.propertyName),class:"tok-propertyName tok-definition"},{tag:y.typeName,class:"tok-typeName"},{tag:y.namespace,class:"tok-namespace"},{tag:y.className,class:"tok-className"},{tag:y.macroName,class:"tok-macroName"},{tag:y.propertyName,class:"tok-propertyName"},{tag:y.operator,class:"tok-operator"},{tag:y.comment,class:"tok-comment"},{tag:y.meta,class:"tok-meta"},{tag:y.invalid,class:"tok-invalid"},{tag:y.punctuation,class:"tok-punctuation"}]);var Nn;const je=new L;function Up(s){return D.define({combine:s?t=>t.concat(s):void 0})}const Jp=new L;class Ft{constructor(t,e,i=[],n=""){this.data=t,this.name=n,V.prototype.hasOwnProperty("tree")||Object.defineProperty(V.prototype,"tree",{get(){return ft(this)}}),this.parser=e,this.extension=[ke.of(this),V.languageData.of((r,o,l)=>{let a=zl(r,o,l),h=a.type.prop(je);if(!h)return[];let c=r.facet(h),f=a.type.prop(Jp);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(c)}}return c})].concat(i)}isActiveAt(t,e,i=-1){return zl(t,e,i).type.prop(je)==this.data}findRegions(t){let e=t.facet(ke);if((e==null?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(r,o)=>{if(r.prop(je)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(L.mounted);if(l){if(l.tree.prop(je)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(n(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof X&&n(h,r.positions[a]+o)}};return n(ft(t),0),i}get allowsNesting(){return!0}}Ft.setState=B.define();function zl(s,t,e){let i=s.facet(ke),n=ft(s).topNode;if(!i||i.allowsNesting)for(let r=n;r;r=r.enter(t,e,st.ExcludeBuffers))r.type.isTop&&(n=r);return n}class qs extends Ft{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=Up(t.languageData);return new qs(e,t.parser.configure({props:[je.add(i=>i.isTop?e:void 0)]}),t.name)}configure(t,e){return new qs(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function ft(s){let t=s.field(Ft.state,!1);return t?t.tree:X.empty}class Gp{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let ui=null;class js{constructor(t,e,i=[],n,r,o,l,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new js(t,e,[],X.empty,0,i,[],null)}startParse(){return this.parser.startParse(new Gp(this.state.doc),this.fragments)}work(t,e){return e!=null&&e>=this.state.doc.length&&(e=void 0),this.tree!=X.empty&&this.isDone(e??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof t=="number"){let n=Date.now()+t;t=()=>Date.now()>n}for(this.parse||(this.parse=this.startParse()),e!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n)if(this.fragments=this.withoutTempSkipped(Re.addTree(n,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=n,this.parse=null,this.treeLen<(e??this.state.doc.length))this.parse=this.startParse();else return!0;if(t())return!1}})}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(e=this.parse.advance()););}),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(Re.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=ui;ui=this;try{return t()}finally{ui=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=Vl(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!t.empty){let a=[];if(t.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=Re.applyChanges(i,a),n=X.empty,r=0,o={from:t.mapPos(o.from,-1),to:t.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=t.mapPos(h.from,1),f=t.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new js(this.parser,e,i,n,r,o,l,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:n,to:r}=this.skipped[i];n<t.to&&r>t.from&&(this.fragments=Vl(this.fragments,n,r),this.skipped.splice(i--,1))}return this.skipped.length>=e?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends rc{createParse(e,i,n){let r=n[0].from,o=n[n.length-1].to;return{parsedPos:r,advance(){let a=ui;if(a){for(let h of n)a.tempSkipped.push(h);t&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,t]):t)}return this.parsedPos=o,new X(kt.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&e[0].from==0&&e[0].to>=t}static get(){return ui}}function Vl(s,t,e){return Re.applyChanges(s,[{fromA:t,toA:e,fromB:t,toB:e}])}class ii{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new ii(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=js.create(t.facet(ke).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new ii(i)}}Ft.state=it.define({create:ii.init,update(s,t){for(let e of t.effects)if(e.is(Ft.setState))return e.value;return t.startState.facet(ke)!=t.state.facet(ke)?ii.init(t.state):s.apply(t)}});let hc=s=>{let t=setTimeout(()=>s(),500);return()=>clearTimeout(t)};typeof requestIdleCallback<"u"&&(hc=s=>{let t=-1,e=setTimeout(()=>{t=requestIdleCallback(s,{timeout:500-100})},100);return()=>t<0?clearTimeout(e):cancelIdleCallback(t)});const Fn=typeof navigator<"u"&&(!((Nn=navigator.scheduling)===null||Nn===void 0)&&Nn.isInputPending)?()=>navigator.scheduling.isInputPending():null,Yp=Z.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(Ft.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(Ft.state);(e.tree!=e.context.tree||!e.context.isDone(t.doc.length))&&(this.working=hc(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(Ft.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,t&&!Fn?Math.max(25,t.timeRemaining()-5):1e9),l=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(()=>Fn&&Fn()||Date.now()>o,n+(l?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:Ft.setState.of(new ii(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(e=>wt(this.view.state,e)).then(()=>this.workScheduled--),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),ke=D.define({combine(s){return s.length?s[0]:null},enables:s=>[Ft.state,Yp,O.contentAttributes.compute([s],t=>{let e=t.facet(s);return e&&e.name?{"data-language":e.name}:{}})]});class Qp{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}const Xp=D.define(),un=D.define({combine:s=>{if(!s.length)return"  ";let t=s[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(s[0]));return t}});function _s(s){let t=s.facet(un);return t.charCodeAt(0)==9?s.tabSize*t.length:t.length}function Ii(s,t){let e="",i=s.tabSize,n=s.facet(un)[0];if(n=="	"){for(;t>=i;)e+="	",t-=i;n=" "}for(let r=0;r<t;r++)e+=n;return e}function fo(s,t){s instanceof V&&(s=new dn(s));for(let i of s.state.facet(Xp)){let n=i(s,t);if(n!==void 0)return n}let e=ft(s.state);return e.length>=t?Zp(s,e,t):null}class dn{constructor(t,e={}){this.state=t,this.options=e,this.unit=_s(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return n!=null&&n>=i.from&&n<=i.to?r&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),o=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return ri(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let o=r(n);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const cc=new L;function Zp(s,t,e){let i=t.resolveStack(e),n=t.resolveInner(e,-1).resolve(e,0).enterUnfinishedNodesBefore(e);if(n!=i.node){let r=[];for(let o=n;o&&!(o.from==i.node.from&&o.type==i.node.type);o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i}}return fc(i,s,e)}function fc(s,t,e){for(let i=s;i;i=i.next){let n=em(i.node);if(n)return n(uo.create(t,e,i))}return 0}function tm(s){return s.pos==s.options.simulateBreak&&s.options.simulateDoubleBreak}function em(s){let t=s.type.prop(cc);if(t)return t;let e=s.firstChild,i;if(e&&(i=e.type.prop(L.closedBy))){let n=s.lastChild,r=n&&i.indexOf(n.name)>-1;return o=>rm(o,!0,1,void 0,r&&!tm(o)?n.from:void 0)}return s.parent==null?im:null}function im(){return 0}class uo extends dn{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new uo(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(sm(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return fc(this.context.next,this.base,this.pos)}}function sm(s,t){for(let e=t;e;e=e.parent)if(s==e)return!0;return!1}function nm(s){let t=s.node,e=t.childAfter(t.from),i=t.lastChild;if(!e)return null;let n=s.options.simulateBreak,r=s.state.doc.lineAt(e.from),o=n==null||n<=r.from?r.to:Math.min(r.to,n);for(let l=e.to;;){let a=t.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped){if(a.from>=o)return null;let h=/^ */.exec(r.text.slice(e.to-r.from))[0].length;return{from:e.from,to:e.to+h}}l=a.to}}function rm(s,t,e,i,n){let r=s.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||n==s.pos+o,a=t?nm(s):null;return a?l?s.column(a.from):s.column(a.to):s.baseIndent+(l?0:s.unit*e)}function Hl({except:s,units:t=1}={}){return e=>{let i=s&&s.test(e.textAfter);return e.baseIndent+(i?0:t*e.unit)}}const om=200;function lm(){return V.transactionFilter.of(s=>{if(!s.docChanged||!s.isUserEvent("input.type")&&!s.isUserEvent("input.complete"))return s;let t=s.startState.languageDataAt("indentOnInput",s.startState.selection.main.head);if(!t.length)return s;let e=s.newDoc,{head:i}=s.newSelection.main,n=e.lineAt(i);if(i>n.from+om)return s;let r=e.sliceString(n.from,i);if(!t.some(h=>h.test(r)))return s;let{state:o}=s,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=fo(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=Ii(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[s,{changes:a,sequential:!0}]:s})}const am=D.define(),uc=new L;function hm(s){let t=s.firstChild,e=s.lastChild;return t&&t.to<e.from?{from:t.to,to:e.type.isError?s.to:e.from}:null}function cm(s,t,e){let i=ft(s);if(i.length<e)return null;let n=i.resolveStack(e,1),r=null;for(let o=n;o;o=o.next){let l=o.node;if(l.to<=e||l.from>e)continue;if(r&&l.from<t)break;let a=l.type.prop(uc);if(a&&(l.to<i.length-50||i.length==s.doc.length||!fm(l))){let h=a(l,s);h&&h.from<=e&&h.from>=t&&h.to>e&&(r=h)}}return r}function fm(s){let t=s.lastChild;return t&&t.to==s.to&&t.type.isError}function Ks(s,t,e){for(let i of s.facet(am)){let n=i(s,t,e);if(n)return n}return cm(s,t,e)}function dc(s,t){let e=t.mapPos(s.from,1),i=t.mapPos(s.to,-1);return e>=i?void 0:{from:e,to:i}}const pn=B.define({map:dc}),ji=B.define({map:dc});function pc(s){let t=[];for(let{head:e}of s.state.selection.ranges)t.some(i=>i.from<=e&&i.to>=e)||t.push(s.lineBlockAt(e));return t}const Fe=it.define({create(){return P.none},update(s,t){s=s.map(t.changes);for(let e of t.effects)if(e.is(pn)&&!um(s,e.value.from,e.value.to)){let{preparePlaceholder:i}=t.state.facet(po),n=i?P.replace({widget:new xm(i(t.state,e.value))}):Wl;s=s.update({add:[n.range(e.value.from,e.value.to)]})}else e.is(ji)&&(s=s.update({filter:(i,n)=>e.value.from!=i||e.value.to!=n,filterFrom:e.value.from,filterTo:e.value.to}));if(t.selection){let e=!1,{head:i}=t.selection.main;s.between(i,i,(n,r)=>{n<i&&r>i&&(e=!0)}),e&&(s=s.update({filterFrom:i,filterTo:i,filter:(n,r)=>r<=i||n>=i}))}return s},provide:s=>O.decorations.from(s),toJSON(s,t){let e=[];return s.between(0,t.doc.length,(i,n)=>{e.push(i,n)}),e},fromJSON(s){if(!Array.isArray(s)||s.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let e=0;e<s.length;){let i=s[e++],n=s[e++];if(typeof i!="number"||typeof n!="number")throw new RangeError("Invalid JSON for fold state");t.push(Wl.range(i,n))}return P.set(t,!0)}});function Us(s,t,e){var i;let n=null;return(i=s.field(Fe,!1))===null||i===void 0||i.between(t,e,(r,o)=>{(!n||n.from>r)&&(n={from:r,to:o})}),n}function um(s,t,e){let i=!1;return s.between(t,t,(n,r)=>{n==t&&r==e&&(i=!0)}),i}function mc(s,t){return s.field(Fe,!1)?t:t.concat(B.appendConfig.of(bc()))}const dm=s=>{for(let t of pc(s)){let e=Ks(s.state,t.from,t.to);if(e)return s.dispatch({effects:mc(s.state,[pn.of(e),gc(s,e)])}),!0}return!1},pm=s=>{if(!s.state.field(Fe,!1))return!1;let t=[];for(let e of pc(s)){let i=Us(s.state,e.from,e.to);i&&t.push(ji.of(i),gc(s,i,!1))}return t.length&&s.dispatch({effects:t}),t.length>0};function gc(s,t,e=!0){let i=s.state.doc.lineAt(t.from).number,n=s.state.doc.lineAt(t.to).number;return O.announce.of(`${s.state.phrase(e?"Folded lines":"Unfolded lines")} ${i} ${s.state.phrase("to")} ${n}.`)}const mm=s=>{let{state:t}=s,e=[];for(let i=0;i<t.doc.length;){let n=s.lineBlockAt(i),r=Ks(t,n.from,n.to);r&&e.push(pn.of(r)),i=(r?s.lineBlockAt(r.to):n).to+1}return e.length&&s.dispatch({effects:mc(s.state,e)}),!!e.length},gm=s=>{let t=s.state.field(Fe,!1);if(!t||!t.size)return!1;let e=[];return t.between(0,s.state.doc.length,(i,n)=>{e.push(ji.of({from:i,to:n}))}),s.dispatch({effects:e}),!0},bm=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:dm},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:pm},{key:"Ctrl-Alt-[",run:mm},{key:"Ctrl-Alt-]",run:gm}],ym={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},po=D.define({combine(s){return Ht(s,ym)}});function bc(s){let t=[Fe,km];return s&&t.push(po.of(s)),t}function yc(s,t){let{state:e}=s,i=e.facet(po),n=o=>{let l=s.lineBlockAt(s.posAtDOM(o.target)),a=Us(s.state,l.from,l.to);a&&s.dispatch({effects:ji.of(a)}),o.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(s,n,t);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",e.phrase("folded code")),r.title=e.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}const Wl=P.replace({widget:new class extends Se{toDOM(s){return yc(s,null)}}});class xm extends Se{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return yc(t,this.value)}}const wm={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class zn extends ee{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function vm(s={}){let t=Object.assign(Object.assign({},wm),s),e=new zn(t,!0),i=new zn(t,!1),n=Z.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(ke)!=o.state.facet(ke)||o.startState.field(Fe,!1)!=o.state.field(Fe,!1)||ft(o.startState)!=ft(o.state)||t.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new le;for(let a of o.viewportLineBlocks){let h=Us(o.state,a.from,a.to)?i:Ks(o.state,a.from,a.to)?e:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=t;return[n,Xh({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(n))===null||l===void 0?void 0:l.markers)||F.empty},initialSpacer(){return new zn(t,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=Us(o.state,l.from,l.to);if(h)return o.dispatch({effects:ji.of(h)}),!0;let c=Ks(o.state,l.from,l.to);return c?(o.dispatch({effects:pn.of(c)}),!0):!1}})}),bc()]}const km=O.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class _i{constructor(t,e){this.specs=t;let i;function n(l){let a=ye.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof e.all=="string"?e.all:e.all?n(e.all):void 0,o=e.scope;this.scope=o instanceof Ft?l=>l.prop(je)==o.data:o?l=>l==o:void 0,this.style=ac(t.map(l=>({tag:l.tag,class:l.class||n(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new ye(i):null,this.themeType=e.themeType}static define(t,e){return new _i(t,e||{})}}const Lr=D.define(),xc=D.define({combine(s){return s.length?[s[0]]:null}});function Vn(s){let t=s.facet(Lr);return t.length?t:s.facet(xc)}function wc(s,t){let e=[Cm],i;return s instanceof _i&&(s.module&&e.push(O.styleModule.of(s.module)),i=s.themeType),t!=null&&t.fallback?e.push(xc.of(s)):i?e.push(Lr.computeN([O.darkTheme],n=>n.facet(O.darkTheme)==(i=="dark")?[s]:[])):e.push(Lr.of(s)),e}class Sm{constructor(t){this.markCache=Object.create(null),this.tree=ft(t.state),this.decorations=this.buildDeco(t,Vn(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=ft(t.state),i=Vn(t.state),n=i!=Vn(t.startState),{viewport:r}=t.view,o=t.changes.mapPos(this.decoratedTo,1);e.length<r.to&&!n&&e.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=o):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=r.to)}buildDeco(t,e){if(!e||!this.tree.length)return P.none;let i=new le;for(let{from:n,to:r}of t.visibleRanges)jp(this.tree,e,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=P.mark({class:a})))},n,r);return i.finish()}}const Cm=ze.high(Z.fromClass(Sm,{decorations:s=>s.decorations})),Am=_i.define([{tag:y.meta,color:"#404740"},{tag:y.link,textDecoration:"underline"},{tag:y.heading,textDecoration:"underline",fontWeight:"bold"},{tag:y.emphasis,fontStyle:"italic"},{tag:y.strong,fontWeight:"bold"},{tag:y.strikethrough,textDecoration:"line-through"},{tag:y.keyword,color:"#708"},{tag:[y.atom,y.bool,y.url,y.contentSeparator,y.labelName],color:"#219"},{tag:[y.literal,y.inserted],color:"#164"},{tag:[y.string,y.deleted],color:"#a11"},{tag:[y.regexp,y.escape,y.special(y.string)],color:"#e40"},{tag:y.definition(y.variableName),color:"#00f"},{tag:y.local(y.variableName),color:"#30a"},{tag:[y.typeName,y.namespace],color:"#085"},{tag:y.className,color:"#167"},{tag:[y.special(y.variableName),y.macroName],color:"#256"},{tag:y.definition(y.propertyName),color:"#00c"},{tag:y.comment,color:"#940"},{tag:y.invalid,color:"#f00"}]),Mm=O.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),vc=1e4,kc="()[]{}",Sc=D.define({combine(s){return Ht(s,{afterCursor:!0,brackets:kc,maxScanDistance:vc,renderMatch:Tm})}}),Om=P.mark({class:"cm-matchingBracket"}),Dm=P.mark({class:"cm-nonmatchingBracket"});function Tm(s){let t=[],e=s.matched?Om:Dm;return t.push(e.range(s.start.from,s.start.to)),s.end&&t.push(e.range(s.end.from,s.end.to)),t}const Pm=it.define({create(){return P.none},update(s,t){if(!t.docChanged&&!t.selection)return s;let e=[],i=t.state.facet(Sc);for(let n of t.state.selection.ranges){if(!n.empty)continue;let r=Qt(t.state,n.head,-1,i)||n.head>0&&Qt(t.state,n.head-1,1,i)||i.afterCursor&&(Qt(t.state,n.head,1,i)||n.head<t.state.doc.length&&Qt(t.state,n.head+1,-1,i));r&&(e=e.concat(i.renderMatch(r,t.state)))}return P.set(e,!0)},provide:s=>O.decorations.from(s)}),Rm=[Pm,Mm];function Bm(s={}){return[Sc.of(s),Rm]}const Em=new L;function Ir(s,t,e){let i=s.prop(t<0?L.openedBy:L.closedBy);if(i)return i;if(s.name.length==1){let n=e.indexOf(s.name);if(n>-1&&n%2==(t<0?1:0))return[e[n+t]]}return null}function Nr(s){let t=s.type.prop(Em);return t?t(s.node):s}function Qt(s,t,e,i={}){let n=i.maxScanDistance||vc,r=i.brackets||kc,o=ft(s),l=o.resolveInner(t,e);for(let a=l;a;a=a.parent){let h=Ir(a.type,e,r);if(h&&a.from<a.to){let c=Nr(a);if(c&&(e>0?t>=c.from&&t<c.to:t>c.from&&t<=c.to))return Lm(s,t,e,a,c,h,r)}}return Im(s,t,e,o,l.type,n,r)}function Lm(s,t,e,i,n,r,o){let l=i.parent,a={from:n.from,to:n.to},h=0,c=l==null?void 0:l.cursor();if(c&&(e<0?c.childBefore(i.from):c.childAfter(i.to)))do if(e<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Nr(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Ir(c.type,e,o))h++;else if(Ir(c.type,-e,o)){if(h==0){let f=Nr(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(e<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function Im(s,t,e,i,n,r,o){let l=e<0?s.sliceDoc(t-1,t):s.sliceDoc(t,t+1),a=o.indexOf(l);if(a<0||a%2==0!=e>0)return null;let h={from:e<0?t-1:t,to:e>0?t+1:t},c=s.doc.iterRange(t,e>0?s.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;e<0&&(u+=d.length);let p=t+u*e;for(let m=e>0?0:d.length-1,g=e>0?d.length:-1;m!=g;m+=e){let b=o.indexOf(d[m]);if(!(b<0||i.resolveInner(p+m,1).type!=n))if(b%2==0==e>0)f++;else{if(f==1)return{start:h,end:{from:p+m,to:p+m+1},matched:b>>1==a>>1};f--}}e>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}const Nm=Object.create(null),$l=[kt.none],ql=[],jl=Object.create(null),Fm=Object.create(null);for(let[s,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Fm[s]=zm(Nm,t);function Hn(s,t){ql.indexOf(s)>-1||(ql.push(s),console.warn(t))}function zm(s,t){let e=[];for(let l of t.split(" ")){let a=[];for(let h of l.split(".")){let c=s[h]||y[h];c?typeof c=="function"?a.length?a=a.map(c):Hn(h,`Modifier ${h} used at start of tag`):a.length?Hn(h,`Tag ${h} used as modifier`):a=Array.isArray(c)?c:[c]:Hn(h,`Unknown highlighting tag ${h}`)}for(let h of a)e.push(h)}if(!e.length)return 0;let i=t.replace(/ /g,"_"),n=i+" "+e.map(l=>l.id),r=jl[n];if(r)return r.id;let o=jl[n]=kt.define({id:$l.length,name:i,props:[oc({[i]:e})]});return $l.push(o),o.id}J.RTL,J.LTR;const Vm=s=>{let{state:t}=s,e=t.doc.lineAt(t.selection.main.from),i=go(s.state,e.from);return i.line?Hm(s):i.block?$m(s):!1};function mo(s,t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let n=s(t,e);return n?(i(e.update(n)),!0):!1}}const Hm=mo(_m,0),Wm=mo(Cc,0),$m=mo((s,t)=>Cc(s,t,jm(t)),0);function go(s,t){let e=s.languageDataAt("commentTokens",t,1);return e.length?e[0]:{}}const di=50;function qm(s,{open:t,close:e},i,n){let r=s.sliceDoc(i-di,i),o=s.sliceDoc(n,n+di),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-t.length,h)==t&&o.slice(a,a+e.length)==e)return{open:{pos:i-l,margin:l&&1},close:{pos:n+a,margin:a&&1}};let c,f;n-i<=2*di?c=f=s.sliceDoc(i,n):(c=s.sliceDoc(i,i+di),f=s.sliceDoc(n-di,n));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,p=f.length-d-e.length;return c.slice(u,u+t.length)==t&&f.slice(p,p+e.length)==e?{open:{pos:i+u+t.length,margin:/\s/.test(c.charAt(u+t.length))?1:0},close:{pos:n-d-e.length,margin:/\s/.test(f.charAt(p-1))?1:0}}:null}function jm(s){let t=[];for(let e of s.selection.ranges){let i=s.doc.lineAt(e.from),n=e.to<=i.to?i:s.doc.lineAt(e.to);n.from>i.from&&n.from==e.to&&(n=e.to==i.to+1?i:s.doc.lineAt(e.to-1));let r=t.length-1;r>=0&&t[r].to>i.from?t[r].to=n.to:t.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:n.to})}return t}function Cc(s,t,e=t.selection.ranges){let i=e.map(r=>go(t,r.from).block);if(!i.every(r=>r))return null;let n=e.map((r,o)=>qm(t,i[o],r.from,r.to));if(s!=2&&!n.every(r=>r))return{changes:t.changes(e.map((r,o)=>n[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(s!=1&&n.some(r=>r)){let r=[];for(let o=0,l;o<n.length;o++)if(l=n[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function _m(s,t,e=t.selection.ranges){let i=[],n=-1;for(let{from:r,to:o}of e){let l=i.length,a=1e9,h=go(t,r).line;if(h){for(let c=r;c<=o;){let f=t.doc.lineAt(c);if(f.from>n&&(r==o||o>f.from)){n=f.from;let u=/^\s*/.exec(f.text)[0].length,d=u==f.length,p=f.text.slice(u,u+h.length)==h?u:-1;u<f.text.length&&u<a&&(a=u),i.push({line:f,comment:p,token:h,indent:u,empty:d,single:!1})}c=f.to+1}if(a<1e9)for(let c=l;c<i.length;c++)i[c].indent<i[c].line.text.length&&(i[c].indent=a);i.length==l+1&&(i[l].single=!0)}}if(s!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=t.changes(r);return{changes:o,selection:t.selection.map(o,1)}}else if(s!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const Fr=he.define(),Km=he.define(),Um=D.define(),Ac=D.define({combine(s){return Ht(s,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}}),Mc=it.define({create(){return Xt.empty},update(s,t){let e=t.state.facet(Ac),i=t.annotation(Fr);if(i){let a=vt.fromTransaction(t,i.selection),h=i.side,c=h==0?s.undone:s.done;return a?c=Js(c,c.length,e.minDepth,a):c=Tc(c,t.startState.selection),new Xt(h==0?i.rest:c,h==0?c:i.rest)}let n=t.annotation(Km);if((n=="full"||n=="before")&&(s=s.isolate()),t.annotation(et.addToHistory)===!1)return t.changes.empty?s:s.addMapping(t.changes.desc);let r=vt.fromTransaction(t),o=t.annotation(et.time),l=t.annotation(et.userEvent);return r?s=s.addChanges(r,o,l,e,t):t.selection&&(s=s.addSelection(t.startState.selection,o,l,e.newGroupDelay)),(n=="full"||n=="after")&&(s=s.isolate()),s},toJSON(s){return{done:s.done.map(t=>t.toJSON()),undone:s.undone.map(t=>t.toJSON())}},fromJSON(s){return new Xt(s.done.map(vt.fromJSON),s.undone.map(vt.fromJSON))}});function Jm(s={}){return[Mc,Ac.of(s),O.domEventHandlers({beforeinput(t,e){let i=t.inputType=="historyUndo"?Oc:t.inputType=="historyRedo"?zr:null;return i?(t.preventDefault(),i(e)):!1}})]}function mn(s,t){return function({state:e,dispatch:i}){if(!t&&e.readOnly)return!1;let n=e.field(Mc,!1);if(!n)return!1;let r=n.pop(s,e,t);return r?(i(r),!0):!1}}const Oc=mn(0,!1),zr=mn(1,!1),Gm=mn(0,!0),Ym=mn(1,!0);class vt{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(t){return new vt(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:(t=this.changes)===null||t===void 0?void 0:t.toJSON(),mapped:(e=this.mapped)===null||e===void 0?void 0:e.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(n=>n.toJSON())}}static fromJSON(t){return new vt(t.changes&&tt.fromJSON(t.changes),[],t.mapped&&Zt.fromJSON(t.mapped),t.startSelection&&x.fromJSON(t.startSelection),t.selectionsAfter.map(x.fromJSON))}static fromTransaction(t,e){let i=Rt;for(let n of t.startState.facet(Um)){let r=n(t);r.length&&(i=i.concat(r))}return!i.length&&t.changes.empty?null:new vt(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Rt)}static selection(t){return new vt(void 0,Rt,void 0,void 0,t)}}function Js(s,t,e,i){let n=t+1>e+20?t-e-1:0,r=s.slice(n,t);return r.push(i),r}function Qm(s,t){let e=[],i=!1;return s.iterChangedRanges((n,r)=>e.push(n,r)),t.iterChangedRanges((n,r,o,l)=>{for(let a=0;a<e.length;){let h=e[a++],c=e[a++];l>=h&&o<=c&&(i=!0)}}),i}function Xm(s,t){return s.ranges.length==t.ranges.length&&s.ranges.filter((e,i)=>e.empty!=t.ranges[i].empty).length===0}function Dc(s,t){return s.length?t.length?s.concat(t):s:t}const Rt=[],Zm=200;function Tc(s,t){if(s.length){let e=s[s.length-1],i=e.selectionsAfter.slice(Math.max(0,e.selectionsAfter.length-Zm));return i.length&&i[i.length-1].eq(t)?s:(i.push(t),Js(s,s.length-1,1e9,e.setSelAfter(i)))}else return[vt.selection([t])]}function tg(s){let t=s[s.length-1],e=s.slice();return e[s.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),e}function Wn(s,t){if(!s.length)return s;let e=s.length,i=Rt;for(;e;){let n=eg(s[e-1],t,i);if(n.changes&&!n.changes.empty||n.effects.length){let r=s.slice(0,e);return r[e-1]=n,r}else t=n.mapped,e--,i=n.selectionsAfter}return i.length?[vt.selection(i)]:Rt}function eg(s,t,e){let i=Dc(s.selectionsAfter.length?s.selectionsAfter.map(l=>l.map(t)):Rt,e);if(!s.changes)return vt.selection(i);let n=s.changes.map(t),r=t.mapDesc(s.changes,!0),o=s.mapped?s.mapped.composeDesc(r):r;return new vt(n,B.mapEffects(s.effects,t),o,s.startSelection.map(r),i)}const ig=/^(input\.type|delete)($|\.)/;class Xt{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Xt(this.done,this.undone):this}addChanges(t,e,i,n,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&t.changes&&(!i||ig.test(i))&&(!l.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(r,Qm(l.changes,t.changes))||i=="input.type.compose")?o=Js(o,o.length-1,n.minDepth,new vt(t.changes.compose(l.changes),Dc(B.mapEffects(t.effects,l.changes),l.effects),l.mapped,l.startSelection,Rt)):o=Js(o,o.length,n.minDepth,t),new Xt(o,Rt,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Rt;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&Xm(r[r.length-1],t)?this:new Xt(Tc(this.done,t),this.undone,e,i)}addMapping(t){return new Xt(Wn(this.done,t),Wn(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=t==0?this.done:this.undone;if(n.length==0)return null;let r=n[n.length-1],o=r.selectionsAfter[0]||e.selection;if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Fr.of({side:t,rest:tg(n),selection:o}),userEvent:t==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=n.length==1?Rt:n.slice(0,n.length-1);return r.mapped&&(l=Wn(l,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Fr.of({side:t,rest:l,selection:o}),filter:!1,userEvent:t==0?"undo":"redo",scrollIntoView:!0})}else return null}}Xt.empty=new Xt(Rt,Rt);const sg=[{key:"Mod-z",run:Oc,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:zr,preventDefault:!0},{linux:"Ctrl-Shift-z",run:zr,preventDefault:!0},{key:"Mod-u",run:Gm,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:Ym,preventDefault:!0}];function oi(s,t){return x.create(s.ranges.map(t),s.mainIndex)}function ie(s,t){return s.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function Wt({state:s,dispatch:t},e){let i=oi(s.selection,e);return i.eq(s.selection,!0)?!1:(t(ie(s,i)),!0)}function gn(s,t){return x.cursor(t?s.to:s.from)}function Pc(s,t){return Wt(s,e=>e.empty?s.moveByChar(e,t):gn(e,t))}function ut(s){return s.textDirectionAt(s.state.selection.main.head)==J.LTR}const Rc=s=>Pc(s,!ut(s)),Bc=s=>Pc(s,ut(s));function Ec(s,t){return Wt(s,e=>e.empty?s.moveByGroup(e,t):gn(e,t))}const ng=s=>Ec(s,!ut(s)),rg=s=>Ec(s,ut(s));function og(s,t,e){if(t.type.prop(e))return!0;let i=t.to-t.from;return i&&(i>2||/[^\s,.;:]/.test(s.sliceDoc(t.from,t.to)))||t.firstChild}function bn(s,t,e){let i=ft(s).resolveInner(t.head),n=e?L.closedBy:L.openedBy;for(let a=t.head;;){let h=e?i.childAfter(a):i.childBefore(a);if(!h)break;og(s,h,n)?i=h:a=e?h.to:h.from}let r=i.type.prop(n),o,l;return r&&(o=e?Qt(s,i.from,1):Qt(s,i.to,-1))&&o.matched?l=e?o.end.to:o.end.from:l=e?i.to:i.from,x.cursor(l,e?-1:1)}const lg=s=>Wt(s,t=>bn(s.state,t,!ut(s))),ag=s=>Wt(s,t=>bn(s.state,t,ut(s)));function Lc(s,t){return Wt(s,e=>{if(!e.empty)return gn(e,t);let i=s.moveVertically(e,t);return i.head!=e.head?i:s.moveToLineBoundary(e,t)})}const Ic=s=>Lc(s,!1),Nc=s=>Lc(s,!0);function Fc(s){let t=s.scrollDOM.clientHeight<s.scrollDOM.scrollHeight-2,e=0,i=0,n;if(t){for(let r of s.state.facet(O.scrollMargins)){let o=r(s);o!=null&&o.top&&(e=Math.max(o==null?void 0:o.top,e)),o!=null&&o.bottom&&(i=Math.max(o==null?void 0:o.bottom,i))}n=s.scrollDOM.clientHeight-e-i}else n=(s.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:e,marginBottom:i,selfScroll:t,height:Math.max(s.defaultLineHeight,n-5)}}function zc(s,t){let e=Fc(s),{state:i}=s,n=oi(i.selection,o=>o.empty?s.moveVertically(o,t,e.height):gn(o,t));if(n.eq(i.selection))return!1;let r;if(e.selfScroll){let o=s.coordsAtPos(i.selection.main.head),l=s.scrollDOM.getBoundingClientRect(),a=l.top+e.marginTop,h=l.bottom-e.marginBottom;o&&o.top>a&&o.bottom<h&&(r=O.scrollIntoView(n.main.head,{y:"start",yMargin:o.top-a}))}return s.dispatch(ie(i,n),{effects:r}),!0}const _l=s=>zc(s,!1),Vr=s=>zc(s,!0);function Ce(s,t,e){let i=s.lineBlockAt(t.head),n=s.moveToLineBoundary(t,e);if(n.head==t.head&&n.head!=(e?i.to:i.from)&&(n=s.moveToLineBoundary(t,e,!1)),!e&&n.head==i.from&&i.length){let r=/^\s*/.exec(s.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&t.head!=i.from+r&&(n=x.cursor(i.from+r))}return n}const hg=s=>Wt(s,t=>Ce(s,t,!0)),cg=s=>Wt(s,t=>Ce(s,t,!1)),fg=s=>Wt(s,t=>Ce(s,t,!ut(s))),ug=s=>Wt(s,t=>Ce(s,t,ut(s))),dg=s=>Wt(s,t=>x.cursor(s.lineBlockAt(t.head).from,1)),pg=s=>Wt(s,t=>x.cursor(s.lineBlockAt(t.head).to,-1));function mg(s,t,e){let i=!1,n=oi(s.selection,r=>{let o=Qt(s,r.head,-1)||Qt(s,r.head,1)||r.head>0&&Qt(s,r.head-1,1)||r.head<s.doc.length&&Qt(s,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return e?x.range(r.anchor,l):x.cursor(l)});return i?(t(ie(s,n)),!0):!1}const gg=({state:s,dispatch:t})=>mg(s,t,!1);function Nt(s,t){let e=oi(s.state.selection,i=>{let n=t(i);return x.range(i.anchor,n.head,n.goalColumn,n.bidiLevel||void 0)});return e.eq(s.state.selection)?!1:(s.dispatch(ie(s.state,e)),!0)}function Vc(s,t){return Nt(s,e=>s.moveByChar(e,t))}const Hc=s=>Vc(s,!ut(s)),Wc=s=>Vc(s,ut(s));function $c(s,t){return Nt(s,e=>s.moveByGroup(e,t))}const bg=s=>$c(s,!ut(s)),yg=s=>$c(s,ut(s)),xg=s=>Nt(s,t=>bn(s.state,t,!ut(s))),wg=s=>Nt(s,t=>bn(s.state,t,ut(s)));function qc(s,t){return Nt(s,e=>s.moveVertically(e,t))}const jc=s=>qc(s,!1),_c=s=>qc(s,!0);function Kc(s,t){return Nt(s,e=>s.moveVertically(e,t,Fc(s).height))}const Kl=s=>Kc(s,!1),Ul=s=>Kc(s,!0),vg=s=>Nt(s,t=>Ce(s,t,!0)),kg=s=>Nt(s,t=>Ce(s,t,!1)),Sg=s=>Nt(s,t=>Ce(s,t,!ut(s))),Cg=s=>Nt(s,t=>Ce(s,t,ut(s))),Ag=s=>Nt(s,t=>x.cursor(s.lineBlockAt(t.head).from)),Mg=s=>Nt(s,t=>x.cursor(s.lineBlockAt(t.head).to)),Jl=({state:s,dispatch:t})=>(t(ie(s,{anchor:0})),!0),Gl=({state:s,dispatch:t})=>(t(ie(s,{anchor:s.doc.length})),!0),Yl=({state:s,dispatch:t})=>(t(ie(s,{anchor:s.selection.main.anchor,head:0})),!0),Ql=({state:s,dispatch:t})=>(t(ie(s,{anchor:s.selection.main.anchor,head:s.doc.length})),!0),Og=({state:s,dispatch:t})=>(t(s.update({selection:{anchor:0,head:s.doc.length},userEvent:"select"})),!0),Dg=({state:s,dispatch:t})=>{let e=yn(s).map(({from:i,to:n})=>x.range(i,Math.min(n+1,s.doc.length)));return t(s.update({selection:x.create(e),userEvent:"select"})),!0},Tg=({state:s,dispatch:t})=>{let e=oi(s.selection,i=>{let n=ft(s),r=n.resolveStack(i.from,1);if(i.empty){let o=n.resolveStack(i.from,-1);o.node.from>=r.node.from&&o.node.to<=r.node.to&&(r=o)}for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&o.next)return x.range(l.to,l.from)}return i});return e.eq(s.selection)?!1:(t(ie(s,e)),!0)},Pg=({state:s,dispatch:t})=>{let e=s.selection,i=null;return e.ranges.length>1?i=x.create([e.main]):e.main.empty||(i=x.create([x.cursor(e.main.head)])),i?(t(ie(s,i)),!0):!1};function Ki(s,t){if(s.state.readOnly)return!1;let e="delete.selection",{state:i}=s,n=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=t(r);a<o?(e="delete.backward",a=ds(s,a,!1)):a>o&&(e="delete.forward",a=ds(s,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=ds(s,o,!1),l=ds(s,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:x.cursor(o,o<r.head?-1:1)}});return n.changes.empty?!1:(s.dispatch(i.update(n,{scrollIntoView:!0,userEvent:e,effects:e=="delete.selection"?O.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function ds(s,t,e){if(s instanceof O)for(let i of s.state.facet(O.atomicRanges).map(n=>n(s)))i.between(t,t,(n,r)=>{n<t&&r>t&&(t=e?r:n)});return t}const Uc=(s,t,e)=>Ki(s,i=>{let n=i.from,{state:r}=s,o=r.doc.lineAt(n),l,a;if(e&&!t&&n>o.from&&n<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,n-o.from))){if(l[l.length-1]=="	")return n-1;let h=ri(l,r.tabSize),c=h%_s(r)||_s(r);for(let f=0;f<c&&l[l.length-1-f]==" ";f++)n--;a=n}else a=at(o.text,n-o.from,t,t)+o.from,a==n&&o.number!=(t?r.doc.lines:1)?a+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,n-o.from))&&(a=at(o.text,a-o.from,!1,!1)+o.from);return a}),Hr=s=>Uc(s,!1,!0),Jc=s=>Uc(s,!0,!1),Gc=(s,t)=>Ki(s,e=>{let i=e.head,{state:n}=s,r=n.doc.lineAt(i),o=n.charCategorizer(i);for(let l=null;;){if(i==(t?r.to:r.from)){i==e.head&&r.number!=(t?n.doc.lines:1)&&(i+=t?1:-1);break}let a=at(r.text,i-r.from,t)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=e.head)&&(l=c),i=a}return i}),Yc=s=>Gc(s,!1),Rg=s=>Gc(s,!0),Bg=s=>Ki(s,t=>{let e=s.lineBlockAt(t.head).to;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),Eg=s=>Ki(s,t=>{let e=s.moveToLineBoundary(t,!1).head;return t.head>e?e:Math.max(0,t.head-1)}),Lg=s=>Ki(s,t=>{let e=s.moveToLineBoundary(t,!0).head;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),Ig=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:z.of(["",""])},range:x.cursor(i.from)}));return t(s.update(e,{scrollIntoView:!0,userEvent:"input"})),!0},Ng=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>{if(!i.empty||i.from==0||i.from==s.doc.length)return{range:i};let n=i.from,r=s.doc.lineAt(n),o=n==r.from?n-1:at(r.text,n-r.from,!1)+r.from,l=n==r.to?n+1:at(r.text,n-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:s.doc.slice(n,l).append(s.doc.slice(o,n))},range:x.cursor(l)}});return e.changes.empty?!1:(t(s.update(e,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function yn(s){let t=[],e=-1;for(let i of s.selection.ranges){let n=s.doc.lineAt(i.from),r=s.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=s.doc.lineAt(i.to-1)),e>=n.number){let o=t[t.length-1];o.to=r.to,o.ranges.push(i)}else t.push({from:n.from,to:r.to,ranges:[i]});e=r.number+1}return t}function Qc(s,t,e){if(s.readOnly)return!1;let i=[],n=[];for(let r of yn(s)){if(e?r.to==s.doc.length:r.from==0)continue;let o=s.doc.lineAt(e?r.to+1:r.from-1),l=o.length+1;if(e){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+s.lineBreak});for(let a of r.ranges)n.push(x.range(Math.min(s.doc.length,a.anchor+l),Math.min(s.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:s.lineBreak+o.text});for(let a of r.ranges)n.push(x.range(a.anchor-l,a.head-l))}}return i.length?(t(s.update({changes:i,scrollIntoView:!0,selection:x.create(n,s.selection.mainIndex),userEvent:"move.line"})),!0):!1}const Fg=({state:s,dispatch:t})=>Qc(s,t,!1),zg=({state:s,dispatch:t})=>Qc(s,t,!0);function Xc(s,t,e){if(s.readOnly)return!1;let i=[];for(let n of yn(s))e?i.push({from:n.from,insert:s.doc.slice(n.from,n.to)+s.lineBreak}):i.push({from:n.to,insert:s.lineBreak+s.doc.slice(n.from,n.to)});return t(s.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const Vg=({state:s,dispatch:t})=>Xc(s,t,!1),Hg=({state:s,dispatch:t})=>Xc(s,t,!0),Wg=s=>{if(s.state.readOnly)return!1;let{state:t}=s,e=t.changes(yn(t).map(({from:n,to:r})=>(n>0?n--:r<t.doc.length&&r++,{from:n,to:r}))),i=oi(t.selection,n=>{let r;if(s.lineWrapping){let o=s.lineBlockAt(n.head),l=s.coordsAtPos(n.head,n.assoc||1);l&&(r=o.bottom+s.documentTop-l.bottom+s.defaultLineHeight/2)}return s.moveVertically(n,!0,r)}).map(e);return s.dispatch({changes:e,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function $g(s,t){if(/\(\)|\[\]|\{\}/.test(s.sliceDoc(t-1,t+1)))return{from:t,to:t};let e=ft(s).resolveInner(t),i=e.childBefore(t),n=e.childAfter(t),r;return i&&n&&i.to<=t&&n.from>=t&&(r=i.type.prop(L.closedBy))&&r.indexOf(n.name)>-1&&s.doc.lineAt(i.to).from==s.doc.lineAt(n.from).from&&!/\S/.test(s.sliceDoc(i.to,n.from))?{from:i.to,to:n.from}:null}const Xl=Zc(!1),qg=Zc(!0);function Zc(s){return({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange(n=>{let{from:r,to:o}=n,l=t.doc.lineAt(r),a=!s&&r==o&&$g(t,r);s&&(r=o=(o<=l.to?l:t.doc.lineAt(o)).to);let h=new dn(t,{simulateBreak:r,simulateDoubleBreak:!!a}),c=fo(h,r);for(c==null&&(c=ri(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",Ii(t,c)];return a&&f.push(Ii(t,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:z.of(f)},range:x.cursor(r+1+f[1].length)}});return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function bo(s,t){let e=-1;return s.changeByRange(i=>{let n=[];for(let o=i.from;o<=i.to;){let l=s.doc.lineAt(o);l.number>e&&(i.empty||i.to>l.from)&&(t(l,n,i),e=l.number),o=l.to+1}let r=s.changes(n);return{changes:n,range:x.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const jg=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=Object.create(null),i=new dn(s,{overrideIndentation:r=>{let o=e[r];return o??-1}}),n=bo(s,(r,o,l)=>{let a=fo(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=Ii(s,a);(h!=c||l.from<r.from+h.length)&&(e[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return n.changes.empty||t(s.update(n,{userEvent:"indent"})),!0},_g=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(bo(s,(e,i)=>{i.push({from:e.from,insert:s.facet(un)})}),{userEvent:"input.indent"})),!0),Kg=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(bo(s,(e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=ri(n,s.tabSize),o=0,l=Ii(s,Math.max(0,r-_s(s)));for(;o<n.length&&o<l.length&&n.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:e.from+o,to:e.from+n.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),Ug=s=>(s.setTabFocusMode(),!0),Jg=[{key:"Ctrl-b",run:Rc,shift:Hc,preventDefault:!0},{key:"Ctrl-f",run:Bc,shift:Wc},{key:"Ctrl-p",run:Ic,shift:jc},{key:"Ctrl-n",run:Nc,shift:_c},{key:"Ctrl-a",run:dg,shift:Ag},{key:"Ctrl-e",run:pg,shift:Mg},{key:"Ctrl-d",run:Jc},{key:"Ctrl-h",run:Hr},{key:"Ctrl-k",run:Bg},{key:"Ctrl-Alt-h",run:Yc},{key:"Ctrl-o",run:Ig},{key:"Ctrl-t",run:Ng},{key:"Ctrl-v",run:Vr}],Gg=[{key:"ArrowLeft",run:Rc,shift:Hc,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:ng,shift:bg,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:fg,shift:Sg,preventDefault:!0},{key:"ArrowRight",run:Bc,shift:Wc,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:rg,shift:yg,preventDefault:!0},{mac:"Cmd-ArrowRight",run:ug,shift:Cg,preventDefault:!0},{key:"ArrowUp",run:Ic,shift:jc,preventDefault:!0},{mac:"Cmd-ArrowUp",run:Jl,shift:Yl},{mac:"Ctrl-ArrowUp",run:_l,shift:Kl},{key:"ArrowDown",run:Nc,shift:_c,preventDefault:!0},{mac:"Cmd-ArrowDown",run:Gl,shift:Ql},{mac:"Ctrl-ArrowDown",run:Vr,shift:Ul},{key:"PageUp",run:_l,shift:Kl},{key:"PageDown",run:Vr,shift:Ul},{key:"Home",run:cg,shift:kg,preventDefault:!0},{key:"Mod-Home",run:Jl,shift:Yl},{key:"End",run:hg,shift:vg,preventDefault:!0},{key:"Mod-End",run:Gl,shift:Ql},{key:"Enter",run:Xl,shift:Xl},{key:"Mod-a",run:Og},{key:"Backspace",run:Hr,shift:Hr},{key:"Delete",run:Jc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Yc},{key:"Mod-Delete",mac:"Alt-Delete",run:Rg},{mac:"Mod-Backspace",run:Eg},{mac:"Mod-Delete",run:Lg}].concat(Jg.map(s=>({mac:s.key,run:s.run,shift:s.shift}))),Yg=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:lg,shift:xg},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:ag,shift:wg},{key:"Alt-ArrowUp",run:Fg},{key:"Shift-Alt-ArrowUp",run:Vg},{key:"Alt-ArrowDown",run:zg},{key:"Shift-Alt-ArrowDown",run:Hg},{key:"Escape",run:Pg},{key:"Mod-Enter",run:qg},{key:"Alt-l",mac:"Ctrl-l",run:Dg},{key:"Mod-i",run:Tg,preventDefault:!0},{key:"Mod-[",run:Kg},{key:"Mod-]",run:_g},{key:"Mod-Alt-\\",run:jg},{key:"Shift-Mod-k",run:Wg},{key:"Shift-Mod-\\",run:gg},{key:"Mod-/",run:Vm},{key:"Alt-A",run:Wm},{key:"Ctrl-m",mac:"Shift-Alt-m",run:Ug}].concat(Gg);function q(){var s=arguments[0];typeof s=="string"&&(s=document.createElement(s));var t=1,e=arguments[1];if(e&&typeof e=="object"&&e.nodeType==null&&!Array.isArray(e)){for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var n=e[i];typeof n=="string"?s.setAttribute(i,n):n!=null&&(s[i]=n)}t++}for(;t<arguments.length;t++)tf(s,arguments[t]);return s}function tf(s,t){if(typeof t=="string")s.appendChild(document.createTextNode(t));else if(t!=null)if(t.nodeType!=null)s.appendChild(t);else if(Array.isArray(t))for(var e=0;e<t.length;e++)tf(s,t[e]);else throw new RangeError("Unsupported child node: "+t)}const Zl=typeof String.prototype.normalize=="function"?s=>s.normalize("NFKD"):s=>s;class si{constructor(t,e,i=0,n=t.length,r,o){this.test=o,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=t.iterRange(i,n),this.bufferStart=i,this.normalize=r?l=>r(Zl(l)):Zl,this.query=this.normalize(e)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return bt(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let t=this.peek();if(t<0)return this.done=!0,this;let e=_r(t),i=this.bufferStart+this.bufferPos;this.bufferPos+=Gt(t);let n=this.normalize(e);if(n.length)for(let r=0,o=i;;r++){let l=n.charCodeAt(r),a=this.match(l,o,this.bufferPos+this.bufferStart);if(r==n.length-1){if(a)return this.value=a,this;break}o==i&&r<e.length&&e.charCodeAt(r)==l&&o++}}}match(t,e,i){let n=null;for(let r=0;r<this.matches.length;r+=2){let o=this.matches[r],l=!1;this.query.charCodeAt(o)==t&&(o==this.query.length-1?n={from:this.matches[r+1],to:i}:(this.matches[r]++,l=!0)),l||(this.matches.splice(r,2),r-=2)}return this.query.charCodeAt(0)==t&&(this.query.length==1?n={from:e,to:i}:this.matches.push(1,e)),n&&this.test&&!this.test(n.from,n.to,this.buffer,this.bufferStart)&&(n=null),n}}typeof Symbol<"u"&&(si.prototype[Symbol.iterator]=function(){return this});const ef={from:-1,to:-1,match:/.*/.exec("")},yo="gm"+(/x/.unicode==null?"":"u");class sf{constructor(t,e,i,n=0,r=t.length){if(this.text=t,this.to=r,this.curLine="",this.done=!1,this.value=ef,/\\[sWDnr]|\n|\r|\[\^/.test(e))return new nf(t,e,i,n,r);this.re=new RegExp(e,yo+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.iter=t.iter();let o=t.lineAt(n);this.curLineStart=o.from,this.matchPos=Gs(t,n),this.getLine(this.curLineStart)}getLine(t){this.iter.next(t),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let t=this.matchPos-this.curLineStart;;){this.re.lastIndex=t;let e=this.matchPos<=this.to&&this.re.exec(this.curLine);if(e){let i=this.curLineStart+e.index,n=i+e[0].length;if(this.matchPos=Gs(this.text,n+(i==n?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<n||i>this.value.to)&&(!this.test||this.test(i,n,e)))return this.value={from:i,to:n,match:e},this;t=this.matchPos-this.curLineStart}else if(this.curLineStart+this.curLine.length<this.to)this.nextLine(),t=0;else return this.done=!0,this}}}const $n=new WeakMap;class Ge{constructor(t,e){this.from=t,this.text=e}get to(){return this.from+this.text.length}static get(t,e,i){let n=$n.get(t);if(!n||n.from>=i||n.to<=e){let l=new Ge(e,t.sliceString(e,i));return $n.set(t,l),l}if(n.from==e&&n.to==i)return n;let{text:r,from:o}=n;return o>e&&(r=t.sliceString(e,o)+r,o=e),n.to<i&&(r+=t.sliceString(n.to,i)),$n.set(t,new Ge(o,r)),new Ge(e,r.slice(e-o,i-o))}}class nf{constructor(t,e,i,n,r){this.text=t,this.to=r,this.done=!1,this.value=ef,this.matchPos=Gs(t,n),this.re=new RegExp(e,yo+(i!=null&&i.ignoreCase?"i":"")),this.test=i==null?void 0:i.test,this.flat=Ge.get(t,n,this.chunkEnd(n+5e3))}chunkEnd(t){return t>=this.to?this.to:this.text.lineAt(t).to}next(){for(;;){let t=this.re.lastIndex=this.matchPos-this.flat.from,e=this.re.exec(this.flat.text);if(e&&!e[0]&&e.index==t&&(this.re.lastIndex=t+1,e=this.re.exec(this.flat.text)),e){let i=this.flat.from+e.index,n=i+e[0].length;if((this.flat.to>=this.to||e.index+e[0].length<=this.flat.text.length-10)&&(!this.test||this.test(i,n,e)))return this.value={from:i,to:n,match:e},this.matchPos=Gs(this.text,n+(i==n?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=Ge.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+this.flat.text.length*2))}}}typeof Symbol<"u"&&(sf.prototype[Symbol.iterator]=nf.prototype[Symbol.iterator]=function(){return this});function Qg(s){try{return new RegExp(s,yo),!0}catch{return!1}}function Gs(s,t){if(t>=s.length)return t;let e=s.lineAt(t),i;for(;t<e.to&&(i=e.text.charCodeAt(t-e.from))>=56320&&i<57344;)t++;return t}function Wr(s){let t=String(s.state.doc.lineAt(s.state.selection.main.head).number),e=q("input",{class:"cm-textfield",name:"line",value:t}),i=q("form",{class:"cm-gotoLine",onkeydown:r=>{r.keyCode==27?(r.preventDefault(),s.dispatch({effects:Ci.of(!1)}),s.focus()):r.keyCode==13&&(r.preventDefault(),n())},onsubmit:r=>{r.preventDefault(),n()}},q("label",s.state.phrase("Go to line"),": ",e)," ",q("button",{class:"cm-button",type:"submit"},s.state.phrase("go")),q("button",{name:"close",onclick:()=>{s.dispatch({effects:Ci.of(!1)}),s.focus()},"aria-label":s.state.phrase("close"),type:"button"},["×"]));function n(){let r=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(e.value);if(!r)return;let{state:o}=s,l=o.doc.lineAt(o.selection.main.head),[,a,h,c,f]=r,u=c?+c.slice(1):0,d=h?+h:l.number;if(h&&f){let g=d/100;a&&(g=g*(a=="-"?-1:1)+l.number/o.doc.lines),d=Math.round(o.doc.lines*g)}else h&&a&&(d=d*(a=="-"?-1:1)+l.number);let p=o.doc.line(Math.max(1,Math.min(o.doc.lines,d))),m=x.cursor(p.from+Math.max(0,Math.min(u,p.length)));s.dispatch({effects:[Ci.of(!1),O.scrollIntoView(m.from,{y:"center"})],selection:m}),s.focus()}return{dom:i}}const Ci=B.define(),ta=it.define({create(){return!0},update(s,t){for(let e of t.effects)e.is(Ci)&&(s=e.value);return s},provide:s=>Ei.from(s,t=>t?Wr:null)}),Xg=s=>{let t=Bi(s,Wr);if(!t){let e=[Ci.of(!0)];s.state.field(ta,!1)==null&&e.push(B.appendConfig.of([ta,Zg])),s.dispatch({effects:e}),t=Bi(s,Wr)}return t&&t.dom.querySelector("input").select(),!0},Zg=O.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px",position:"relative","& label":{fontSize:"80%"},"& [name=close]":{position:"absolute",top:"0",bottom:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:"0"}}}),t0={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},rf=D.define({combine(s){return Ht(s,t0,{highlightWordAroundCursor:(t,e)=>t||e,minSelectionLength:Math.min,maxMatches:Math.min})}});function e0(s){let t=[o0,r0];return s&&t.push(rf.of(s)),t}const i0=P.mark({class:"cm-selectionMatch"}),s0=P.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function ea(s,t,e,i){return(e==0||s(t.sliceDoc(e-1,e))!=G.Word)&&(i==t.doc.length||s(t.sliceDoc(i,i+1))!=G.Word)}function n0(s,t,e,i){return s(t.sliceDoc(e,e+1))==G.Word&&s(t.sliceDoc(i-1,i))==G.Word}const r0=Z.fromClass(class{constructor(s){this.decorations=this.getDeco(s)}update(s){(s.selectionSet||s.docChanged||s.viewportChanged)&&(this.decorations=this.getDeco(s.view))}getDeco(s){let t=s.state.facet(rf),{state:e}=s,i=e.selection;if(i.ranges.length>1)return P.none;let n=i.main,r,o=null;if(n.empty){if(!t.highlightWordAroundCursor)return P.none;let a=e.wordAt(n.head);if(!a)return P.none;o=e.charCategorizer(n.head),r=e.sliceDoc(a.from,a.to)}else{let a=n.to-n.from;if(a<t.minSelectionLength||a>200)return P.none;if(t.wholeWords){if(r=e.sliceDoc(n.from,n.to),o=e.charCategorizer(n.head),!(ea(o,e,n.from,n.to)&&n0(o,e,n.from,n.to)))return P.none}else if(r=e.sliceDoc(n.from,n.to),!r)return P.none}let l=[];for(let a of s.visibleRanges){let h=new si(e.doc,r,a.from,a.to);for(;!h.next().done;){let{from:c,to:f}=h.value;if((!o||ea(o,e,c,f))&&(n.empty&&c<=n.from&&f>=n.to?l.push(s0.range(c,f)):(c>=n.to||f<=n.from)&&l.push(i0.range(c,f)),l.length>t.maxMatches))return P.none}}return P.set(l)}},{decorations:s=>s.decorations}),o0=O.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),l0=({state:s,dispatch:t})=>{let{selection:e}=s,i=x.create(e.ranges.map(n=>s.wordAt(n.head)||x.cursor(n.head)),e.mainIndex);return i.eq(e)?!1:(t(s.update({selection:i})),!0)};function a0(s,t){let{main:e,ranges:i}=s.selection,n=s.wordAt(e.head),r=n&&n.from==e.from&&n.to==e.to;for(let o=!1,l=new si(s.doc,t,i[i.length-1].to);;)if(l.next(),l.done){if(o)return null;l=new si(s.doc,t,0,Math.max(0,i[i.length-1].from-1)),o=!0}else{if(o&&i.some(a=>a.from==l.value.from))continue;if(r){let a=s.wordAt(l.value.from);if(!a||a.from!=l.value.from||a.to!=l.value.to)continue}return l.value}}const h0=({state:s,dispatch:t})=>{let{ranges:e}=s.selection;if(e.some(r=>r.from===r.to))return l0({state:s,dispatch:t});let i=s.sliceDoc(e[0].from,e[0].to);if(s.selection.ranges.some(r=>s.sliceDoc(r.from,r.to)!=i))return!1;let n=a0(s,i);return n?(t(s.update({selection:s.selection.addRange(x.range(n.from,n.to),!1),effects:O.scrollIntoView(n.to)})),!0):!1},li=D.define({combine(s){return Ht(s,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:t=>new v0(t),scrollToMatch:t=>O.scrollIntoView(t)})}});class of{constructor(t){this.search=t.search,this.caseSensitive=!!t.caseSensitive,this.literal=!!t.literal,this.regexp=!!t.regexp,this.replace=t.replace||"",this.valid=!!this.search&&(!this.regexp||Qg(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!t.wholeWord}unquote(t){return this.literal?t:t.replace(/\\([nrt\\])/g,(e,i)=>i=="n"?`
`:i=="r"?"\r":i=="t"?"	":"\\")}eq(t){return this.search==t.search&&this.replace==t.replace&&this.caseSensitive==t.caseSensitive&&this.regexp==t.regexp&&this.wholeWord==t.wholeWord}create(){return this.regexp?new d0(this):new f0(this)}getCursor(t,e=0,i){let n=t.doc?t:V.create({doc:t});return i==null&&(i=n.doc.length),this.regexp?$e(this,n,e,i):We(this,n,e,i)}}class lf{constructor(t){this.spec=t}}function We(s,t,e,i){return new si(t.doc,s.unquoted,e,i,s.caseSensitive?void 0:n=>n.toLowerCase(),s.wholeWord?c0(t.doc,t.charCategorizer(t.selection.main.head)):void 0)}function c0(s,t){return(e,i,n,r)=>((r>e||r+n.length<i)&&(r=Math.max(0,e-2),n=s.sliceString(r,Math.min(s.length,i+2))),(t(Ys(n,e-r))!=G.Word||t(Qs(n,e-r))!=G.Word)&&(t(Qs(n,i-r))!=G.Word||t(Ys(n,i-r))!=G.Word))}class f0 extends lf{constructor(t){super(t)}nextMatch(t,e,i){let n=We(this.spec,t,i,t.doc.length).nextOverlapping();if(n.done){let r=Math.min(t.doc.length,e+this.spec.unquoted.length);n=We(this.spec,t,0,r).nextOverlapping()}return n.done||n.value.from==e&&n.value.to==i?null:n.value}prevMatchInRange(t,e,i){for(let n=i;;){let r=Math.max(e,n-1e4-this.spec.unquoted.length),o=We(this.spec,t,r,n),l=null;for(;!o.nextOverlapping().done;)l=o.value;if(l)return l;if(r==e)return null;n-=1e4}}prevMatch(t,e,i){let n=this.prevMatchInRange(t,0,e);return n||(n=this.prevMatchInRange(t,Math.max(0,i-this.spec.unquoted.length),t.doc.length)),n&&(n.from!=e||n.to!=i)?n:null}getReplacement(t){return this.spec.unquote(this.spec.replace)}matchAll(t,e){let i=We(this.spec,t,0,t.doc.length),n=[];for(;!i.next().done;){if(n.length>=e)return null;n.push(i.value)}return n}highlight(t,e,i,n){let r=We(this.spec,t,Math.max(0,e-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,t.doc.length));for(;!r.next().done;)n(r.value.from,r.value.to)}}function $e(s,t,e,i){return new sf(t.doc,s.search,{ignoreCase:!s.caseSensitive,test:s.wholeWord?u0(t.charCategorizer(t.selection.main.head)):void 0},e,i)}function Ys(s,t){return s.slice(at(s,t,!1),t)}function Qs(s,t){return s.slice(t,at(s,t))}function u0(s){return(t,e,i)=>!i[0].length||(s(Ys(i.input,i.index))!=G.Word||s(Qs(i.input,i.index))!=G.Word)&&(s(Qs(i.input,i.index+i[0].length))!=G.Word||s(Ys(i.input,i.index+i[0].length))!=G.Word)}class d0 extends lf{nextMatch(t,e,i){let n=$e(this.spec,t,i,t.doc.length).next();return n.done&&(n=$e(this.spec,t,0,e).next()),n.done?null:n.value}prevMatchInRange(t,e,i){for(let n=1;;n++){let r=Math.max(e,i-n*1e4),o=$e(this.spec,t,r,i),l=null;for(;!o.next().done;)l=o.value;if(l&&(r==e||l.from>r+10))return l;if(r==e)return null}}prevMatch(t,e,i){return this.prevMatchInRange(t,0,e)||this.prevMatchInRange(t,i,t.doc.length)}getReplacement(t){return this.spec.unquote(this.spec.replace).replace(/\$([$&]|\d+)/g,(e,i)=>{if(i=="&")return t.match[0];if(i=="$")return"$";for(let n=i.length;n>0;n--){let r=+i.slice(0,n);if(r>0&&r<t.match.length)return t.match[r]+i.slice(n)}return e})}matchAll(t,e){let i=$e(this.spec,t,0,t.doc.length),n=[];for(;!i.next().done;){if(n.length>=e)return null;n.push(i.value)}return n}highlight(t,e,i,n){let r=$e(this.spec,t,Math.max(0,e-250),Math.min(i+250,t.doc.length));for(;!r.next().done;)n(r.value.from,r.value.to)}}const Ni=B.define(),xo=B.define(),be=it.define({create(s){return new qn($r(s).create(),null)},update(s,t){for(let e of t.effects)e.is(Ni)?s=new qn(e.value.create(),s.panel):e.is(xo)&&(s=new qn(s.query,e.value?wo:null));return s},provide:s=>Ei.from(s,t=>t.panel)});class qn{constructor(t,e){this.query=t,this.panel=e}}const p0=P.mark({class:"cm-searchMatch"}),m0=P.mark({class:"cm-searchMatch cm-searchMatch-selected"}),g0=Z.fromClass(class{constructor(s){this.view=s,this.decorations=this.highlight(s.state.field(be))}update(s){let t=s.state.field(be);(t!=s.startState.field(be)||s.docChanged||s.selectionSet||s.viewportChanged)&&(this.decorations=this.highlight(t))}highlight({query:s,panel:t}){if(!t||!s.spec.valid)return P.none;let{view:e}=this,i=new le;for(let n=0,r=e.visibleRanges,o=r.length;n<o;n++){let{from:l,to:a}=r[n];for(;n<o-1&&a>r[n+1].from-2*250;)a=r[++n].to;s.highlight(e.state,l,a,(h,c)=>{let f=e.state.selection.ranges.some(u=>u.from==h&&u.to==c);i.add(h,c,f?m0:p0)})}return i.finish()}},{decorations:s=>s.decorations});function Ui(s){return t=>{let e=t.state.field(be,!1);return e&&e.query.spec.valid?s(t,e):cf(t)}}const Xs=Ui((s,{query:t})=>{let{to:e}=s.state.selection.main,i=t.nextMatch(s.state,e,e);if(!i)return!1;let n=x.single(i.from,i.to),r=s.state.facet(li);return s.dispatch({selection:n,effects:[vo(s,i),r.scrollToMatch(n.main,s)],userEvent:"select.search"}),hf(s),!0}),Zs=Ui((s,{query:t})=>{let{state:e}=s,{from:i}=e.selection.main,n=t.prevMatch(e,i,i);if(!n)return!1;let r=x.single(n.from,n.to),o=s.state.facet(li);return s.dispatch({selection:r,effects:[vo(s,n),o.scrollToMatch(r.main,s)],userEvent:"select.search"}),hf(s),!0}),b0=Ui((s,{query:t})=>{let e=t.matchAll(s.state,1e3);return!e||!e.length?!1:(s.dispatch({selection:x.create(e.map(i=>x.range(i.from,i.to))),userEvent:"select.search.matches"}),!0)}),y0=({state:s,dispatch:t})=>{let e=s.selection;if(e.ranges.length>1||e.main.empty)return!1;let{from:i,to:n}=e.main,r=[],o=0;for(let l=new si(s.doc,s.sliceDoc(i,n));!l.next().done;){if(r.length>1e3)return!1;l.value.from==i&&(o=r.length),r.push(x.range(l.value.from,l.value.to))}return t(s.update({selection:x.create(r,o),userEvent:"select.search.matches"})),!0},ia=Ui((s,{query:t})=>{let{state:e}=s,{from:i,to:n}=e.selection.main;if(e.readOnly)return!1;let r=t.nextMatch(e,i,i);if(!r)return!1;let o=r,l=[],a,h,c=[];if(o.from==i&&o.to==n&&(h=e.toText(t.getReplacement(o)),l.push({from:o.from,to:o.to,insert:h}),o=t.nextMatch(e,o.from,o.to),c.push(O.announce.of(e.phrase("replaced match on line $",e.doc.lineAt(i).number)+"."))),o){let f=l.length==0||l[0].from>=r.to?0:r.to-r.from-h.length;a=x.single(o.from-f,o.to-f),c.push(vo(s,o)),c.push(e.facet(li).scrollToMatch(a.main,s))}return s.dispatch({changes:l,selection:a,effects:c,userEvent:"input.replace"}),!0}),x0=Ui((s,{query:t})=>{if(s.state.readOnly)return!1;let e=t.matchAll(s.state,1e9).map(n=>{let{from:r,to:o}=n;return{from:r,to:o,insert:t.getReplacement(n)}});if(!e.length)return!1;let i=s.state.phrase("replaced $ matches",e.length)+".";return s.dispatch({changes:e,effects:O.announce.of(i),userEvent:"input.replace.all"}),!0});function wo(s){return s.state.facet(li).createPanel(s)}function $r(s,t){var e,i,n,r,o;let l=s.selection.main,a=l.empty||l.to>l.from+100?"":s.sliceDoc(l.from,l.to);if(t&&!a)return t;let h=s.facet(li);return new of({search:((e=t==null?void 0:t.literal)!==null&&e!==void 0?e:h.literal)?a:a.replace(/\n/g,"\\n"),caseSensitive:(i=t==null?void 0:t.caseSensitive)!==null&&i!==void 0?i:h.caseSensitive,literal:(n=t==null?void 0:t.literal)!==null&&n!==void 0?n:h.literal,regexp:(r=t==null?void 0:t.regexp)!==null&&r!==void 0?r:h.regexp,wholeWord:(o=t==null?void 0:t.wholeWord)!==null&&o!==void 0?o:h.wholeWord})}function af(s){let t=Bi(s,wo);return t&&t.dom.querySelector("[main-field]")}function hf(s){let t=af(s);t&&t==s.root.activeElement&&t.select()}const cf=s=>{let t=s.state.field(be,!1);if(t&&t.panel){let e=af(s);if(e&&e!=s.root.activeElement){let i=$r(s.state,t.query.spec);i.valid&&s.dispatch({effects:Ni.of(i)}),e.focus(),e.select()}}else s.dispatch({effects:[xo.of(!0),t?Ni.of($r(s.state,t.query.spec)):B.appendConfig.of(S0)]});return!0},ff=s=>{let t=s.state.field(be,!1);if(!t||!t.panel)return!1;let e=Bi(s,wo);return e&&e.dom.contains(s.root.activeElement)&&s.focus(),s.dispatch({effects:xo.of(!1)}),!0},w0=[{key:"Mod-f",run:cf,scope:"editor search-panel"},{key:"F3",run:Xs,shift:Zs,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:Xs,shift:Zs,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:ff,scope:"editor search-panel"},{key:"Mod-Shift-l",run:y0},{key:"Mod-Alt-g",run:Xg},{key:"Mod-d",run:h0,preventDefault:!0}];class v0{constructor(t){this.view=t;let e=this.query=t.state.field(be).query.spec;this.commit=this.commit.bind(this),this.searchField=q("input",{value:e.search,placeholder:Ct(t,"Find"),"aria-label":Ct(t,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=q("input",{value:e.replace,placeholder:Ct(t,"Replace"),"aria-label":Ct(t,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=q("input",{type:"checkbox",name:"case",form:"",checked:e.caseSensitive,onchange:this.commit}),this.reField=q("input",{type:"checkbox",name:"re",form:"",checked:e.regexp,onchange:this.commit}),this.wordField=q("input",{type:"checkbox",name:"word",form:"",checked:e.wholeWord,onchange:this.commit});function i(n,r,o){return q("button",{class:"cm-button",name:n,onclick:r,type:"button"},o)}this.dom=q("div",{onkeydown:n=>this.keydown(n),class:"cm-search"},[this.searchField,i("next",()=>Xs(t),[Ct(t,"next")]),i("prev",()=>Zs(t),[Ct(t,"previous")]),i("select",()=>b0(t),[Ct(t,"all")]),q("label",null,[this.caseField,Ct(t,"match case")]),q("label",null,[this.reField,Ct(t,"regexp")]),q("label",null,[this.wordField,Ct(t,"by word")]),...t.state.readOnly?[]:[q("br"),this.replaceField,i("replace",()=>ia(t),[Ct(t,"replace")]),i("replaceAll",()=>x0(t),[Ct(t,"replace all")])],q("button",{name:"close",onclick:()=>ff(t),"aria-label":Ct(t,"close"),type:"button"},["×"])])}commit(){let t=new of({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});t.eq(this.query)||(this.query=t,this.view.dispatch({effects:Ni.of(t)}))}keydown(t){Bd(this.view,t,"search-panel")?t.preventDefault():t.keyCode==13&&t.target==this.searchField?(t.preventDefault(),(t.shiftKey?Zs:Xs)(this.view)):t.keyCode==13&&t.target==this.replaceField&&(t.preventDefault(),ia(this.view))}update(t){for(let e of t.transactions)for(let i of e.effects)i.is(Ni)&&!i.value.eq(this.query)&&this.setQuery(i.value)}setQuery(t){this.query=t,this.searchField.value=t.search,this.replaceField.value=t.replace,this.caseField.checked=t.caseSensitive,this.reField.checked=t.regexp,this.wordField.checked=t.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(li).top}}function Ct(s,t){return s.state.phrase(t)}const ps=30,ms=/[\s\.,:;?!]/;function vo(s,{from:t,to:e}){let i=s.state.doc.lineAt(t),n=s.state.doc.lineAt(e).to,r=Math.max(i.from,t-ps),o=Math.min(n,e+ps),l=s.state.sliceDoc(r,o);if(r!=i.from){for(let a=0;a<ps;a++)if(!ms.test(l[a+1])&&ms.test(l[a])){l=l.slice(a);break}}if(o!=n){for(let a=l.length-1;a>l.length-ps;a--)if(!ms.test(l[a-1])&&ms.test(l[a])){l=l.slice(0,a);break}}return O.announce.of(`${s.state.phrase("current match")}. ${l} ${s.state.phrase("on line")} ${i.number}.`)}const k0=O.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),S0=[be,ze.low(g0),k0];class uf{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(t){let e=ft(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(df(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(t,e,i){t=="abort"&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function sa(s){let t=Object.keys(s).join(""),e=/\w/.test(t);return e&&(t=t.replace(/\w/g,"")),`[${e?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function C0(s){let t=Object.create(null),e=Object.create(null);for(let{label:n}of s){t[n[0]]=!0;for(let r=1;r<n.length;r++)e[n[r]]=!0}let i=sa(t)+sa(e)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function A0(s){let t=s.map(n=>typeof n=="string"?{label:n}:n),[e,i]=t.every(n=>/^\w+$/.test(n.label))?[/\w*$/,/\w+$/]:C0(t);return n=>{let r=n.matchBefore(i);return r||n.explicit?{from:r?r.from:n.pos,options:t,validFor:e}:null}}class na{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function Be(s){return s.selection.main.from}function df(s,t){var e;let{source:i}=s,n=t&&i[0]!="^",r=i[i.length-1]!="$";return!n&&!r?s:new RegExp(`${n?"^":""}(?:${i})${r?"$":""}`,(e=s.flags)!==null&&e!==void 0?e:s.ignoreCase?"i":"")}const pf=he.define();function M0(s,t,e,i){let{main:n}=s.selection,r=e-n.from,o=i-n.from;return Object.assign(Object.assign({},s.changeByRange(l=>{if(l!=n&&e!=i&&s.sliceDoc(l.from+r,l.from+o)!=s.sliceDoc(e,i))return{range:l};let a=s.toText(t);return{changes:{from:l.from+r,to:i==n.from?l.to:l.from+o,insert:a},range:x.cursor(l.from+r+a.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}const ra=new WeakMap;function O0(s){if(!Array.isArray(s))return s;let t=ra.get(s);return t||ra.set(s,t=A0(s)),t}const tn=B.define(),Fi=B.define();class D0{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=bt(t,e),n=Gt(i);this.chars.push(i);let r=t.slice(e,e+n),o=r.toUpperCase();this.folded.push(bt(o==r?r.toLowerCase():o,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(this.pattern.length==0)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:r,byWord:o}=this;if(e.length==1){let w=bt(t,0),k=Gt(w),S=k==t.length?0:-100;if(w!=e[0])if(w==i[0])S+=-200;else return null;return this.ret(S,[0,k])}let l=t.indexOf(this.pattern);if(l==0)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,h=0;if(l<0){for(let w=0,k=Math.min(t.length,200);w<k&&h<a;){let S=bt(t,w);(S==e[h]||S==i[h])&&(n[h++]=w),w+=Gt(S)}if(h<a)return null}let c=0,f=0,u=!1,d=0,p=-1,m=-1,g=/[a-z]/.test(t),b=!0;for(let w=0,k=Math.min(t.length,200),S=0;w<k&&f<a;){let v=bt(t,w);l<0&&(c<a&&v==e[c]&&(r[c++]=w),d<a&&(v==e[d]||v==i[d]?(d==0&&(p=w),m=w+1,d++):d=0));let C,M=v<255?v>=48&&v<=57||v>=97&&v<=122?2:v>=65&&v<=90?1:0:(C=_r(v))!=C.toLowerCase()?1:C!=C.toUpperCase()?2:0;(!w||M==1&&g||S==0&&M!=0)&&(e[f]==v||i[f]==v&&(u=!0)?o[f++]=w:o.length&&(b=!1)),S=M,w+=Gt(v)}return f==a&&o[0]==0&&b?this.result(-100+(u?-200:0),o,t):d==a&&p==0?this.ret(-200-t.length+(m==t.length?0:-100),[0,m]):l>-1?this.ret(-700-t.length,[l,l+this.pattern.length]):d==a?this.ret(-200+-700-t.length,[p,m]):f==a?this.result(-100+(u?-200:0)+-700+(b?0:-1100),o,t):e.length==2?null:this.result((n[0]?-700:0)+-200+-1100,n,t)}result(t,e,i){let n=[],r=0;for(let o of e){let l=o+(this.astral?Gt(bt(i,o)):1);r&&n[r-1]==o?n[r-1]=l:(n[r++]=o,n[r++]=l)}return this.ret(t-i.length,n)}}class T0{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase()}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const nt=D.define({combine(s){return Ht(s,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:P0,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>oa(t(i),e(i)),optionClass:(t,e)=>i=>oa(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})}});function oa(s,t){return s?t?s+" "+t:s:t}function P0(s,t,e,i,n,r){let o=s.textDirection==J.RTL,l=o,a=!1,h="top",c,f,u=t.left-n.left,d=n.right-t.right,p=i.right-i.left,m=i.bottom-i.top;if(l&&u<Math.min(p,d)?l=!1:!l&&d<Math.min(p,u)&&(l=!0),p<=(l?u:d))c=Math.max(n.top,Math.min(e.top,n.bottom-m))-t.top,f=Math.min(400,l?u:d);else{a=!0,f=Math.min(400,(o?t.right:n.right-t.left)-30);let w=n.bottom-t.bottom;w>=m||w>t.top?c=e.bottom-t.top:(h="bottom",c=t.bottom-e.top)}let g=(t.bottom-t.top)/r.offsetHeight,b=(t.right-t.left)/r.offsetWidth;return{style:`${h}: ${c/g}px; max-width: ${f/b}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function R0(s){let t=s.addToOptions.slice();return s.icons&&t.push({render(e){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),e.type&&i.classList.add(...e.type.split(/\s+/g).map(n=>"cm-completionIcon-"+n)),i.setAttribute("aria-hidden","true"),i},position:20}),t.push({render(e,i,n,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=e.displayLabel||e.label,a=0;for(let h=0;h<r.length;){let c=r[h++],f=r[h++];c>a&&o.appendChild(document.createTextNode(l.slice(a,c)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(c,f))),u.className="cm-completionMatchedText",a=f}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(e){if(!e.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=e.detail,i},position:80}),t.sort((e,i)=>e.position-i.position).map(e=>e.render)}function jn(s,t,e){if(s<=e)return{from:0,to:s};if(t<0&&(t=0),t<=s>>1){let n=Math.floor(t/e);return{from:n*e,to:(n+1)*e}}let i=Math.floor((s-t)/e);return{from:s-(i+1)*e,to:s-i*e}}class B0{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:r,selected:o}=n.open,l=t.state.facet(nt);this.optionContent=R0(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=jn(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=t.state.field(e).open;for(let c=a.target,f;c&&c!=this.dom;c=c.parentNode)if(c.nodeName=="LI"&&(f=/-(\d+)$/.exec(c.id))&&+f[1]<h.length){this.applyCompletion(t,h[+f[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=t.state.field(this.stateField,!1);h&&h.tooltip&&t.state.facet(nt).closeOnBlur&&a.relatedTarget!=t.contentDOM&&t.dispatch({effects:Fi.of(null)})}),this.showOptions(r,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:r,selected:o,disabled:l}=i.open;(!n.open||n.open.options!=r)&&(this.range=jn(r.length,o,t.state.facet(nt).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((e=n.open)===null||e===void 0?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of e.split(" "))i&&this.dom.classList.add(i);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=jn(e.options.length,e.selected,this.view.state.facet(nt).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let r=typeof n=="string"?document.createTextNode(n):n(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(o,i)}).catch(o=>wt(this.view.state,o,"completion info")):this.addInfoPane(r,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",t.nodeType!=null)i.appendChild(t),this.infoDestroy=null;else{let{dom:n,destroy:r}=t;i.appendChild(n),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)i.nodeName!="LI"||!i.id?n--:n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return e&&L0(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:o.clientWidth,bottom:o.clientHeight}}return n.top>Math.min(r.bottom,e.bottom)-10||n.bottom<Math.max(r.top,e.top)+10?null:this.view.state.facet(nt).positionInfo(this.view,e,n,i,r,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions")),n.addEventListener("mousedown",o=>{o.target==n&&o.preventDefault()});let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=t[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)n.appendChild(h.header(h));else{let d=n.appendChild(document.createElement("completion-section"));d.textContent=u}}const c=n.appendChild(document.createElement("li"));c.id=e+"-"+o,c.setAttribute("role","option");let f=this.optionClass(l);f&&(c.className=f);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&c.appendChild(d)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function E0(s,t){return e=>new B0(e,s,t)}function L0(s,t){let e=s.getBoundingClientRect(),i=t.getBoundingClientRect(),n=e.height/s.offsetHeight;i.top<e.top?s.scrollTop-=(e.top-i.top)/n:i.bottom>e.bottom&&(s.scrollTop+=(i.bottom-e.bottom)/n)}function la(s){return(s.boost||0)*100+(s.apply?10:0)+(s.info?5:0)+(s.type?1:0)}function I0(s,t){let e=[],i=null,n=h=>{e.push(h);let{section:c}=h.completion;if(c){i||(i=[]);let f=typeof c=="string"?c:c.name;i.some(u=>u.name==f)||i.push(typeof c=="string"?{name:f}:c)}},r=t.facet(nt);for(let h of s)if(h.hasResult()){let c=h.result.getMatch;if(h.result.filter===!1)for(let f of h.result.options)n(new na(f,h.source,c?c(f):[],1e9-e.length));else{let f=t.sliceDoc(h.from,h.to),u,d=r.filterStrict?new T0(f):new D0(f);for(let p of h.result.options)if(u=d.match(p.label)){let m=p.displayLabel?c?c(p,u.matched):[]:u.matched;n(new na(p,h.source,m,u.score+(p.boost||0)))}}}if(i){let h=Object.create(null),c=0,f=(u,d)=>{var p,m;return((p=u.rank)!==null&&p!==void 0?p:1e9)-((m=d.rank)!==null&&m!==void 0?m:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(f))c-=1e5,h[u.name]=c;for(let u of e){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name])}}let o=[],l=null,a=r.compareCompletions;for(let h of e.sort((c,f)=>f.score-c.score||a(c.completion,f.completion))){let c=h.completion;!l||l.label!=c.label||l.detail!=c.detail||l.type!=null&&c.type!=null&&l.type!=c.type||l.apply!=c.apply||l.boost!=c.boost?o.push(h):la(h.completion)>la(l)&&(o[o.length-1]=h),l=h.completion}return o}class _e{constructor(t,e,i,n,r,o){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=r,this.disabled=o}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new _e(this.options,aa(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,r,o){if(n&&!o&&t.some(h=>h.isPending))return n.setDisabled();let l=I0(t,e);if(!l.length)return n&&t.some(h=>h.isPending)?n.setDisabled():null;let a=e.facet(nt).selectOnOpen?0:-1;if(n&&n.selected!=a&&n.selected!=-1){let h=n.options[n.selected].completion;for(let c=0;c<l.length;c++)if(l[c].completion==h){a=c;break}}return new _e(l,aa(i,a),{pos:t.reduce((h,c)=>c.hasResult()?Math.min(h,c.from):h,1e8),create:W0,above:r.aboveCursor},n?n.timestamp:Date.now(),a,!1)}map(t){return new _e(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new _e(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class en{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new en(V0,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(t){let{state:e}=t,i=e.facet(nt),r=(i.override||e.languageDataAt("autocomplete",Be(e)).map(O0)).map(a=>(this.active.find(c=>c.source==a)||new Bt(a,this.active.some(c=>c.state!=0)?1:0)).update(t,i));r.length==this.active.length&&r.every((a,h)=>a==this.active[h])&&(r=this.active);let o=this.open,l=t.effects.some(a=>a.is(ko));o&&t.docChanged&&(o=o.map(t.changes)),t.selection||r.some(a=>a.hasResult()&&t.changes.touchesRange(a.from,a.to))||!N0(r,this.active)||l?o=_e.build(r,e,this.id,o,i,l):o&&o.disabled&&!r.some(a=>a.isPending)&&(o=null),!o&&r.every(a=>!a.isPending)&&r.some(a=>a.hasResult())&&(r=r.map(a=>a.hasResult()?new Bt(a.source,0):a));for(let a of t.effects)a.is(gf)&&(o=o&&o.setSelected(a.value,this.id));return r==this.active&&o==this.open?this:new en(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?F0:z0}}function N0(s,t){if(s==t)return!0;for(let e=0,i=0;;){for(;e<s.length&&!s[e].hasResult();)e++;for(;i<t.length&&!t[i].hasResult();)i++;let n=e==s.length,r=i==t.length;if(n||r)return n==r;if(s[e++].result!=t[i++].result)return!1}}const F0={"aria-autocomplete":"list"},z0={};function aa(s,t){let e={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":s};return t>-1&&(e["aria-activedescendant"]=s+"-"+t),e}const V0=[];function mf(s,t){if(s.isUserEvent("input.complete")){let i=s.annotation(pf);if(i&&t.activateOnCompletion(i))return 12}let e=s.isUserEvent("input.type");return e&&t.activateOnTyping?5:e?1:s.isUserEvent("delete.backward")?2:s.selection?8:s.docChanged?16:0}class Bt{constructor(t,e,i=!1){this.source=t,this.state=e,this.explicit=i}hasResult(){return!1}get isPending(){return this.state==1}update(t,e){let i=mf(t,e),n=this;(i&8||i&16&&this.touches(t))&&(n=new Bt(n.source,0)),i&4&&n.state==0&&(n=new Bt(this.source,1)),n=n.updateFor(t,i);for(let r of t.effects)if(r.is(tn))n=new Bt(n.source,1,r.value);else if(r.is(Fi))n=new Bt(n.source,0);else if(r.is(ko))for(let o of r.value)o.source==n.source&&(n=o);return n}updateFor(t,e){return this.map(t.changes)}map(t){return this}touches(t){return t.changes.touchesRange(Be(t.state))}}class Ye extends Bt{constructor(t,e,i,n,r,o){super(t,3,e),this.limit=i,this.result=n,this.from=r,this.to=o}hasResult(){return!0}updateFor(t,e){var i;if(!(e&3))return this.map(t.changes);let n=this.result;n.map&&!t.changes.empty&&(n=n.map(n,t.changes));let r=t.changes.mapPos(this.from),o=t.changes.mapPos(this.to,1),l=Be(t.state);if(l>o||!n||e&2&&(Be(t.startState)==this.from||l<this.limit))return new Bt(this.source,e&4?1:0);let a=t.changes.mapPos(this.limit);return H0(n.validFor,t.state,r,o)?new Ye(this.source,this.explicit,a,n,r,o):n.update&&(n=n.update(n,r,o,new uf(t.state,l,!1)))?new Ye(this.source,this.explicit,a,n,n.from,(i=n.to)!==null&&i!==void 0?i:Be(t.state)):new Bt(this.source,1,this.explicit)}map(t){return t.empty?this:(this.result.map?this.result.map(this.result,t):this.result)?new Ye(this.source,this.explicit,t.mapPos(this.limit),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new Bt(this.source,0)}touches(t){return t.changes.touchesRange(this.from,this.to)}}function H0(s,t,e,i){if(!s)return!1;let n=t.sliceDoc(e,i);return typeof s=="function"?s(n,e,i,t):df(s,!0).test(n)}const ko=B.define({map(s,t){return s.map(e=>e.map(t))}}),gf=B.define(),xt=it.define({create(){return en.start()},update(s,t){return s.update(t)},provide:s=>[cn.from(s,t=>t.tooltip),O.contentAttributes.from(s,t=>t.attrs)]});function So(s,t){const e=t.completion.apply||t.completion.label;let i=s.state.field(xt).active.find(n=>n.source==t.source);return i instanceof Ye?(typeof e=="string"?s.dispatch(Object.assign(Object.assign({},M0(s.state,e,i.from,i.to)),{annotations:pf.of(t.completion)})):e(s,t.completion,i.from,i.to),!0):!1}const W0=E0(xt,So);function gs(s,t="option"){return e=>{let i=e.state.field(xt,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<e.state.facet(nt).interactionDelay)return!1;let n=1,r;t=="page"&&(r=Yh(e,i.open.tooltip))&&(n=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+n*(s?1:-1):s?0:o-1;return l<0?l=t=="page"?0:o-1:l>=o&&(l=t=="page"?o-1:0),e.dispatch({effects:gf.of(l)}),!0}}const $0=s=>{let t=s.state.field(xt,!1);return s.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<s.state.facet(nt).interactionDelay?!1:So(s,t.open.options[t.open.selected])},ha=s=>s.state.field(xt,!1)?(s.dispatch({effects:tn.of(!0)}),!0):!1,q0=s=>{let t=s.state.field(xt,!1);return!t||!t.active.some(e=>e.state!=0)?!1:(s.dispatch({effects:Fi.of(null)}),!0)};class j0{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const _0=50,K0=1e3,U0=Z.fromClass(class{constructor(s){this.view=s,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let t of s.state.field(xt).active)t.isPending&&this.startQuery(t)}update(s){let t=s.state.field(xt),e=s.state.facet(nt);if(!s.selectionSet&&!s.docChanged&&s.startState.field(xt)==t)return;let i=s.transactions.some(r=>{let o=mf(r,e);return o&8||(r.selection||r.docChanged)&&!(o&3)});for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.context.abortOnDocChange&&s.docChanged||o.updates.length+s.transactions.length>_0&&Date.now()-o.time>K0){for(let l of o.context.abortListeners)try{l()}catch(a){wt(this.view.state,a)}o.context.abortListeners=null,this.running.splice(r--,1)}else o.updates.push(...s.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),s.transactions.some(r=>r.effects.some(o=>o.is(tn)))&&(this.pendingStart=!0);let n=this.pendingStart?50:e.activateOnTypingDelay;if(this.debounceUpdate=t.active.some(r=>r.isPending&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),n):-1,this.composing!=0)for(let r of s.transactions)r.isUserEvent("input.type")?this.composing=2:this.composing==2&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:s}=this.view,t=s.field(xt);for(let e of t.active)e.isPending&&!this.running.some(i=>i.active.source==e.source)&&this.startQuery(e);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(nt).updateSyncTime))}startQuery(s){let{state:t}=this.view,e=Be(t),i=new uf(t,e,s.explicit,this.view),n=new j0(s,i);this.running.push(n),Promise.resolve(s.source(i)).then(r=>{n.context.aborted||(n.done=r||null,this.scheduleAccept())},r=>{this.view.dispatch({effects:Fi.of(null)}),wt(this.view.state,r)})}scheduleAccept(){this.running.every(s=>s.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(nt).updateSyncTime))}accept(){var s;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],e=this.view.state.facet(nt),i=this.view.state.field(xt);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(r.done===void 0)continue;if(this.running.splice(n--,1),r.done){let l=Be(r.updates.length?r.updates[0].startState:this.view.state),a=Math.min(l,r.done.from+(r.active.explicit?0:1)),h=new Ye(r.active.source,r.active.explicit,a,r.done,r.done.from,(s=r.done.to)!==null&&s!==void 0?s:l);for(let c of r.updates)h=h.update(c,e);if(h.hasResult()){t.push(h);continue}}let o=i.active.find(l=>l.source==r.active.source);if(o&&o.isPending)if(r.done==null){let l=new Bt(r.active.source,0);for(let a of r.updates)l=l.update(a,e);l.isPending||t.push(l)}else this.startQuery(o)}(t.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:ko.of(t)})}},{eventHandlers:{blur(s){let t=this.view.state.field(xt,!1);if(t&&t.tooltip&&this.view.state.facet(nt).closeOnBlur){let e=t.open&&Yh(this.view,t.open.tooltip);(!e||!e.dom.contains(s.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:Fi.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:tn.of(!1)}),20),this.composing=0}}}),J0=typeof navigator=="object"&&/Win/.test(navigator.platform),G0=ze.highest(O.domEventHandlers({keydown(s,t){let e=t.state.field(xt,!1);if(!e||!e.open||e.open.disabled||e.open.selected<0||s.key.length>1||s.ctrlKey&&!(J0&&s.altKey)||s.metaKey)return!1;let i=e.open.options[e.open.selected],n=e.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||n.result.commitCharacters;return r&&r.indexOf(s.key)>-1&&So(t,i),!1}})),Y0=O.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}}),zi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Pe=B.define({map(s,t){let e=t.mapPos(s,-1,pt.TrackAfter);return e??void 0}}),Co=new class extends Ee{};Co.startSide=1;Co.endSide=-1;const bf=it.define({create(){return F.empty},update(s,t){if(s=s.map(t.changes),t.selection){let e=t.state.doc.lineAt(t.selection.main.head);s=s.update({filter:i=>i>=e.from&&i<=e.to})}for(let e of t.effects)e.is(Pe)&&(s=s.update({add:[Co.range(e.value,e.value+1)]}));return s}});function Q0(){return[Z0,bf]}const _n="()[]{}<>«»»«［］｛｝";function yf(s){for(let t=0;t<_n.length;t+=2)if(_n.charCodeAt(t)==s)return _n.charAt(t+1);return _r(s<128?s:s+1)}function xf(s,t){return s.languageDataAt("closeBrackets",t)[0]||zi}const X0=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),Z0=O.inputHandler.of((s,t,e,i)=>{if((X0?s.composing:s.compositionStarted)||s.state.readOnly)return!1;let n=s.state.selection.main;if(i.length>2||i.length==2&&Gt(bt(i,0))==1||t!=n.from||e!=n.to)return!1;let r=ib(s.state,i);return r?(s.dispatch(r),!0):!1}),tb=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let i=xf(s,s.selection.main.head).brackets||zi.brackets,n=null,r=s.changeByRange(o=>{if(o.empty){let l=sb(s.doc,o.head);for(let a of i)if(a==l&&xn(s.doc,o.head)==yf(bt(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:x.cursor(o.head-a.length)}}return{range:n=o}});return n||t(s.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!n},eb=[{key:"Backspace",run:tb}];function ib(s,t){let e=xf(s,s.selection.main.head),i=e.brackets||zi.brackets;for(let n of i){let r=yf(bt(n,0));if(t==n)return r==n?ob(s,n,i.indexOf(n+n+n)>-1,e):nb(s,n,r,e.before||zi.before);if(t==r&&wf(s,s.selection.main.from))return rb(s,n,r)}return null}function wf(s,t){let e=!1;return s.field(bf).between(0,s.doc.length,i=>{i==t&&(e=!0)}),e}function xn(s,t){let e=s.sliceString(t,t+2);return e.slice(0,Gt(bt(e,0)))}function sb(s,t){let e=s.sliceString(t-2,t);return Gt(bt(e,0))==e.length?e:e.slice(1)}function nb(s,t,e,i){let n=null,r=s.changeByRange(o=>{if(!o.empty)return{changes:[{insert:t,from:o.from},{insert:e,from:o.to}],effects:Pe.of(o.to+t.length),range:x.range(o.anchor+t.length,o.head+t.length)};let l=xn(s.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:t+e,from:o.head},effects:Pe.of(o.head+t.length),range:x.cursor(o.head+t.length)}:{range:n=o}});return n?null:s.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function rb(s,t,e){let i=null,n=s.changeByRange(r=>r.empty&&xn(s.doc,r.head)==e?{changes:{from:r.head,to:r.head+e.length,insert:e},range:x.cursor(r.head+e.length)}:i={range:r});return i?null:s.update(n,{scrollIntoView:!0,userEvent:"input.type"})}function ob(s,t,e,i){let n=i.stringPrefixes||zi.stringPrefixes,r=null,o=s.changeByRange(l=>{if(!l.empty)return{changes:[{insert:t,from:l.from},{insert:t,from:l.to}],effects:Pe.of(l.to+t.length),range:x.range(l.anchor+t.length,l.head+t.length)};let a=l.head,h=xn(s.doc,a),c;if(h==t){if(ca(s,a))return{changes:{insert:t+t,from:a},effects:Pe.of(a+t.length),range:x.cursor(a+t.length)};if(wf(s,a)){let u=e&&s.sliceDoc(a,a+t.length*3)==t+t+t?t+t+t:t;return{changes:{from:a,to:a+u.length,insert:u},range:x.cursor(a+u.length)}}}else{if(e&&s.sliceDoc(a-2*t.length,a)==t+t&&(c=fa(s,a-2*t.length,n))>-1&&ca(s,c))return{changes:{insert:t+t+t+t,from:a},effects:Pe.of(a+t.length),range:x.cursor(a+t.length)};if(s.charCategorizer(a)(h)!=G.Word&&fa(s,a,n)>-1&&!lb(s,a,t,n))return{changes:{insert:t+t,from:a},effects:Pe.of(a+t.length),range:x.cursor(a+t.length)}}return{range:r=l}});return r?null:s.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function ca(s,t){let e=ft(s).resolveInner(t+1);return e.parent&&e.from==t}function lb(s,t,e,i){let n=ft(s).resolveInner(t,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=s.sliceDoc(n.from,Math.min(n.to,n.from+e.length+r)),a=l.indexOf(e);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=n.firstChild;for(;c&&c.from==n.from&&c.to-c.from>e.length+a;){if(s.sliceDoc(c.to-e.length,c.to)==e)return!1;c=c.firstChild}return!0}let h=n.to==t&&n.parent;if(!h)break;n=h}return!1}function fa(s,t,e){let i=s.charCategorizer(t);if(i(s.sliceDoc(t-1,t))!=G.Word)return t;for(let n of e){let r=t-n.length;if(s.sliceDoc(r,t)==n&&i(s.sliceDoc(r-1,r))!=G.Word)return r}return-1}function ab(s={}){return[G0,xt,nt.of(s),U0,hb,Y0]}const vf=[{key:"Ctrl-Space",run:ha},{mac:"Alt-`",run:ha},{key:"Escape",run:q0},{key:"ArrowDown",run:gs(!0)},{key:"ArrowUp",run:gs(!1)},{key:"PageDown",run:gs(!0,"page")},{key:"PageUp",run:gs(!1,"page")},{key:"Enter",run:$0}],hb=ze.highest(ro.computeN([nt],s=>s.facet(nt).defaultKeymap?[vf]:[]));class ua{constructor(t,e,i){this.from=t,this.to=e,this.diagnostic=i}}class De{constructor(t,e,i){this.diagnostics=t,this.panel=e,this.selected=i}static init(t,e,i){let n=i.facet(Vi).markerFilter;n&&(t=n(t,i));let r=t.slice().sort((c,f)=>c.from-f.from||c.to-f.to),o=new le,l=[],a=0;for(let c=0;;){let f=c==r.length?null:r[c];if(!f&&!l.length)break;let u,d;for(l.length?(u=a,d=l.reduce((m,g)=>Math.min(m,g.to),f&&f.from>u?f.from:1e8)):(u=f.from,d=f.to,l.push(f),c++);c<r.length;){let m=r[c];if(m.from==u&&(m.to>m.from||m.to==u))l.push(m),c++,d=Math.min(m.to,d);else{d=Math.min(m.from,d);break}}let p=Of(l);if(l.some(m=>m.from==m.to||m.from==m.to-1&&i.doc.lineAt(m.from).to==m.from))o.add(u,u,P.widget({widget:new gb(p),diagnostics:l.slice()}));else{let m=l.reduce((g,b)=>b.markClass?g+" "+b.markClass:g,"");o.add(u,d,P.mark({class:"cm-lintRange cm-lintRange-"+p+m,diagnostics:l.slice(),inclusiveEnd:l.some(g=>g.to>d)}))}a=d;for(let m=0;m<l.length;m++)l[m].to<=a&&l.splice(m--,1)}let h=o.finish();return new De(h,e,ni(h))}}function ni(s,t=null,e=0){let i=null;return s.between(e,1e9,(n,r,{spec:o})=>{if(!(t&&o.diagnostics.indexOf(t)<0))if(!i)i=new ua(n,r,t||o.diagnostics[0]);else{if(o.diagnostics.indexOf(i.diagnostic)<0)return!1;i=new ua(i.from,r,i.diagnostic)}}),i}function kf(s,t){let e=t.pos,i=t.end||e,n=s.state.facet(Vi).hideOn(s,e,i);if(n!=null)return n;let r=s.startState.doc.lineAt(t.pos);return!!(s.effects.some(o=>o.is(Ao))||s.changes.touchesRange(r.from,Math.max(r.to,i)))}function cb(s,t){return s.field(Ot,!1)?t:t.concat(B.appendConfig.of(Cb))}const Ao=B.define(),Mo=B.define(),Sf=B.define(),Ot=it.define({create(){return new De(P.none,null,null)},update(s,t){if(t.docChanged&&s.diagnostics.size){let e=s.diagnostics.map(t.changes),i=null,n=s.panel;if(s.selected){let r=t.changes.mapPos(s.selected.from,1);i=ni(e,s.selected.diagnostic,r)||ni(e,null,r)}!e.size&&n&&t.state.facet(Vi).autoPanel&&(n=null),s=new De(e,n,i)}for(let e of t.effects)if(e.is(Ao)){let i=t.state.facet(Vi).autoPanel?e.value.length?Hi.open:null:s.panel;s=De.init(e.value,i,t.state)}else e.is(Mo)?s=new De(s.diagnostics,e.value?Hi.open:null,s.selected):e.is(Sf)&&(s=new De(s.diagnostics,s.panel,e.value));return s},provide:s=>[Ei.from(s,t=>t.panel),O.decorations.from(s,t=>t.diagnostics)]}),fb=P.mark({class:"cm-lintRange cm-lintRange-active"});function ub(s,t,e){let{diagnostics:i}=s.state.field(Ot),n,r=-1,o=-1;i.between(t-(e<0?1:0),t+(e>0?1:0),(a,h,{spec:c})=>{if(t>=a&&t<=h&&(a==h||(t>a||e>0)&&(t<h||e<0)))return n=c.diagnostics,r=a,o=h,!1});let l=s.state.facet(Vi).tooltipFilter;return n&&l&&(n=l(n,s.state)),n?{pos:r,end:o,above:s.state.doc.lineAt(r).to<o,create(){return{dom:Cf(s,n)}}}:null}function Cf(s,t){return q("ul",{class:"cm-tooltip-lint"},t.map(e=>Mf(s,e,!1)))}const db=s=>{let t=s.state.field(Ot,!1);(!t||!t.panel)&&s.dispatch({effects:cb(s.state,[Mo.of(!0)])});let e=Bi(s,Hi.open);return e&&e.dom.querySelector(".cm-panel-lint ul").focus(),!0},da=s=>{let t=s.state.field(Ot,!1);return!t||!t.panel?!1:(s.dispatch({effects:Mo.of(!1)}),!0)},pb=s=>{let t=s.state.field(Ot,!1);if(!t)return!1;let e=s.state.selection.main,i=t.diagnostics.iter(e.to+1);return!i.value&&(i=t.diagnostics.iter(0),!i.value||i.from==e.from&&i.to==e.to)?!1:(s.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},mb=[{key:"Mod-Shift-m",run:db,preventDefault:!0},{key:"F8",run:pb}],Vi=D.define({combine(s){return Object.assign({sources:s.map(t=>t.source).filter(t=>t!=null)},Ht(s.map(t=>t.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(t,e)=>t?e?i=>t(i)||e(i):t:e}))}});function Af(s){let t=[];if(s)t:for(let{name:e}of s){for(let i=0;i<e.length;i++){let n=e[i];if(/[a-zA-Z]/.test(n)&&!t.some(r=>r.toLowerCase()==n.toLowerCase())){t.push(n);continue t}}t.push("")}return t}function Mf(s,t,e){var i;let n=e?Af(t.actions):[];return q("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},q("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(s):t.message),(i=t.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=ni(s.state.field(Ot).diagnostics,t);d&&r.apply(s,d.from,d.to)},{name:h}=r,c=n[o]?h.indexOf(n[o]):-1,f=c<0?h:[h.slice(0,c),q("u",h.slice(c,c+1)),h.slice(c+1)];return q("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${c<0?"":` (access key "${n[o]})"`}.`},f)}),t.source&&q("div",{class:"cm-diagnosticSource"},t.source))}class gb extends Se{constructor(t){super(),this.sev=t}eq(t){return t.sev==this.sev}toDOM(){return q("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class pa{constructor(t,e){this.diagnostic=e,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=Mf(t,e,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Hi{constructor(t){this.view=t,this.items=[];let e=n=>{if(n.keyCode==27)da(this.view),this.view.focus();else if(n.keyCode==38||n.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(n.keyCode==40||n.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(n.keyCode==36)this.moveSelection(0);else if(n.keyCode==35)this.moveSelection(this.items.length-1);else if(n.keyCode==13)this.view.focus();else if(n.keyCode>=65&&n.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=Af(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==n.keyCode){let a=ni(this.view.state.field(Ot).diagnostics,r);a&&r.actions[l].apply(t,a.from,a.to)}}else return;n.preventDefault()},i=n=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(n.target)&&this.moveSelection(r)};this.list=q("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:e,onclick:i}),this.dom=q("div",{class:"cm-panel-lint"},this.list,q("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>da(this.view)},"×")),this.update()}get selectedIndex(){let t=this.view.state.field(Ot).selected;if(!t)return-1;for(let e=0;e<this.items.length;e++)if(this.items[e].diagnostic==t.diagnostic)return e;return-1}update(){let{diagnostics:t,selected:e}=this.view.state.field(Ot),i=0,n=!1,r=null,o=new Set;for(t.between(0,this.view.state.doc.length,(l,a,{spec:h})=>{for(let c of h.diagnostics){if(o.has(c))continue;o.add(c);let f=-1,u;for(let d=i;d<this.items.length;d++)if(this.items[d].diagnostic==c){f=d;break}f<0?(u=new pa(this.view,c),this.items.splice(i,0,u),n=!0):(u=this.items[f],f>i&&(this.items.splice(i,f-i),n=!0)),e&&u.diagnostic==e.diagnostic?u.dom.hasAttribute("aria-selected")||(u.dom.setAttribute("aria-selected","true"),r=u):u.dom.hasAttribute("aria-selected")&&u.dom.removeAttribute("aria-selected"),i++}});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)n=!0,this.items.pop();this.items.length==0&&(this.items.push(new pa(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:l,panel:a})=>{let h=a.height/this.list.offsetHeight;l.top<a.top?this.list.scrollTop-=(a.top-l.top)/h:l.bottom>a.bottom&&(this.list.scrollTop+=(l.bottom-a.bottom)/h)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync()}sync(){let t=this.list.firstChild;function e(){let i=t;t=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;t!=i.dom;)e();t=i.dom.nextSibling}else this.list.insertBefore(i.dom,t);for(;t;)e()}moveSelection(t){if(this.selectedIndex<0)return;let e=this.view.state.field(Ot),i=ni(e.diagnostics,this.items[t].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Sf.of(i)})}static open(t){return new Hi(t)}}function Ts(s,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(s)}</svg>')`}function bs(s){return Ts(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${s}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const bb=O.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:bs("#d11")},".cm-lintRange-warning":{backgroundImage:bs("orange")},".cm-lintRange-info":{backgroundImage:bs("#999")},".cm-lintRange-hint":{backgroundImage:bs("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function yb(s){return s=="error"?4:s=="warning"?3:s=="info"?2:1}function Of(s){let t="hint",e=1;for(let i of s){let n=yb(i.severity);n>e&&(e=n,t=i.severity)}return t}class Df extends ee{constructor(t){super(),this.diagnostics=t,this.severity=Of(t)}toDOM(t){let e=document.createElement("div");e.className="cm-lint-marker cm-lint-marker-"+this.severity;let i=this.diagnostics,n=t.state.facet(wn).tooltipFilter;return n&&(i=n(i,t.state)),i.length&&(e.onmouseover=()=>wb(t,e,i)),e}}function xb(s,t){let e=i=>{let n=t.getBoundingClientRect();if(!(i.clientX>n.left-10&&i.clientX<n.right+10&&i.clientY>n.top-10&&i.clientY<n.bottom+10)){for(let r=i.target;r;r=r.parentNode)if(r.nodeType==1&&r.classList.contains("cm-tooltip-lint"))return;window.removeEventListener("mousemove",e),s.state.field(Tf)&&s.dispatch({effects:Oo.of(null)})}};window.addEventListener("mousemove",e)}function wb(s,t,e){function i(){let o=s.elementAtHeight(t.getBoundingClientRect().top+5-s.documentTop);s.coordsAtPos(o.from)&&s.dispatch({effects:Oo.of({pos:o.from,above:!1,clip:!1,create(){return{dom:Cf(s,e),getCoords:()=>t.getBoundingClientRect()}}})}),t.onmouseout=t.onmousemove=null,xb(s,t)}let{hoverTime:n}=s.state.facet(wn),r=setTimeout(i,n);t.onmouseout=()=>{clearTimeout(r),t.onmouseout=t.onmousemove=null},t.onmousemove=()=>{clearTimeout(r),r=setTimeout(i,n)}}function vb(s,t){let e=Object.create(null);for(let n of t){let r=s.lineAt(n.from);(e[r.from]||(e[r.from]=[])).push(n)}let i=[];for(let n in e)i.push(new Df(e[n]).range(+n));return F.of(i,!0)}const kb=Xh({class:"cm-gutter-lint",markers:s=>s.state.field(qr),widgetMarker:(s,t,e)=>{let i=[];return s.state.field(qr).between(e.from,e.to,(n,r,o)=>{n>e.from&&n<e.to&&i.push(...o.diagnostics)}),i.length?new Df(i):null}}),qr=it.define({create(){return F.empty},update(s,t){s=s.map(t.changes);let e=t.state.facet(wn).markerFilter;for(let i of t.effects)if(i.is(Ao)){let n=i.value;e&&(n=e(n||[],t.state)),s=vb(t.state.doc,n.slice(0))}return s}}),Oo=B.define(),Tf=it.define({create(){return null},update(s,t){return s&&t.docChanged&&(s=kf(t,s)?null:Object.assign(Object.assign({},s),{pos:t.changes.mapPos(s.pos)})),t.effects.reduce((e,i)=>i.is(Oo)?i.value:e,s)},provide:s=>cn.from(s)}),Sb=O.baseTheme({".cm-gutter-lint":{width:"1.4em","& .cm-gutterElement":{padding:".2em"}},".cm-lint-marker":{width:"1em",height:"1em"},".cm-lint-marker-info":{content:Ts('<path fill="#aaf" stroke="#77e" stroke-width="6" stroke-linejoin="round" d="M5 5L35 5L35 35L5 35Z"/>')},".cm-lint-marker-warning":{content:Ts('<path fill="#fe8" stroke="#fd7" stroke-width="6" stroke-linejoin="round" d="M20 6L37 35L3 35Z"/>')},".cm-lint-marker-error":{content:Ts('<circle cx="20" cy="20" r="15" fill="#f87" stroke="#f43" stroke-width="6"/>')}}),Cb=[Ot,O.decorations.compute([Ot],s=>{let{selected:t,panel:e}=s.field(Ot);return!t||!e||t.from==t.to?P.none:P.set([fb.range(t.from,t.to)])}),yp(ub,{hideOn:kf}),bb],wn=D.define({combine(s){return Ht(s,{hoverTime:300,markerFilter:null,tooltipFilter:null})}});function Ab(s={}){return[wn.of(s),qr,kb,Sb,Tf]}const Mb=(()=>[Dp(),Rp(),Gd(),Jm(),vm(),zd(),qd(),V.allowMultipleSelections.of(!0),lm(),wc(Am,{fallback:!0}),Bm(),Q0(),ab(),lp(),cp(),ep(),e0(),ro.of([...eb,...Yg,...w0,...sg,...bm,...vf,...mb])])();class sn{constructor(t,e,i,n,r,o,l,a,h,c=0,f){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=r,this.score=o,this.buffer=l,this.bufferBase=a,this.curContext=h,this.lookAhead=c,this.parent=f}toString(){return`[${this.stack.filter((t,e)=>e%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new sn(t,[],e,i,i,0,[],0,n?new ma(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var e;let i=t>>19,n=t&65535,{parser:r}=this.p,o=this.reducePos<this.pos-25;o&&this.setLookAhead(this.pos);let l=r.dynamicPrecedence(n);if(l&&(this.score+=l),i==0){this.pushState(r.getGoto(this.state,n,!0),this.reducePos),n<r.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,o?8:4,!0),this.reduceContext(n,this.reducePos);return}let a=this.stack.length-(i-1)*3-(t&262144?6:0),h=a?this.stack[a-2]:this.p.ranges[0].from,c=this.reducePos-h;c>=2e3&&!(!((e=this.p.parser.nodeSet.types[n])===null||e===void 0)&&e.isAnonymous)&&(h==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=c):this.p.lastBigReductionSize<c&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=h,this.p.lastBigReductionSize=c));let f=a?this.stack[a-1]:0,u=this.bufferBase+this.buffer.length-f;if(n<r.minRepeatTerm||t&131072){let d=r.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,h,d,u+4,!0)}if(t&262144)this.state=this.stack[a];else{let d=this.stack[a-3];this.state=r.getGoto(d,n,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(n,h)}storeNode(t,e,i,n=4,r=!1){if(t==0&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let o=this,l=this.buffer.length;if(l==0&&o.parent&&(l=o.bufferBase-o.parent.bufferBase,o=o.parent),l>0&&o.buffer[l-4]==0&&o.buffer[l-1]>-1){if(e==i)return;if(o.buffer[l-2]>=e){o.buffer[l-2]=i;return}}}if(!r||this.pos==i)this.buffer.push(t,e,i,n);else{let o=this.buffer.length;if(o>0&&this.buffer[o-4]!=0){let l=!1;for(let a=o;a>0&&this.buffer[a-2]>i;a-=4)if(this.buffer[a-1]>=0){l=!0;break}if(l)for(;o>0&&this.buffer[o-2]>i;)this.buffer[o]=this.buffer[o-4],this.buffer[o+1]=this.buffer[o-3],this.buffer[o+2]=this.buffer[o-2],this.buffer[o+3]=this.buffer[o-1],o-=4,n>4&&(n-=4)}this.buffer[o]=t,this.buffer[o+1]=e,this.buffer[o+2]=i,this.buffer[o+3]=n}}shift(t,e,i,n){if(t&131072)this.pushState(t&65535,this.pos);else if(t&262144)this.pos=n,this.shiftContext(e,i),e<=this.p.parser.maxNode&&this.buffer.push(e,i,n,4);else{let r=t,{parser:o}=this.p;(n>this.pos||e<=o.maxNode)&&(this.pos=n,o.stateFlag(r,1)||(this.reducePos=n)),this.pushState(r,i),this.shiftContext(e,i),e<=o.maxNode&&this.buffer.push(e,i,n,4)}}apply(t,e,i,n){t&65536?this.reduce(t):this.shift(t,e,i,n)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new sn(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new Ob(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(i==0)return!1;if(!(i&65536))return!0;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let n=[];for(let r=0,o;r<e.length;r+=2)(o=e[r+1])!=this.state&&this.p.parser.hasAction(o,t)&&n.push(e[r],o);if(this.stack.length<120)for(let r=0;n.length<8&&r<e.length;r+=2){let o=e[r+1];n.some((l,a)=>a&1&&l==o)||n.push(e[r],o)}e=n}let i=[];for(let n=0;n<e.length&&i.length<4;n+=2){let r=e[n+1];if(r==this.state)continue;let o=this.split();o.pushState(r,this.pos),o.storeNode(0,o.pos,o.pos,4,!0),o.shiftContext(e[n],this.pos),o.reducePos=this.pos,o.score-=200,i.push(o)}return i}forceReduce(){let{parser:t}=this.p,e=t.stateSlot(this.state,5);if(!(e&65536))return!1;if(!t.validAction(this.state,e)){let i=e>>19,n=e&65535,r=this.stack.length-i*3;if(r<0||t.getGoto(this.stack[r],n,!1)<0){let o=this.findForcedReduction();if(o==null)return!1;e=o}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(e),!0}findForcedReduction(){let{parser:t}=this.p,e=[],i=(n,r)=>{if(!e.includes(n))return e.push(n),t.allActions(n,o=>{if(!(o&393216))if(o&65536){let l=(o>>19)-r;if(l>1){let a=o&65535,h=this.stack.length-l*3;if(h>=0&&t.getGoto(this.stack[h],a,!1)>=0)return l<<19|65536|a}}else{let l=i(o,r+1);if(l!=null)return l}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(this.stack.length!=3)return!1;let{parser:t}=this.p;return t.data[t.stateSlot(this.state,1)]==65535&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-3)&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-4)&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new ma(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class ma{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}class Ob{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=t&65535,i=t>>19;i==0?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(i-1)*3;let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class nn{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,this.index==0&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new nn(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;t!=null&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,this.index==0&&this.maybeNext()}fork(){return new nn(this.stack,this.pos,this.index)}}function ys(s,t=Uint16Array){if(typeof s!="string")return s;let e=null;for(let i=0,n=0;i<s.length;){let r=0;for(;;){let o=s.charCodeAt(i++),l=!1;if(o==126){r=65535;break}o>=92&&o--,o>=34&&o--;let a=o-32;if(a>=46&&(a-=46,l=!0),r+=a,l)break;r*=46}e?e[n++]=r:e=new t(r)}return e}class Ps{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const ga=new Ps;class Db{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=ga,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,r=this.pos+t;for(;r<i.from;){if(!n)return null;let o=this.ranges[--n];r-=i.from-o.to,i=o}for(;e<0?r>i.to:r>=i.to;){if(n==this.ranges.length-1)return null;let o=this.ranges[++n];r+=o.from-i.to,i=o}return r}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let e of this.ranges)if(e.to>t)return Math.max(t,e.from);return this.end}peek(t){let e=this.chunkOff+t,i,n;if(e>=0&&e<this.chunk.length)i=this.pos+t,n=this.chunk.charCodeAt(e);else{let r=this.resolveOffset(t,1);if(r==null)return-1;if(i=r,i>=this.chunk2Pos&&i<this.chunk2Pos+this.chunk2.length)n=this.chunk2.charCodeAt(i-this.chunk2Pos);else{let o=this.rangeIndex,l=this.range;for(;l.to<=i;)l=this.ranges[++o];this.chunk2=this.input.chunk(this.chunk2Pos=i),i+this.chunk2.length>l.to&&(this.chunk2=this.chunk2.slice(0,l.to-i)),n=this.chunk2.charCodeAt(0)}}return i>=this.token.lookAhead&&(this.token.lookAhead=i+1),n}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(i==null||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}acceptTokenTo(t,e){this.token.value=t,this.token.end=e}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=ga,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class Qe{constructor(t,e){this.data=t,this.id=e}token(t,e){let{parser:i}=e.p;Tb(this.data,t,e,this.id,i.data,i.tokenPrecTable)}}Qe.prototype.contextual=Qe.prototype.fallback=Qe.prototype.extend=!1;Qe.prototype.fallback=Qe.prototype.extend=!1;function Tb(s,t,e,i,n,r){let o=0,l=1<<i,{dialect:a}=e.p.parser;t:for(;l&s[o];){let h=s[o+1];for(let d=o+3;d<h;d+=2)if((s[d+1]&l)>0){let p=s[d];if(a.allows(p)&&(t.token.value==-1||t.token.value==p||Pb(p,t.token.value,n,r))){t.acceptToken(p);break}}let c=t.next,f=0,u=s[o+2];if(t.next<0&&u>f&&s[h+u*3-3]==65535){o=s[h+u*3-1];continue t}for(;f<u;){let d=f+u>>1,p=h+d+(d<<1),m=s[p],g=s[p+1]||65536;if(c<m)u=d;else if(c>=g)f=d+1;else{o=s[p+2],t.advance();continue t}}break}}function ba(s,t,e){for(let i=t,n;(n=s[i])!=65535;i++)if(n==e)return i-t;return-1}function Pb(s,t,e,i){let n=ba(e,i,t);return n<0||ba(e,i,s)<n}const At=typeof process<"u"&&process.env&&/\bparse\b/.test({}.LOG);let Kn=null;function ya(s,t,e){let i=s.cursor(st.IncludeAnonymous);for(i.moveTo(t);;)if(!(e<0?i.childBefore(t):i.childAfter(t)))for(;;){if((e<0?i.to<t:i.from>t)&&!i.type.isError)return e<0?Math.max(0,Math.min(i.to-1,t-25)):Math.min(s.length,Math.max(i.from+1,t+25));if(e<0?i.prevSibling():i.nextSibling())break;if(!i.parent())return e<0?0:s.length}}class Rb{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?ya(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?ya(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],n=this.index[e];if(n==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[n],o=this.start[e]+i.positions[n];if(o>t)return this.nextStart=o,null;if(r instanceof X){if(o==t){if(o<this.safeFrom)return null;let l=o+r.length;if(l<=this.safeTo){let a=r.prop(L.lookAhead);if(!a||l+a<this.fragment.to)return r}}this.index[e]++,o+r.length>=Math.max(this.safeFrom,t)&&(this.trees.push(r),this.start.push(o),this.index.push(0))}else this.index[e]++,this.nextStart=o+r.length}}}class Bb{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map(i=>new Ps)}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:r}=n,o=n.stateSlot(t.state,3),l=t.curContext?t.curContext.hash:0,a=0;for(let h=0;h<r.length;h++){if(!(1<<h&o))continue;let c=r[h],f=this.tokens[h];if(!(i&&!c.fallback)&&((c.contextual||f.start!=t.pos||f.mask!=o||f.context!=l)&&(this.updateCachedToken(f,c,t),f.mask=o,f.context=l),f.lookAhead>f.end+25&&(a=Math.max(f.lookAhead,a)),f.value!=0)){let u=e;if(f.extended>-1&&(e=this.addActions(t,f.extended,f.end,e)),e=this.addActions(t,f.value,f.end,e),!c.extend&&(i=f,e>u))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),!i&&t.pos==this.stream.end&&(i=new Ps,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new Ps,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){let n=this.stream.clipPos(i.pos);if(e.token(this.stream.reset(n,t),i),t.value>-1){let{parser:r}=i.p;for(let o=0;o<r.specialized.length;o++)if(r.specialized[o]==t.value){let l=r.specializers[o](this.stream.read(t.start,t.end),i);if(l>=0&&i.p.parser.dialect.allows(l>>1)){l&1?t.extended=l>>1:t.value=l>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,e,i,n){for(let r=0;r<n;r+=3)if(this.actions[r]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:r}=t,{parser:o}=t.p,{data:l}=o;for(let a=0;a<2;a++)for(let h=o.stateSlot(r,a?2:1);;h+=3){if(l[h]==65535)if(l[h+1]==1)h=ne(l,h+2);else{n==0&&l[h+1]==2&&(n=this.putAction(ne(l,h+2),e,i,n));break}l[h]==e&&(n=this.putAction(ne(l,h+1),e,i,n))}return n}}class Eb{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new Db(e,n),this.tokens=new Bb(t,this.stream),this.topTerm=t.top[1];let{from:r}=n[0];this.stacks=[sn.start(this,t.top[0],r)],this.fragments=i.length&&this.stream.end-r>t.bufferLength*4?new Rb(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t=this.stacks,e=this.minStackPos,i=this.stacks=[],n,r;if(this.bigReductionCount>300&&t.length==1){let[o]=t;for(;o.forceReduce()&&o.stack.length&&o.stack[o.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let o=0;o<t.length;o++){let l=t[o];for(;;){if(this.tokens.mainToken=null,l.pos>e)i.push(l);else{if(this.advanceStack(l,i,t))continue;{n||(n=[],r=[]),n.push(l);let a=this.tokens.getMainToken(l);r.push(a.value,a.end)}}break}}if(!i.length){let o=n&&Ib(n);if(o)return At&&console.log("Finish with "+this.stackID(o)),this.stackToTree(o);if(this.parser.strict)throw At&&n&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+e);this.recovering||(this.recovering=5)}if(this.recovering&&n){let o=this.stoppedAt!=null&&n[0].pos>this.stoppedAt?n[0]:this.runRecovery(n,r,i);if(o)return At&&console.log("Force-finish "+this.stackID(o)),this.stackToTree(o.forceAll())}if(this.recovering){let o=this.recovering==1?1:this.recovering*3;if(i.length>o)for(i.sort((l,a)=>a.score-l.score);i.length>o;)i.pop();i.some(l=>l.reducePos>e)&&this.recovering--}else if(i.length>1){t:for(let o=0;o<i.length-1;o++){let l=i[o];for(let a=o+1;a<i.length;a++){let h=i[a];if(l.sameState(h)||l.buffer.length>500&&h.buffer.length>500)if((l.score-h.score||l.buffer.length-h.buffer.length)>0)i.splice(a--,1);else{i.splice(o--,1);continue t}}}i.length>12&&i.splice(12,i.length-12)}this.minStackPos=i[0].pos;for(let o=1;o<i.length;o++)i[o].pos<this.minStackPos&&(this.minStackPos=i[o].pos);return null}stopAt(t){if(this.stoppedAt!=null&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let n=t.pos,{parser:r}=this,o=At?this.stackID(t)+" -> ":"";if(this.stoppedAt!=null&&n>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let h=t.curContext&&t.curContext.tracker.strict,c=h?t.curContext.hash:0;for(let f=this.fragments.nodeAt(n);f;){let u=this.parser.nodeSet.types[f.type.id]==f.type?r.getGoto(t.state,f.type.id):-1;if(u>-1&&f.length&&(!h||(f.prop(L.contextHash)||0)==c))return t.useNode(f,u),At&&console.log(o+this.stackID(t)+` (via reuse of ${r.getName(f.type.id)})`),!0;if(!(f instanceof X)||f.children.length==0||f.positions[0]>0)break;let d=f.children[0];if(d instanceof X&&f.positions[0]==0)f=d;else break}}let l=r.stateSlot(t.state,4);if(l>0)return t.reduce(l),At&&console.log(o+this.stackID(t)+` (via always-reduce ${r.getName(l&65535)})`),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let a=this.tokens.getActions(t);for(let h=0;h<a.length;){let c=a[h++],f=a[h++],u=a[h++],d=h==a.length||!i,p=d?t:t.split(),m=this.tokens.mainToken;if(p.apply(c,f,m?m.start:p.pos,u),At&&console.log(o+this.stackID(p)+` (via ${c&65536?`reduce of ${r.getName(c&65535)}`:"shift"} for ${r.getName(f)} @ ${n}${p==t?"":", split"})`),d)return!0;p.pos>n?e.push(p):i.push(p)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return xa(t,e),!0}}runRecovery(t,e,i){let n=null,r=!1;for(let o=0;o<t.length;o++){let l=t[o],a=e[o<<1],h=e[(o<<1)+1],c=At?this.stackID(l)+" -> ":"";if(l.deadEnd&&(r||(r=!0,l.restart(),At&&console.log(c+this.stackID(l)+" (restarted)"),this.advanceFully(l,i))))continue;let f=l.split(),u=c;for(let d=0;f.forceReduce()&&d<10&&(At&&console.log(u+this.stackID(f)+" (via force-reduce)"),!this.advanceFully(f,i));d++)At&&(u=this.stackID(f)+" -> ");for(let d of l.recoverByInsert(a))At&&console.log(c+this.stackID(d)+" (via recover-insert)"),this.advanceFully(d,i);this.stream.end>l.pos?(h==l.pos&&(h++,a=0),l.recoverByDelete(a,h),At&&console.log(c+this.stackID(l)+` (via recover-delete ${this.parser.getName(a)})`),xa(l,i)):(!n||n.score<l.score)&&(n=l)}return n}stackToTree(t){return t.close(),X.build({buffer:nn.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(Kn||(Kn=new WeakMap)).get(t);return e||Kn.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function xa(s,t){for(let e=0;e<t.length;e++){let i=t[e];if(i.pos==s.pos&&i.sameState(s)){t[e].score<s.score&&(t[e]=s);return}}t.push(s)}class Lb{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||this.disabled[t]==0}}class rn extends rc{constructor(t){if(super(),this.wrappers=[],t.version!=14)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (14)`);let e=t.nodeNames.split(" ");this.minRepeatTerm=e.length;for(let l=0;l<t.repeatNodeCount;l++)e.push("");let i=Object.keys(t.topRules).map(l=>t.topRules[l][1]),n=[];for(let l=0;l<e.length;l++)n.push([]);function r(l,a,h){n[l].push([a,a.deserialize(String(h))])}if(t.nodeProps)for(let l of t.nodeProps){let a=l[0];typeof a=="string"&&(a=L[a]);for(let h=1;h<l.length;){let c=l[h++];if(c>=0)r(c,a,l[h++]);else{let f=l[h+-c];for(let u=-c;u>0;u--)r(l[h++],a,f);h++}}}this.nodeSet=new lo(e.map((l,a)=>kt.define({name:a>=this.minRepeatTerm?void 0:l,id:a,props:n[a],top:i.indexOf(a)>-1,error:a==0,skipped:t.skippedNodes&&t.skippedNodes.indexOf(a)>-1}))),t.propSources&&(this.nodeSet=this.nodeSet.extend(...t.propSources)),this.strict=!1,this.bufferLength=ec;let o=ys(t.tokenData);this.context=t.context,this.specializerSpecs=t.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let l=0;l<this.specializerSpecs.length;l++)this.specialized[l]=this.specializerSpecs[l].term;this.specializers=this.specializerSpecs.map(wa),this.states=ys(t.states,Uint32Array),this.data=ys(t.stateData),this.goto=ys(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map(l=>typeof l=="number"?new Qe(o,l):l),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new Eb(this,t,e,i);for(let r of this.wrappers)n=r(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let r=n[e+1];;){let o=n[r++],l=o&1,a=n[r++];if(l&&i)return a;for(let h=r+(o>>1);r<h;r++)if(n[r]==t)return a;if(l)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let r=this.stateSlot(t,n?2:1),o;;r+=3){if((o=i[r])==65535)if(i[r+1]==1)o=i[r=ne(i,r+2)];else{if(i[r+1]==2)return ne(i,r+2);break}if(o==e||o==0)return ne(i,r+1)}return 0}stateSlot(t,e){return this.states[t*6+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){return!!this.allActions(t,i=>i==e?!0:null)}allActions(t,e){let i=this.stateSlot(t,4),n=i?e(i):void 0;for(let r=this.stateSlot(t,1);n==null;r+=3){if(this.data[r]==65535)if(this.data[r+1]==1)r=ne(this.data,r+2);else break;n=e(ne(this.data,r+1))}return n}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(this.data[i]==65535)if(this.data[i+1]==1)i=ne(this.data,i+2);else break;if(!(this.data[i+2]&1)){let n=this.data[i+1];e.some((r,o)=>o&1&&r==n)||e.push(this.data[i],n)}}return e}configure(t){let e=Object.assign(Object.create(rn.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map(i=>{let n=t.tokenizers.find(r=>r.from==i);return n?n.to:i})),t.specializers&&(e.specializers=this.specializers.slice(),e.specializerSpecs=this.specializerSpecs.map((i,n)=>{let r=t.specializers.find(l=>l.from==i.external);if(!r)return i;let o=Object.assign(Object.assign({},i),{external:r.to});return e.specializers[n]=wa(o),o})),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),t.strict!=null&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),t.bufferLength!=null&&(e.bufferLength=t.bufferLength),e}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return e==null?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map(()=>!1);if(t)for(let r of t.split(" ")){let o=e.indexOf(r);o>=0&&(i[o]=!0)}let n=null;for(let r=0;r<e.length;r++)if(!i[r])for(let o=this.dialects[e[r]],l;(l=this.data[o++])!=65535;)(n||(n=new Uint8Array(this.maxTerm+1)))[l]=1;return new Lb(t,i,n)}static deserialize(t){return new rn(t)}}function ne(s,t){return s[t]|s[t+1]<<16}function Ib(s){let t=null;for(let e of s){let i=e.p.stoppedAt;(e.pos==e.p.stream.end||i!=null&&e.pos>i)&&e.p.parser.stateFlag(e.state,2)&&(!t||t.score<e.score)&&(t=e)}return t}function wa(s){if(s.external){let t=s.extend?1:0;return(e,i)=>s.external(e,i)<<1|t}return s.get}const Nb=oc({String:y.string,Number:y.number,"True False":y.bool,PropertyName:y.propertyName,Null:y.null,", :":y.separator,"[ ]":y.squareBracket,"{ }":y.brace}),Fb=rn.deserialize({version:14,states:"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#ClOOQO'#Cr'#CrQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CtOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59W,59WO!iQPO,59WOVQPO,59QOqQPO'#CmO!nQPO,59`OOQO1G.k1G.kOVQPO'#CnO!vQPO,59aOOQO1G.r1G.rOOQO1G.l1G.lOOQO,59X,59XOOQO-E6k-E6kOOQO,59Y,59YOOQO-E6l-E6l",stateData:"#O~OeOS~OQSORSOSSOTSOWQO_ROgPO~OVXOgUO~O^[O~PVO[^O~O]_OVhX~OVaO~O]bO^iX~O^dO~O]_OVha~O]bO^ia~O",goto:"!kjPPPPPPkPPkqwPPPPk{!RPPP!XP!e!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R",nodeNames:"⚠ JsonText True False Null Number String } { Object Property PropertyName : , ] [ Array",maxTerm:25,nodeProps:[["isolate",-2,6,11,""],["openedBy",7,"{",14,"["],["closedBy",8,"}",15,"]"]],propSources:[Nb],skippedNodes:[0],repeatNodeCount:2,tokenData:"(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oe~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Og~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zO]~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yO[~~'OO_~~'TO^~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~",tokenizers:[0],topRules:{JsonText:[0,1]},tokenPrec:0}),zb=qs.define({name:"json",parser:Fb.configure({props:[cc.add({Object:Hl({except:/^\s*\}/}),Array:Hl({except:/^\s*\]/})}),uc.add({"Object Array":hm})]}),languageData:{closeBrackets:{brackets:["[","{",'"']},indentOnInput:/^\s*[\}\]]$/}});function va(){return new Qp(zb)}const Vb="#e5c07b",ka="#e06c75",Hb="#56b6c2",Wb="#ffffff",Rs="#abb2bf",jr="#7d8799",$b="#61afef",qb="#98c379",Sa="#d19a66",jb="#c678dd",_b="#21252b",Ca="#2c313a",Aa="#282c34",Un="#353a42",Kb="#3E4451",Ma="#528bff",Ub=O.theme({"&":{color:Rs,backgroundColor:Aa},".cm-content":{caretColor:Ma},".cm-cursor, .cm-dropCursor":{borderLeftColor:Ma},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Kb},".cm-panels":{backgroundColor:_b,color:Rs},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:Aa,color:jr,border:"none"},".cm-activeLineGutter":{backgroundColor:Ca},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Un},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Un,borderBottomColor:Un},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Ca,color:Rs}}},{dark:!0}),Jb=_i.define([{tag:y.keyword,color:jb},{tag:[y.name,y.deleted,y.character,y.propertyName,y.macroName],color:ka},{tag:[y.function(y.variableName),y.labelName],color:$b},{tag:[y.color,y.constant(y.name),y.standard(y.name)],color:Sa},{tag:[y.definition(y.name),y.separator],color:Rs},{tag:[y.typeName,y.className,y.number,y.changed,y.annotation,y.modifier,y.self,y.namespace],color:Vb},{tag:[y.operator,y.operatorKeyword,y.url,y.escape,y.regexp,y.link,y.special(y.string)],color:Hb},{tag:[y.meta,y.comment],color:jr},{tag:y.strong,fontWeight:"bold"},{tag:y.emphasis,fontStyle:"italic"},{tag:y.strikethrough,textDecoration:"line-through"},{tag:y.link,color:jr,textDecoration:"underline"},{tag:y.heading,fontWeight:"bold",color:ka},{tag:[y.atom,y.bool,y.special(y.variableName)],color:Sa},{tag:[y.processingInstruction,y.string,y.inserted],color:qb},{tag:y.invalid,color:Wb}]),Gb=[Ub,wc(Jb)];var Yb=function(){var s=this,t=s.$createElement,e=s._self._c||t;return e("t-dialog",{attrs:{header:"编辑原始JSON",top:"10vh",closeOnOverlayClick:!1,visible:s.internalVisible,width:880,footer:!1},on:{close:s.handleClose}},[e("div",{attrs:{slot:"body"},slot:"body"},[e("t-form",{ref:"form",attrs:{data:s.formData,rules:s.rules,labelWidth:100},on:{submit:s.onSubmit}},[e("t-form-item",{attrs:{label:"脚本名称",name:"scriptName"}},[e("t-input",{style:{width:"480px"},attrs:{placeholder:"请输入脚本名称"},model:{value:s.formData.scriptName,callback:function(i){s.$set(s.formData,"scriptName",i)},expression:"formData.scriptName"}})],1),e("t-form-item",{attrs:{label:"脚本描述",name:"description"}},[e("t-input",{style:{width:"480px"},attrs:{placeholder:"请输入脚本描述"},model:{value:s.formData.description,callback:function(i){s.$set(s.formData,"description",i)},expression:"formData.description"}})],1),e("t-form-item",{attrs:{label:"业务域",name:"app"}},[e("t-select",{staticClass:"search-app-select",staticStyle:{width:"180px","margin-right":"12px"},attrs:{placeholder:"请选择业务域",clearable:"",loading:s.appLoading,options:s.appOptions},model:{value:s.formData.app,callback:function(i){s.$set(s.formData,"app",i)},expression:"formData.app"}})],1),e("t-form-item",{attrs:{label:"脚本内容",name:"content"}},[e("div",{staticClass:"content-editor-wrapper"},[e("div",{ref:"editorContainer",staticClass:"codemirror-container"},[s.isFullscreen?e("div",{staticClass:"fullscreen-title-bar"},[e("div",{staticClass:"title-text"},[s._v("脚本编辑器-"+s._s(s.formData.scriptName))]),e("t-button",{staticClass:"exit-fullscreen-btn",on:{click:s.toggleFullscreen}},[s._v("退出全屏")])],1):s._e(),s.isFullscreen?s._e():e("div",{staticClass:"editor-header"},[e("t-button",{staticClass:"fullscreen-btn",on:{click:s.toggleFullscreen}},[s._v(" "+s._s(s.isFullscreen?"退出全屏":"全屏查看")+" ")])],1)])])]),e("t-form-item",{attrs:{label:"备注",name:"remark"}},[e("t-textarea",{style:{width:"480px"},attrs:{placeholder:"请输入内容",name:"description"},model:{value:s.formData.remark,callback:function(i){s.$set(s.formData,"remark",i)},expression:"formData.remark"}})],1),e("t-form-item",{staticStyle:{float:"right"}},[e("t-button",{attrs:{variant:"outline"},on:{click:s.onClickCloseBtn}},[s._v("取消")]),e("t-button",{attrs:{theme:"primary",type:"submit"}},[s._v("确定")])],1)],1)],1)])},Qb=[];const Xb={name:"JsonEditor",props:{visible:{type:Boolean,default:!1},scriptInfo:{type:Object,default:()=>({})},scriptContent:{type:String,default:""},appOptions:{type:Array,default:()=>[]},appLoading:{type:Boolean,default:!1}},data(){return{internalVisible:!1,isFullscreen:!1,editorView:null,formData:{id:"",scriptName:"",description:"",app:"",remark:"",content:""},rules:{scriptName:[{required:!0,message:"请输入脚本名称",type:"error"}],content:[{required:!0,message:"请输入脚本内容",type:"error"}]}}},watch:{visible:{handler(s){this.internalVisible=s,s?(this.initFormData(),this.$nextTick(()=>{this.initCodeMirror()})):this.destroyEditor()},immediate:!0},scriptInfo:{handler(){this.visible&&this.initFormData()},deep:!0},scriptContent(){this.visible&&this.initFormData()}},mounted(){this.handleEscape=this.handleEscape.bind(this),document.addEventListener("keydown",this.handleEscape)},beforeDestroy(){document.removeEventListener("keydown",this.handleEscape),this.destroyEditor()},methods:{handleEscape(s){s.key==="Escape"&&this.isFullscreen&&(s.stopPropagation(),this.toggleFullscreen())},toggleFullscreen(){this.isFullscreen=!this.isFullscreen,this.$nextTick(()=>{const s=this.$refs.editorContainer;this.isFullscreen?(s.classList.add("fullscreen-editor"),this.editorView.requestMeasure()):s.classList.remove("fullscreen-editor"),this.editorView.requestMeasure()})},initFormData(){this.formData={id:this.scriptInfo.id||"",scriptName:this.scriptInfo.scriptName||"",description:this.scriptInfo.description||"",app:this.scriptInfo.app||"",remark:this.scriptInfo.remark||"",content:this.scriptContent||'[{"text": "示例","type": "wait","value": "5000","desc": "等待5秒"}]'}},initCodeMirror(){this.$refs.editorContainer&&(this.destroyEditor(),this.editorView=new O({doc:this.formatCompressedJSON(this.formData.content)||"",extensions:[Mb,va(),Gb,Ab(),va(),un.of("  "),O.updateListener.of(s=>{s.docChanged&&(this.formData.content=s.state.doc.toString())})],parent:this.$refs.editorContainer}))},destroyEditor(){this.editorView&&(this.editorView.destroy(),this.editorView=null)},formatCompressedJSON(s){try{const t=JSON.parse(s);return JSON.stringify(t,null,2)}catch(t){return console.error("JSON格式化失败:",t.message),s}},validateJson(s){if(!s)return!1;try{return JSON.parse(s),!0}catch{return!1}},onSubmit({result:s,firstError:t}){var e;if(t)console.log("Errors: ",s),this.$message.warning(t);else{if(!this.validateJson(this.formData.content)){this.$message.error("内容必须是有效的JSON格式，请检查后重试");return}const i={id:this.formData.id,scriptName:this.formData.scriptName,content:this.formData.content,app:this.formData.app,description:this.formData.description,remark:this.formData.remark,editor:((e=this.$store.state.user.userInfo)==null?void 0:e.alias)||""};this.$emit("save",i),this.$emit("update:visible",!1)}},onClickCloseBtn(){this.$emit("update:visible",!1)},handleClose(){this.$emit("update:visible",!1)}}},Oa={};var Zb=Da(Xb,Yb,Qb,!1,ty,"3d65f2ba",null,null);function ty(s){for(let t in Oa)this[t]=Oa[t]}const ry=function(){return Zb.exports}();export{O as E,ry as J,iy as S,Mb as b,un as i,va as j,Ab as l,Gb as o};
