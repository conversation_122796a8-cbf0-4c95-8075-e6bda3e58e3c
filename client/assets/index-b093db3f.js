import{n as l}from"./index-8c8571d2.js";var c=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("t-dialog",{attrs:{visible:e.dialogVisible,header:e.title,width:"600px","on-confirm":e.handleConfirm,"on-cancel":e.handleCancel},on:{"update:visible":function(s){e.dialogVisible=s}}},[t("div",{staticClass:"confirm-content"},[t("p",{staticClass:"warning-text"},[t("t-icon",{staticStyle:{color:"#ff9500","margin-right":"8px"},attrs:{name:"error-circle"}}),e._v(" "+e._s(e.warningMessage)+" ")],1),e.affectedConfigs.length>0?t("div",{staticClass:"affected-configs"},e._l(e.affectedConfigs,function(s){return t("div",{key:s.id,staticClass:"config-item"},[t("div",{staticClass:"config-header"},[t("strong",[e._v(e._s(s.configName))]),t("span",{staticClass:"config-id"},[e._v("(ID: "+e._s(s.id)+")")])]),s.cases&&s.cases.length>0?t("div",{staticClass:"cases-list"},[t("p",{staticClass:"cases-title"},[e._v("关联用例:")]),t("div",{staticClass:"case-tags"},e._l(s.cases,function(a){return t("span",{key:a.id,staticClass:"case-tag case-tag-clickable",on:{click:function(_){return e.goToCaseEdit(a.id)}}},[e._v(" "+e._s(a.caseName)+" ")])}),0)]):t("div",{staticClass:"no-cases"},[t("p",{staticClass:"no-cases-text"},[e._v("暂无关联用例")])])])}),0):e._e(),t("p",{staticClass:"confirm-text"},[e._v(" "+e._s(e.confirmMessage)+" ")])])])},o=[];const r={name:"VariableConfirmDialog",props:{visible:{type:Boolean,default:!1},title:{type:String,default:"操作确认"},warningMessage:{type:String,default:"检测到该变量正在被以下配置使用，此操作可能会影响这些配置的正常运行："},confirmMessage:{type:String,default:"确定要继续操作吗？请确保相关配置能够正确处理此变更。"},affectedConfigs:{type:Array,default:()=>[]}},computed:{dialogVisible:{get(){return this.visible},set(e){this.$emit("update:visible",e)}}},methods:{handleConfirm(){this.$emit("confirm"),this.dialogVisible=!1},handleCancel(){this.$emit("cancel"),this.dialogVisible=!1},goToCaseEdit(e){this.$router.push({path:"/userCase/edit",query:{id:e}})}}},n={};var d=l(r,c,o,!1,f,"159288d4",null,null);function f(e){for(let i in n)this[i]=n[i]}const u=function(){return d.exports}();export{u as V};
