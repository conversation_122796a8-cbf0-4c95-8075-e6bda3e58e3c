import{V as n}from"./index-b093db3f.js";import{n as l}from"./index-8c8571d2.js";var f=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"variable-edit-container"},[t("t-card",[t("t-form",{ref:"form",attrs:{data:e.formData,rules:e.rules,"label-width":"120px",colon:""},on:{submit:e.handleSubmit}},[t("t-form-item",{attrs:{label:"变量名称",name:"variableName"}},[t("t-input",{attrs:{placeholder:"请输入变量名称"},model:{value:e.formData.variableName,callback:function(a){e.$set(e.formData,"variableName",a)},expression:"formData.variableName"}})],1),t("t-form-item",{attrs:{label:"业务域",name:"app"}},[t("t-select",{attrs:{options:e.appOptions,loading:e.appLoading,placeholder:"请选择业务域",filterable:"",clearable:""},model:{value:e.formData.app,callback:function(a){e.$set(e.formData,"app",a)},expression:"formData.app"}})],1),t("t-form-item",{attrs:{label:"关联配置",name:"configId"}},[t("t-select",{attrs:{options:e.configOptions,loading:e.configLoading,placeholder:"请选择关联配置",filterable:"",multiple:"",clearable:""},model:{value:e.formData.configId,callback:function(a){e.$set(e.formData,"configId",a)},expression:"formData.configId"}})],1),t("t-form-item",{attrs:{label:"前缀配置",name:"prefix"}},[t("t-space",{attrs:{direction:"vertical",size:"large"}},[t("t-radio-group",{model:{value:e.formData.prefix.type,callback:function(a){e.$set(e.formData.prefix,"type",a)},expression:"formData.prefix.type"}},[t("t-radio",{attrs:{value:"none"}},[e._v("无前缀")]),t("t-radio",{attrs:{value:"timestamp"}},[e._v("时间戳")]),t("t-radio",{attrs:{value:"custom"}},[e._v("自定义")])],1),e.formData.prefix.type==="custom"?t("t-space",[t("t-form-item",{attrs:{label:"长度",name:"prefix.length"}},[t("t-input-number",{attrs:{min:1,max:10},model:{value:e.formData.prefix.length,callback:function(a){e.$set(e.formData.prefix,"length",a)},expression:"formData.prefix.length"}})],1),t("t-form-item",{attrs:{label:"包含字母",name:"prefix.letters"}},[t("t-switch",{model:{value:e.formData.prefix.letters,callback:function(a){e.$set(e.formData.prefix,"letters",a)},expression:"formData.prefix.letters"}})],1),t("t-form-item",{attrs:{label:"包含数字",name:"prefix.number"}},[t("t-switch",{model:{value:e.formData.prefix.number,callback:function(a){e.$set(e.formData.prefix,"number",a)},expression:"formData.prefix.number"}})],1)],1):e._e()],1)],1),t("t-form-item",{attrs:{label:"后缀配置",name:"suffix"}},[t("t-space",{attrs:{direction:"vertical",size:"large"}},[t("t-radio-group",{model:{value:e.formData.suffix.type,callback:function(a){e.$set(e.formData.suffix,"type",a)},expression:"formData.suffix.type"}},[t("t-radio",{attrs:{value:"none"}},[e._v("无后缀")]),t("t-radio",{attrs:{value:"timestamp"}},[e._v("时间戳")]),t("t-radio",{attrs:{value:"custom"}},[e._v("自定义")])],1),e.formData.suffix.type==="custom"?t("t-space",[t("t-form-item",{attrs:{label:"长度",name:"suffix.length"}},[t("t-input-number",{attrs:{min:1,max:10},model:{value:e.formData.suffix.length,callback:function(a){e.$set(e.formData.suffix,"length",a)},expression:"formData.suffix.length"}})],1),t("t-form-item",{attrs:{label:"包含字母",name:"suffix.letters"}},[t("t-switch",{model:{value:e.formData.suffix.letters,callback:function(a){e.$set(e.formData.suffix,"letters",a)},expression:"formData.suffix.letters"}})],1),t("t-form-item",{attrs:{label:"包含数字",name:"suffix.number"}},[t("t-switch",{model:{value:e.formData.suffix.number,callback:function(a){e.$set(e.formData.suffix,"number",a)},expression:"formData.suffix.number"}})],1)],1):e._e()],1)],1),t("t-form-item",{attrs:{label:"环境值配置",name:"data"}},[t("t-space",{attrs:{direction:"vertical",size:"large"}},[t("t-table",{attrs:{data:e.formData.data,columns:e.envColumns,"row-key":"env"},scopedSlots:e._u([{key:"env",fn:function(a){var i=a.row;return[t("t-select",{attrs:{filterable:"",placeholder:"请输入环境名称",options:e.envOptions},model:{value:i.env,callback:function(o){e.$set(i,"env",o)},expression:"row.env"}})]}},{key:"value",fn:function(a){var i=a.row;return[t("t-input",{model:{value:i.value,callback:function(o){e.$set(i,"value",o)},expression:"row.value"}})]}},{key:"operation",fn:function(a){var i=a.rowIndex;return[t("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(o){return e.removeEnv(i)}}},[e._v("删除")])]}}])}),t("t-button",{staticStyle:{"margin-top":"12px"},attrs:{theme:"default"},on:{click:e.addEnv}},[e._v("添加环境")])],1)],1),t("t-form-item",[t("t-space",[t("t-button",{attrs:{theme:"primary",type:"submit"}},[e._v("保存")]),t("t-button",{attrs:{theme:"default"},on:{click:function(a){return e.$router.go(-1)}}},[e._v("取消")])],1)],1)],1)],1),t("VariableConfirmDialog",{attrs:{visible:e.showConfirmDialog,title:"请确认是否修改变量！","warning-message":"检测到该变量发生修改，修改变量可能会影响以下用例正常运行：","confirm-message":"确定要继续修改变量吗？","affected-configs":e.affectedConfigs},on:{"update:visible":function(a){e.showConfirmDialog=a},confirm:e.handleConfirmSave,cancel:e.handleCancelSave}})],1)},c=[];const m={components:{VariableConfirmDialog:n},data(){return{envConfigType:"preset",appOptions:[],appLoading:!1,configOptions:[],configLoading:!1,originalVariableName:"",showConfirmDialog:!1,affectedConfigs:[],formData:{variableName:"",app:"",configId:[],creator:"",updater:"",updatedAt:"",prefix:{type:"none",length:4,letters:!0},suffix:{type:"none",length:4,letters:!0},data:[{env:"global",value:""}]},rules:{variableName:[{required:!0,message:"请输入变量名称",trigger:"blur"}]},envOptions:[{label:"全局",value:"global"},{label:"生产环境",value:"prod"},{label:"测试环境",value:"test"},{label:"预发环境",value:"pre"},{label:"项目环境1",value:"project1"},{label:"项目环境2",value:"project2"}],envColumns:[{colKey:"env",title:"环境",cell:"env"},{colKey:"value",title:"值",cell:"value"},{colKey:"operation",title:"操作",cell:"operation"}]}},methods:{async fetchAppOptions(){this.appLoading=!0;try{const e=await this.$request.get("https://m.esign.cn/infocenter-manager/forward/bizDomain/list");e.data&&Array.isArray(e.data.data)&&e.data.code==0?this.appOptions=e.data.data.map(r=>({label:r.name,value:r.name})):this.appOptions=[]}catch{this.appOptions=[]}finally{this.appLoading=!1}},async fetchConfigOptions(){this.configLoading=!0;try{const{data:e}=await this.$request.get("/configs/list",{params:{page:1,limit:200}});e&&e.data&&(this.configOptions=e.data.map(r=>({value:r.id,label:`${r.configName} (${r.id})`})))}catch{this.configOptions=[]}finally{this.configLoading=!1}},createOptions(e){this.envOptions.push({value:e,label:e}),console.log("create option:",e)},addEnv(){const e=this.envOptions.find(r=>!this.formData.data.some(t=>t.env===r.value));e?this.formData.data.push({env:e.value,value:""}):this.$message.warning("所有可选环境已添加")},checkDuplicateEnv(){const e=new Set;for(const r of this.formData.data){if(e.has(r.env))return this.$message.error(`环境${r.env}已存在，请勿重复添加`),!1;e.add(r.env)}return!0},removeEnv(e){this.formData.data.splice(e,1)},async checkVariableUsage(){try{const{data:e}=await this.$request.get(`/variables/${this.$route.query.id}/configs`);return e||[]}catch(e){return console.error("检查变量使用情况失败:",e),[]}},async handleSubmit(){if(this.checkDuplicateEnv()){if(this.$route.query.id){const e=await this.checkVariableUsage();if(console.log("affectedConfigs:",e),e.length>0){this.affectedConfigs=e,this.showConfirmDialog=!0;return}}await this.saveVariable()}},async saveVariable(){var e;try{const r=((e=this.$store.state.user.userInfo)==null?void 0:e.alias)||"",t=new Date().toISOString();this.$route.query.id||(this.formData.creator=r),this.formData.updater=r,this.formData.updatedAt=t;const a=this.$route.query.id?`/variables/${this.$route.query.id}`:"/variables/create",i="post";await this.$request[i](a,this.formData),this.$message.success("保存成功"),this.$router.push("/variable")}catch{this.$message.error("保存失败")}},handleConfirmSave(){this.showConfirmDialog=!1,this.saveVariable()},handleCancelSave(){this.showConfirmDialog=!1}},async mounted(){if(await this.fetchAppOptions(),await this.fetchConfigOptions(),this.$route.query.id){const{data:e}=await this.$request.get(`/variables/${this.$route.query.id}`);this.formData={...e},this.originalVariableName=e.variableName;const r=e.configs?e.configs.map(t=>t.id):[];this.$set(this.formData,"configId",r)}}},s={};var u=l(m,f,c,!1,p,"e864657c",null,null);function p(e){for(let r in s)this[r]=s[r]}const h=function(){return u.exports}();export{h as default};
