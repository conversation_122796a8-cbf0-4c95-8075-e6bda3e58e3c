import{V as n}from"./index-b093db3f.js";import{n as o}from"./index-8c8571d2.js";var l=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"variable-list-container"},[e("t-card",[e("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:t.searchForm,"label-width":80,colon:""},on:{reset:t.onReset,submit:t.onSubmit}},[e("t-row",{attrs:{gutter:[24,24]}},[e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"变量名称",name:"variableName"}},[e("t-input",{style:{width:"100%"},attrs:{type:"search",placeholder:"请输入变量名称"},model:{value:t.searchForm.variableName,callback:function(i){t.$set(t.searchForm,"variableName",i)},expression:"searchForm.variableName"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"业务域",name:"app"}},[e("t-select",{style:{width:"100%"},attrs:{options:t.appOptions,placeholder:"请选择业务域",clearable:"",loading:t.appLoading},model:{value:t.searchForm.app,callback:function(i){t.$set(t.searchForm,"app",i)},expression:"searchForm.app"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"更新人",name:"updater"}},[e("t-input",{style:{width:"100%"},attrs:{placeholder:"请输入更新人",clearable:""},model:{value:t.searchForm.updater,callback:function(i){t.$set(t.searchForm,"updater",i)},expression:"searchForm.updater"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"关联配置",name:"configName"}},[e("t-select",{style:{width:"100%"},attrs:{options:t.configOptions,placeholder:"请选择配置",clearable:"",loading:t.configLoading},model:{value:t.searchForm.configName,callback:function(i){t.$set(t.searchForm,"configName",i)},expression:"searchForm.configName"}})],1)],1)],1),e("t-row",{staticClass:"operation-container",staticStyle:{"margin-top":"8px"},attrs:{gutter:[16,24]}},[e("t-col",{staticStyle:{"text-align":"left"},attrs:{span:24}},[e("t-button",{attrs:{theme:"primary",type:"submit"}},[t._v(" 查询 ")]),e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[t._v(" 重置 ")]),e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{theme:"primary"},on:{click:t.handleAdd}},[t._v(" 新增变量 ")])],1)],1)],1),e("div",{staticClass:"table-container"},[e("t-table",{attrs:{data:t.tableData,columns:t.columns,"row-key":"id",pagination:t.pagination},on:{"page-change":t.onPageChange},scopedSlots:t._u([{key:"operation",fn:function(i){return[e("t-space",[e("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(s){return t.handleEdit(i.row)}}},[t._v("编辑")]),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.handleDelete(i.row)}}},[t._v("删除")])],1)]}}])})],1)],1),e("VariableConfirmDialog",{attrs:{visible:t.showDeleteConfirmDialog,title:"变量删除提醒","warning-message":"检测到该变量正在被以下配置使用，删除变量可能会影响以下用例正常运行：","confirm-message":"请先解除变量与相关配置关联，才能进行删除","affected-configs":t.affectedConfigs},on:{"update:visible":function(i){t.showDeleteConfirmDialog=i},confirm:t.handleConfirmDelete,cancel:t.handleCancelDelete}})],1)},c=[];const p={components:{VariableConfirmDialog:n},data(){return{searchForm:{variableName:"",app:"",configName:"",updater:""},configOptions:[],configLoading:!1,appOptions:[],appLoading:!1,tableData:[],showDeleteConfirmDialog:!1,currentDeleteRow:null,affectedConfigs:[],columns:[{colKey:"variableName",title:"变量名称"},{colKey:"app",title:"业务域"},{colKey:"configs",title:"关联配置",width:200,cell:(t,{row:a})=>{if(!a.configNames||a.configNames.length===0)return t("span","-");const e=a.configNames[0],i=a.configNames.length-1;return i===0?t("t-tag",{props:{variant:"light",theme:"primary"}},e):t("t-tooltip",{props:{content:`关联配置列表：${a.configNames.join(", ")}`}},[t("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"}},[t("t-tag",{props:{variant:"light",theme:"primary"}},e),t("span",{style:{marginLeft:"4px",color:"#999",fontSize:"12px"}},`+${i}`)])])}},{colKey:"updater",title:"更新人"},{colKey:"updatedAt",title:"更新时间",cell:(t,{row:a})=>a.updatedAt?t("span",this.$dayjs(a.updatedAt).format("YYYY-MM-DD HH:mm")):t("span","-")},{colKey:"operation",width:200,title:"操作",cell:"operation"}],pagination:{current:1,pageSize:10,total:1}}},methods:{onPageChange(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.fetchVariableList()},handleEdit(t){window.open(`/variable/variableEdit?id=${t.id}`)},async handleDelete(t){try{const{data:a}=await this.$request.get(`/variables/${t.id}/configs`);a&&a.length>0?(this.currentDeleteRow=t,this.affectedConfigs=a,this.showDeleteConfirmDialog=!0):this.deleteVariable(t.id)}catch{this.deleteVariable(t.id)}},async deleteVariable(t){try{await this.$request.delete(`/variables/${t}`),this.$message.success("删除成功"),this.fetchVariableList()}catch{this.$message.error("删除失败")}},handleConfirmDelete(){this.currentDeleteRow&&(this.currentDeleteRow=null)},handleCancelDelete(){this.currentDeleteRow=null},handleAdd(){this.$router.push("/variable/variableEdit")},onSubmit(){this.pagination.current=1,this.fetchVariableList()},onReset(){this.searchForm={variableName:"",app:"",configName:"",updater:""},this.pagination.current=1,this.fetchVariableList()},handleSearch(){this.pagination.current=1,this.fetchVariableList()},handleReset(){this.searchForm={variableName:"",app:"",configName:"",updater:""},this.pagination.current=1,this.fetchVariableList()},async fetchVariableList(){try{const t={page:this.pagination.current,limit:this.pagination.pageSize};this.searchForm.variableName&&(t.variableName=this.searchForm.variableName),this.searchForm.app&&(t.app=this.searchForm.app),this.searchForm.configName&&(t.configName=this.searchForm.configName),this.searchForm.updater&&(t.updater=this.searchForm.updater),this.searchForm.configName&&(t.configId=this.searchForm.configName);const{data:a}=await this.$request.get("/variables/list",{params:t});this.tableData=a.data,this.pagination.total=a.total}catch{this.$message.error("获取变量列表失败")}},async fetchConfigOptions(){try{this.configLoading=!0;const{data:t}=await this.$request.get("/configs/list");this.configOptions=t.data.map(a=>({value:a.id,label:a.configName}))}catch{this.$message.error("获取配置列表失败")}finally{this.configLoading=!1}},async fetchAppOptions(){this.appLoading=!0;try{const t=await this.$request.get("/cases/apps");t.data&&Array.isArray(t.data)&&(this.appOptions=[{label:"全部业务域",value:""},...t.data.map(a=>({label:a,value:a}))])}catch(t){console.error(t),this.$message.error("获取业务域数据失败")}finally{this.appLoading=!1}}},mounted(){this.fetchVariableList(),this.fetchConfigOptions(),this.fetchAppOptions()}},r={};var h=o(p,l,c,!1,m,"b6268a8c",null,null);function m(t){for(let a in r)this[a]=r[a]}const g=function(){return h.exports}();export{g as default};
