import{n as p}from"./index-8c8571d2.js";var f=function(){var t=this,r=t.$createElement,e=t._self._c||r;return e("div",{staticClass:"report-container"},[e("t-card",{staticClass:"stat-card"},[e("t-row",{attrs:{gutter:20}},[e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"总用例数",value:t.stats.uniqueCases},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-layers"})]},proxy:!0}])})],1),e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"总执行数",value:t.stats.totalCases},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-file"})]},proxy:!0}])})],1),e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"通过用例",value:t.stats.passedCases},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-check-circle",staticStyle:{color:"var(--td-success-color)"}})]},proxy:!0}])})],1),e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"未通过用例",value:t.stats.failedCases},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-close-circle",staticStyle:{color:"var(--td-error-color)"}})]},proxy:!0}])})],1),e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"重试用例数",value:t.stats.retryCases},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-refresh",staticStyle:{color:"var(--td-warning-color)"}})]},proxy:!0}])})],1),e("t-col",{attrs:{span:4}},[e("t-statistic",{attrs:{title:"通过率",value:t.stats.passedRate,suffix:"%"},scopedSlots:t._u([{key:"prefix",fn:function(){return[e("i",{staticClass:"t-icon t-icon-chart-pie",staticStyle:{color:"var(--td-brand-color)"}})]},proxy:!0}])})],1)],1)],1),e("t-card",{staticClass:"stat-card"},[e("t-table",{attrs:{data:t.testCases,columns:[{title:"用例名称",colKey:"name"},{title:"用例结果",colKey:"runResult",scopedSlots:{cell:"runResult"}},{title:"测试类型",colKey:"runType",scopedSlots:{cell:"runType"}},{title:"执行时间",colKey:"startTime",scopedSlots:{cell:"startTime"}},{title:"操作",colKey:"op",width:120,scopedSlots:{cell:"op"}}],"row-key":"id"},scopedSlots:t._u([{key:"runType",fn:function(n){var a=n.row;return[e("t-tag",[t._v(t._s(a.runType||"批量执行"))])]}},{key:"runResult",fn:function(n){var a=n.row;return[e("t-tag",{attrs:{theme:a.runResult==="success"?"success":"danger",size:"small"}},[t._v(" "+t._s(a.runResult==="success"?"通过":"失败")+" ")])]}},{key:"startTime",fn:function(n){var a=n.row;return[t._v(" "+t._s(new Date(a.startTime).toLocaleString())+" ")]}},{key:"op",fn:function(n){var a=n.row;return[e("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(s){return t.handleViewResult(a)}}},[t._v("查看结果")])]}}])})],1)],1)},m=[];const h={name:"TestReport",data(){return{testCases:[],groupCases:[],totalExecutions:0,currentScreenshot:"",dialogVisible:!1}},computed:{passedCases(){return new Set(this.testCases.filter(r=>r.runResult==="success").map(r=>r.name)).size},retryCases(){return this.testCases.filter(t=>t.runType==="retry").length},stats(){const t=this.totalExecutions,e=new Set(this.testCases.map(o=>o.name)).size,n=this.testCases.reduce((o,i)=>{const u=Number(i.duration)||0;return o+u},0),a=this.testCases.reduce((o,i)=>{const u=Number(i.duration)||0;return u>o.value?{value:u,caseId:i.id,name:i.name}:o},{value:0,caseId:"",name:""}),s=e>0?this.passedCases/e*100:0,c=t>0?n/t:0,d=e-this.passedCases;return{totalDuration:n,totalCases:t,uniqueCases:e,retryCases:this.retryCases,passedCases:this.passedCases,failedCases:d,passedRate:Number(s.toFixed(1)),averageDuration:Number(c.toFixed(1)),maxDuration:a}}},watch:{"$route.query.runId":{handler(t){t&&this.fetchData()},immediate:!0}},mounted(){this.fetchData(),this.$nextTick(()=>{this.initPieChart(),this.initBarChart()})},methods:{async fetchData(){try{const t=this.$route.query.runId;if(!t){console.error("缺少运行ID参数");return}const e=(await this.$request.get("/records/queryByRunId",{params:{runId:t}})).data.map(s=>s.status==="running"?(this.$message.warning("测试用例正在运行中"),null):{...s}).filter(Boolean),n=e.map(s=>({id:s.id,caseId:s.caseId,groupName:s.groupName,name:s.caseName,runResult:s.runResult,browser:s.browser,duration:s.duration,startTime:s.startTime,runType:s.runType})),a=new Map;n.forEach(s=>{const c=a.get(s.name);(!c||new Date(s.startTime)>new Date(c.startTime))&&a.set(s.name,s)}),this.testCases=Array.from(a.values()),this.totalExecutions=n.length,this.groupCases=e}catch(t){console.error("获取测试报告数据失败:",t),this.testCases=[]}},getUrlParams(t){return new URLSearchParams(window.location.search).get(t)||""},statusType(t){switch(t){case"passed":return"success";case"failed":return"danger";case"running":return"warning";default:return"default"}},statusIcon(t){switch(t){case"passed":return"t-icon t-icon-check-circle";case"failed":return"t-icon t-icon-close-circle";case"running":return"t-icon t-icon-loading";default:return"t-icon t-icon-help-circle"}},handleViewResult(t){const r=`${window.location.origin}/record/result?id=${t.id}&caseId=${t.caseId}`;window.open(r,"_blank")}}},l={};var y=p(h,f,m,!1,C,"0f179c0a",null,null);function C(t){for(let r in l)this[r]=l[r]}const v=function(){return y.exports}();export{v as default};
