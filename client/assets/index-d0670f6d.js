import{n as v,p as $}from"./index-8c8571d2.js";import{T as N}from"./index-9ee2e03b.js";function F(e){const a=e.caseName+"测试用例",t={url:e.config.url,env:e.config.env||"",height:e.config.height,width:e.config.width,pageLoadTimeout:"",assertionTimeout:""},s={cookies:e.config.cookie.map(i=>({name:i.key,value:i.value,domain:"xxxx",path:"xxxx",expires:"xxxxx"})),localStorage:e.config.localstorage,sessionStorage:e.config.session,request:e.config.request.map(i=>{let o;try{if(o=JSON.parse(i.response),typeof o=="string")try{o=JSON.parse(o)}catch{}}catch{o=i.response}return{path:i.path,type:i.path==="/v1/operational/guide-wecom"?"regexp":"include",method:i.method,response:typeof o=="object"?JSON.stringify(o):o,...i.path==="/v1/operational/guide-wecom"&&{merge:!0}}})},n=e.config.variables.map(i=>{var r,c;const o={name:i.variableName,value:((r=i.data.find(u=>u.env===e.config.env))==null?void 0:r.value)||((c=i.data.find(u=>u.env==="global"))==null?void 0:c.value)};return i.prefix.type!=="none"&&(o.prefix={type:i.prefix.type,length:String(i.prefix.length),...i.prefix.letters&&{letters:!0}}),i.suffix.type!=="none"&&(o.suffix={type:i.suffix.type,length:String(i.suffix.length),...i.suffix.letters?{letters:!0}:{number:!0}}),o}),l=[{title:e.caseName,steps:e.scripts.flatMap(i=>({title:i.scriptName,data:JSON.parse(i.content)}))}],f=[{title:e.caseName,steps:e.scripts.flatMap(i=>JSON.parse(i.content).map(o=>({...o})))}];return{id:a,injectId:`${a}-${new Date().getTime()}`,app:e.app,caseId:e.id,product:e.product,config:{name:a,base:t,mock:s,variable:n,preRequest:e.config.url},selector:{},cases:f,xmindCases:l}}function C(e){const a=(o,r=[])=>({data:{text:o,note:"",imageSize:{}},children:r}),t=o=>a(o,[]),s=o=>{const r=[];let{text:c}=o;return o.type==="upload"&&o.filePath&&r.push(t(`file:${o.filePath}`)),["requestAndRedirect","request"].includes(o.type)&&r.push(t(o.value)),o.type.startsWith("assert")&&(c=`ER-${c}`),o.desc&&r.push(t(`#${o.desc}`)),r.length?a(c,r):t(c)},n={template:"default",theme:"fresh-blue",root:a(e.config.name)},l=a("配置"),f=e.config.base;Object.entries(f).forEach(([o,r])=>{r&&l.children.push(a(o,[t(r)]))});const i=a("mock");if(e.config.mock.cookies&&e.config.mock.cookies.length>0){const o=a("cookies");o.children=e.config.mock.cookies.map(r=>a(r.name,[t(r.value)])),i.children.push(o)}if(e.config.mock.localStorage&&e.config.mock.localStorage.length>0){const o=a("localStorage");o.children=e.config.mock.localStorage.map(r=>a(r.key,[t(r.value)])),i.children.push(o)}if(e.config.mock.sessionStorage&&e.config.mock.sessionStorage.length>0){const o=a("sessionStorage");o.children=e.config.mock.sessionStorage.map(r=>a(r.key,[t(r.value)])),i.children.push(o)}if(e.config.mock.request&&e.config.mock.request.length>0){const o=a("request");o.children=e.config.mock.request.map(r=>{const c=a(`${r.type}:${r.path}`);return c.children=[a("method",[t(r.method)]),a("response",[t(r.response)])],c}),i.children.push(o)}if(l.children.push(i),e.config.variable&&e.config.variable.length>0){const o=a("variable");o.children=e.config.variable.map(r=>{const c=a(r.name,[t(r.value)]),u=(h,x)=>{if(!h)return null;const p=a(`${x}:${h.type}`);return h.type==="custom"?(p.children.push(t(`length:${h.length}`)),h.letters&&p.children.push(t("letters")),h.number&&p.children.push(t("number"))):h.type==="timestamp"&&(p.children=[]),p},d=u(r.prefix,"prefix");d&&c.children.push(d);const g=u(r.suffix,"suffix");return g&&c.children.push(g),c}),l.children.push(o)}if(n.root.children.push(l),e.selector&&Object.keys(e.selector).length>0){const o=a("自动化标识");o.children=Object.entries(e.selector).map(([r,c])=>a(r,[t(c)])),n.root.children.push(o)}if(e.xmindCases&&e.xmindCases.length>0){const o=a("功能");o.children=e.xmindCases.map(r=>{const c=a("TL-用例&"+r.title);return c.children=r.steps.map(u=>a("#脚本&"+u.title,u.data.map(d=>s(d)))),c}),n.root.children.push(o)}return n}const S=[{value:"free",label:"未执行"},{value:"running",label:"执行中"},{value:"pending",label:"队列中"}],m={MAIN:0,SUB:1,SUPPLEMENT:2},_=[{value:m.MAIN,label:"主合同"},{value:m.SUB,label:"子合同"},{value:m.SUPPLEMENT,label:"补充合同"}],w={PAYMENT:0,RECEIPT:1};var k=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"list-common-table"},[t("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:e.formData,"label-width":80,colon:""},on:{reset:e.onReset,submit:e.onSubmit}},[t("t-row",{attrs:{gutter:[24,24]}},[t("t-col",{attrs:{span:6}},[t("t-form-item",{attrs:{label:"用例名称",name:"caseName"}},[t("t-input",{style:{width:"100%"},attrs:{type:"search",placeholder:"请输入用例名称"},model:{value:e.formData.caseName,callback:function(s){e.$set(e.formData,"caseName",s)},expression:"formData.caseName"}})],1)],1),t("t-col",{attrs:{span:6}},[t("t-form-item",{attrs:{label:"用例分组",name:"groupName"}},[t("t-select",{style:{width:"100%"},attrs:{filterable:"",options:e.groupNameOptions,placeholder:"请选择分组",clearable:""},model:{value:e.formData.groupName,callback:function(s){e.$set(e.formData,"groupName",s)},expression:"formData.groupName"}})],1)],1),t("t-col",{attrs:{span:6}},[t("t-form-item",{attrs:{label:"业务域",name:"app"}},[t("t-select",{style:{width:"100%"},attrs:{options:e.appOptions,placeholder:"请选择业务域",clearable:"",loading:e.appLoading},model:{value:e.formData.app,callback:function(s){e.$set(e.formData,"app",s)},expression:"formData.app"}})],1)],1),t("t-col",{attrs:{span:6}},[t("t-form-item",{attrs:{label:"产品线",name:"product"}},[t("t-select",{style:{width:"100%"},attrs:{options:e.productOptions,placeholder:"请选择产品线",clearable:"",loading:e.productLineLoading},model:{value:e.formData.product,callback:function(s){e.$set(e.formData,"product",s)},expression:"formData.product"}})],1)],1)],1),t("t-row",{staticClass:"operation-container",staticStyle:{"margin-top":"8px"},attrs:{gutter:[16,24]}},[t("t-col",{staticStyle:{"text-align":"left"},attrs:{span:24}},[t("t-button",{attrs:{theme:"primary",type:"submit"}},[e._v(" 查询 ")]),t("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[e._v(" 重置 ")]),t("t-button",{staticStyle:{"margin-left":"8px"},attrs:{theme:"primary"},on:{click:e.handleAdd}},[e._v(" 新增用例 ")]),t("t-button",{staticStyle:{"margin-left":"8px"},attrs:{theme:"primary"},on:{click:e.handleExecuteGroup}},[e._v(" 批量执行 ")])],1)],1)],1),t("div",{staticClass:"table-container"},[t("t-table",{attrs:{data:e.data,columns:e.columns,rowKey:e.rowKey,verticalAlign:e.verticalAlign,hover:e.hover,pagination:e.pagination,loading:e.dataLoading,headerAffixedTop:!0,headerAffixProps:{offsetTop:e.offsetTop,container:e.getContainer}},on:{"page-change":e.onPageChange},scopedSlots:e._u([{key:"op",fn:function(s){var n=s.row;return[t("a",{staticClass:"t-button-link",on:{click:function(l){return e.handleEdit(n)}}},[e._v("编辑")]),t("a",{staticClass:"t-button-link",on:{click:function(l){return e.handleDownload(n)}}},[e._v("下载")]),t("a",{staticClass:"t-button-link",on:{click:function(l){return e.handleClickDelete(n)}}},[e._v("删除")]),t("a",{staticClass:"t-button-link",on:{click:function(l){return e.handleExecute(n)}}},[e._v("执行")])]}}])}),t("t-dialog",{attrs:{header:"确认删除当前所选用例？",body:e.confirmBody,visible:e.confirmVisible,onClose:e.onCancel},on:{"update:visible":function(s){e.confirmVisible=s},confirm:e.onConfirmDelete}}),t("t-dialog",{attrs:{header:"批量执行测试集",closeOnOverlayClick:!1,visible:e.batchDialogVisible,onClose:function(){e.batchDialogVisible=!1,e.batchExecuting=!1}},on:{"update:visible":function(s){e.batchDialogVisible=s}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("t-button",{attrs:{theme:"default"},on:{click:function(s){e.batchDialogVisible=!1}}},[e._v("取消")]),t("t-button",{attrs:{theme:"primary",loading:e.batchExecuting,disabled:e.batchExecuting},on:{click:e.handleBatchExecute}},[e._v("确认")])]},proxy:!0}])},[t("t-form",{attrs:{data:e.batchForm,"label-width":"120px"}},[t("t-form-item",{attrs:{label:"执行策略",name:"strategy"}},[t("t-radio-group",{model:{value:e.batchForm.strategy,callback:function(s){e.$set(e.batchForm,"strategy",s)},expression:"batchForm.strategy"}},[t("t-radio",{attrs:{value:"group"}},[e._v("用例组执行")]),t("t-radio",{attrs:{value:"cases"}},[e._v("多用例执行")])],1),t("t-tooltip",{attrs:{content:"用例组执行：执行指定分组下的所有用例；多用例执行：选择多个用例进行执行"}},[t("t-icon",{staticStyle:{"margin-left":"8px"},attrs:{name:"help-circle"}})],1)],1),t("t-form-item",{attrs:{label:"测试集名称",name:"testSuiteName"}},[t("t-input",{attrs:{placeholder:"请输入测试集名称"},model:{value:e.batchForm.testSuiteName,callback:function(s){e.$set(e.batchForm,"testSuiteName",s)},expression:"batchForm.testSuiteName"}}),t("t-tooltip",{attrs:{content:"必填，将用于测试报告的显示"}},[t("t-icon",{staticStyle:{"margin-left":"8px"},attrs:{name:"help-circle"}})],1)],1),e.batchForm.strategy==="cases"?t("t-form-item",{staticStyle:{"margin-bottom":"16px"},attrs:{label:"产品线",name:"product"}},[t("t-select",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,placeholder:"请选择产品线",clearable:""},model:{value:e.batchForm.product,callback:function(s){e.$set(e.batchForm,"product",s)},expression:"batchForm.product"}})],1):e._e(),e.batchForm.strategy==="cases"?t("t-form-item",{attrs:{label:"选择用例",name:"selectedCases"}},[t("div",{staticStyle:{display:"flex","align-items":"center",width:"100%"}},[t("t-select",{staticStyle:{flex:"1"},attrs:{multiple:"",filterable:"",remote:"",minCollapsedNum:2,options:e.caseOptions,loading:e.caseLoading,"on-search":e.remoteCaseSearch,placeholder:"请选择用例",max:20},on:{focus:function(s){return e.remoteCaseSearch("")}},model:{value:e.selectedCases,callback:function(s){e.selectedCases=s},expression:"selectedCases"}}),t("t-tooltip",{attrs:{content:"最多可选择20个用例进行批量执行"}},[t("t-icon",{staticStyle:{"margin-left":"8px"},attrs:{name:"help-circle"}})],1)],1)]):e._e(),e.batchForm.strategy==="group"?t("t-form-item",{attrs:{label:"用例分组",name:"groupName"}},[t("t-select",{attrs:{filterable:"",options:e.groupNameOptions,placeholder:"请选择分组"},model:{value:e.batchForm.groupName,callback:function(s){e.$set(e.batchForm,"groupName",s)},expression:"batchForm.groupName"}})],1):e._e(),t("t-form-item",{attrs:{label:"通知群",name:"notifyUrl"}},[t("t-select",{attrs:{options:e.notifyGroupOptions,placeholder:"请选择通知群",filterable:"",loading:e.notifyGroupLoading,clearable:"",remote:"","on-search":e.searchNotifyGroups},on:{focus:e.fetchNotifyGroups},model:{value:e.batchForm.notifyUrl,callback:function(s){e.$set(e.batchForm,"notifyUrl",s)},expression:"batchForm.notifyUrl"}})],1),t("t-form-item",{attrs:{label:"浏览器",name:"browser"}},[t("t-select",{attrs:{options:e.browserOptions,placeholder:"请选择浏览器"},model:{value:e.batchForm.browser,callback:function(s){e.$set(e.batchForm,"browser",s)},expression:"batchForm.browser"}})],1),t("t-form-item",{attrs:{label:"项目类型",name:"projectType",required:""}},[t("t-select",{attrs:{options:e.projectTypeOptions,placeholder:"请选择项目类型"},model:{value:e.batchForm.projectType,callback:function(s){e.$set(e.batchForm,"projectType",s)},expression:"batchForm.projectType"}})],1),t("t-form-item",{attrs:{label:"执行通道",name:"concurrency"}},[t("t-select",{attrs:{loading:e.queueLoading,placeholder:"请选择执行通道"},model:{value:e.batchForm.concurrency,callback:function(s){e.$set(e.batchForm,"concurrency",s)},expression:"batchForm.concurrency"}},e._l(e.queueOptions,function(s){return t("t-option",{key:s.value,attrs:{label:s.label,value:s.value}},[t("t-tag",{staticStyle:{"margin-right":"10px"},attrs:{theme:"success"}},[e._v(e._s(s.queueLength?s.queueLength+"个执行中":"空闲"))]),t("span",[e._v(e._s(s.label))])],1)}),1)],1),t("t-form-item",{attrs:{label:"执行环境",name:"env",required:""}},[t("t-select",{attrs:{options:e.envOptions,placeholder:"请选择执行环境"},model:{value:e.batchForm.env,callback:function(s){e.$set(e.batchForm,"env",s)},expression:"batchForm.env"}})],1)],1)],1),t("t-dialog",{attrs:{header:"执行用例",closeOnOverlayClick:!1,visible:e.executeDialogVisible,onClose:function(){e.executeDialogVisible=!1}},on:{"update:visible":function(s){e.executeDialogVisible=s}},scopedSlots:e._u([{key:"footer",fn:function(){return[t("t-button",{attrs:{theme:"default"},on:{click:function(s){e.executeDialogVisible=!1}}},[e._v("取消")]),t("t-button",{attrs:{theme:"primary"},on:{click:e.handleConfirmExecute}},[e._v("确认")])]},proxy:!0}])},[t("t-form",{attrs:{data:e.executeForm,"label-width":"120px"}},[t("t-form-item",{attrs:{label:"通知群",name:"notifyUrl"}},[t("t-select",{attrs:{options:e.notifyGroupOptions,placeholder:"请选择通知群",filterable:"",loading:e.notifyGroupLoading,clearable:"",remote:"","on-search":e.searchNotifyGroups},on:{focus:e.fetchNotifyGroups},model:{value:e.executeForm.notifyUrl,callback:function(s){e.$set(e.executeForm,"notifyUrl",s)},expression:"executeForm.notifyUrl"}})],1),t("t-form-item",{attrs:{label:"浏览器",name:"browser"}},[t("t-select",{attrs:{options:e.browserOptions,placeholder:"请选择浏览器"},model:{value:e.executeForm.browser,callback:function(s){e.$set(e.executeForm,"browser",s)},expression:"executeForm.browser"}})],1),t("t-form-item",{attrs:{label:"项目类型",name:"projectType",required:""}},[t("t-select",{attrs:{options:e.projectTypeOptions,placeholder:"请选择项目类型"},model:{value:e.executeForm.projectType,callback:function(s){e.$set(e.executeForm,"projectType",s)},expression:"executeForm.projectType"}})],1),t("t-form-item",{attrs:{label:"执行通道",name:"concurrency"}},[t("t-select",{attrs:{loading:e.queueLoading,placeholder:"请选择执行通道"},model:{value:e.executeForm.concurrency,callback:function(s){e.$set(e.executeForm,"concurrency",s)},expression:"executeForm.concurrency"}},e._l(e.queueOptions,function(s){return t("t-option",{key:s.value,attrs:{label:s.label,value:s.value}},[t("t-tag",{staticStyle:{"margin-right":"10px"},attrs:{theme:"success"}},[e._v(e._s(s.queueLength?s.queueLength+"个执行中":"空闲"))]),t("span",[e._v(e._s(s.label))])],1)}),1)],1),t("t-form-item",{attrs:{label:"执行环境",name:"env",required:""}},[t("t-select",{attrs:{options:e.envOptions,placeholder:"请选择执行环境"},model:{value:e.executeForm.env,callback:function(s){e.$set(e.executeForm,"env",s)},expression:"executeForm.env"}})],1)],1)],1)],1)],1)},L=[];const O={name:"list-table",components:{Trend:N},data(){return{queueOptions:[],queueLoading:!1,queueData:{},CONTRACT_STATUS_OPTIONS:S,CONTRACT_TYPES:m,CONTRACT_TYPE_OPTIONS:_,CONTRACT_PAYMENT_TYPES:w,prefix:$,formData:{caseName:"",app:"",product:"",groupName:""},productOptions:[],productLineLoading:!1,appLoading:!1,projectTypeOptions:[{label:"项目",value:"project"},{label:"迭代",value:"iteration"},{label:"调试",value:"debug"}],editForm:{caseName:"",scripts:[],config:"",groupName:"",app:""},scriptOptions:[],configOptions:[],groupNameOptions:[],appOptions:[],scriptLoading:!1,configLoading:!1,data:[],dataLoading:!1,value:"first",runDialogVisible:!1,editDialogVisible:!1,isEditMode:!1,selectedBrowser:"",notifyUrl:"",columns:[{title:"用例ID",width:80,colKey:"id",fixed:"left"},{title:"用例名称",width:140,ellipsis:!0,colKey:"caseName",fixed:"left"},{title:"业务域",width:140,ellipsis:!0,colKey:"app",fixed:"left"},{title:"产品线",width:140,ellipsis:!0,colKey:"product"},{title:"分组",colKey:"groupName",width:120,cell:(e,{row:a})=>{var t;return(t=a.groupName)==null?void 0:t.map(s=>e("t-tag",{props:{theme:"primary",variant:"light",size:"small"},style:{marginLeft:"4px"}},s))}},{title:"编辑人",colKey:"updater",width:120},{title:"编辑时间",colKey:"updatedAt",width:150,cell:(e,{row:a})=>new Date(a.updatedAt).toLocaleString()},{fixed:"right",width:220,colKey:"op",title:"操作"}],rowKey:"index",tableLayout:"auto",verticalAlign:"top",bordered:!0,hover:!0,rowClassName:e=>`${e}-class`,pagination:{current:1,pageSize:10,total:1},confirmVisible:!1,deleteIdx:-1,batchDialogVisible:!1,executeDialogVisible:!1,selectedCases:[],batchForm:{strategy:"group",groupName:"",notifyUrl:"",browser:"edge",product:"",projectType:"",testSuiteName:"",concurrency:"c1",env:"test"},batchExecuting:!1,executeForm:{notifyUrl:"",browser:"edge",projectType:"",concurrency:"c1",env:"test"},notifyGroupOptions:[],notifyGroupLoading:!1,browserOptions:[{label:"Chrome",value:"chrome"},{label:"Chrome https",value:"chrome --allow-insecure-localhost"},{label:"Edge",value:"edge"},{label:"Chrome:iPhone https",value:"chrome:emulation:device=iphone X --allow-insecure-localhost"},{label:"Chrome:Andriod https",value:"chrome:emulation:device=Pixel 7 --allow-insecure-localhost"},{label:"Chrome Headless",value:"chrome:headless"},{label:"Chrome Headless https",value:"chrome:headless --allow-insecure-localhost"},{label:"Edge Headless",value:"edge:headless"}],envOptions:[{label:"测试环境",value:"test"},{label:"模拟环境",value:"pre"},{label:"生产环境",value:"prod"}],caseOptions:[],caseLoading:!1}},computed:{confirmBody(){var e;if(this.deleteIdx>-1){const{caseName:a}=(e=this.data)==null?void 0:e[this.deleteIdx];return`删除后，${a}的所有用例信息将被清空，且无法恢复`}return""},offsetTop(){return this.$store.state.setting.isUseTabsRouter?48:0}},async mounted(){this.fetchQueueData(),this.fetchCaseList({page:this.pagination.current,limit:this.pagination.pageSize}),this.fetchAppOptions(),this.fetchProductLineOptions();const e=await this.$request.get("/cases/groupNames");this.groupNameOptions=e.data.map(a=>({label:`${a.groupName}(${a.count}条用例)`,value:a.groupName}))},methods:{fetchQueueData(){this.queueLoading=!0,this.$request.get("/cases/queueData").then(e=>{const a=e.data.data;this.queueData=a,this.queueOptions=[{label:"串行通道1",value:"c1",queueLength:a.c1||0},{label:"串行通道2",value:"c2",queueLength:a.c2||0},{label:"串行通道3",value:"c3",queueLength:a.c3||0},{label:"串行通道4",value:"c4",queueLength:a.c4||0},{label:"并发通道",value:"c5",queueLength:a.c5||0}],console.log(this.queueOptions);const t=this.queueOptions.filter(s=>s.queueLength===0);t.length>0?(this.batchForm.concurrency=t[0].value,this.executeForm.concurrency=t[0].value):(this.batchForm.concurrency="",this.executeForm.concurrency="",this.$message.error("所有队列都在使用中，请稍后再试"))}).catch(e=>{console.error("获取队列数据失败:",e),this.$message.error("获取队列数据失败")}).finally(()=>{this.queueLoading=!1})},onPageChange(e){this.pagination.current=e.current,this.pagination.pageSize=e.pageSize,this.fetchCaseList({...this.formData,page:this.pagination.current,limit:this.pagination.pageSize})},fetchCaseList(e={}){this.dataLoading=!0,this.$request.get("/cases/list",{params:e}).then(a=>{a&&(this.data=a.data.data,this.pagination={...this.pagination,total:a.data.total||this.data.length}),Object.keys(e).length?this.$message.success("查询成功"):console.log(a)}).catch(a=>{console.log(a),Object.keys(e).length&&this.$message.error("查询失败")}).finally(()=>{this.dataLoading=!1})},getContainer(){return document.querySelector(".tdesign-starter-layout")},onReset(e){this.formData={caseName:"",status:void 0,groupName:void 0}},onSubmit(e){this.dataLoading=!0,this.$request.get("/cases/list",{params:{caseName:this.formData.caseName,app:this.formData.app,product:this.formData.product,groupName:this.formData.groupName,creator:this.formData.creator}}).then(a=>{a&&(this.data=a.data.data,this.pagination.total=a.data.total),this.$message.success("查询成功")}).catch(a=>{console.log(a),this.$message.error("查询失败")}).finally(()=>{this.dataLoading=!1})},handleClickDelete(e){this.deleteIdx=e,this.confirmVisible=!0},onConfirmDelete(){this.dataLoading=!0,this.$request.delete(`/cases/${this.deleteIdx.id}`).then(()=>{this.pagination.total=this.data.length,this.$message.success("删除成功"),this.fetchCaseList({page:this.pagination.current,limit:this.pagination.pageSize})}).catch(e=>{console.log(e),this.$message.error("删除失败")}).finally(()=>{this.dataLoading=!1,this.confirmVisible=!1,this.resetIdx()})},onCancel(){this.resetIdx()},onCancelEdit(){this.editDialogVisible=!1},resetIdx(){this.deleteIdx=-1},handlePreview(e){this.currentCase=e,this.previewDialogVisible=!0},handleAddCase(){this.isEditMode=!1,this.currentCase={},this.editDialogVisible=!0,this.editForm={caseName:"",scripts:[],config:""},this.remoteScriptSearch(),this.remoteConfigSearch()},async handleEdit(e){var a;this.isEditMode=!0,this.currentCase=e,this.editDialogVisible=!0,this.editForm={caseName:e.caseName,scripts:e.scripts.map(t=>t.id),config:((a=e.config)==null?void 0:a.id)||""},window.open(`/userCase/edit?id=${e.id}`)},async remoteScriptSearch(e){this.scriptLoading=!0;try{const a=await this.$request.get("/scripts/search",{params:{keyword:e}});this.scriptOptions=a.data.map(t=>({label:t.scriptName,value:t.id}))}catch(a){console.error(a),this.$message.error("搜索脚本失败")}finally{this.scriptLoading=!1}},async remoteConfigSearch(e){this.configLoading=!0;try{const a=await this.$request.get("/configs/search",{params:{keyword:e}});this.configOptions=a.data.map(t=>({label:t.configName,value:t.id}))}catch(a){console.error(a),this.$message.error("搜索配置失败")}finally{this.configLoading=!1}},async remoteCaseSearch(e){this.caseLoading=!0;try{const a=await this.$request.get("/cases/search",{params:{keyword:e}}),t=this.batchForm.product?a.data.filter(s=>s.product===this.batchForm.product):a.data;this.caseOptions=t.map(s=>({label:s.caseName,value:JSON.stringify(s)}))}catch(a){console.error(a),this.$message.error("搜索用例失败")}finally{this.caseLoading=!1}},handleSaveEdit(){this.dataLoading=!0,this.editForm.creator=this.$store.state.user.userInfo.alias;const e=this.isEditMode?`/cases/${this.currentCase.id}`:"/cases/create";this.$request.post(e,this.editForm).then(a=>{console.log(a),this.$message.success(this.isEditMode?"保存成功":"新增成功"),this.editDialogVisible=!1,this.fetchCaseList({page:this.pagination.current,limit:this.pagination.pageSize})}).catch(a=>{console.log(a),this.$message.error(this.isEditMode?"保存失败":"新增失败")}).finally(()=>{this.dataLoading=!1})},handleAdd(){this.$router.push({path:"/userCase/edit",query:{}})},async handleDownload(e){try{const a=F(e);console.log("orginData",a);const t=C(a);console.log("transformedData",t);const s=await this.$request.post("/generate",t);s.data.data?(window.location.href=s.data.data,this.$message.success(`开始下载用例${e.caseName}的xmind文件`)):this.$message.error("获取下载链接失败")}catch(a){console.error(a),this.$message.error("下载失败")}},async handleExecuteGroup(){try{this.batchDialogVisible=!0,this.fetchNotifyGroups()}catch(e){console.error(e),this.$message.error("获取分组数据失败")}},handleExecute(e){this.executeDialogVisible=!0,this.currentCase=e,this.executeForm={notifyUrl:"",env:"test",browser:"chrome",projectType:"",concurrency:"c1"},this.fetchNotifyGroups(),this.fetchQueueData()},async fetchNotifyGroups(){this.notifyGroupLoading=!0;try{const e=await this.$request.get("/variables/search",{params:{keyword:"群"}});e.data&&Array.isArray(e.data)?(this.notifyGroupOptions=e.data.map(a=>({label:a.variableName,value:a.data[0].value.trim()})),this.notifyGroupOptions.unshift({label:"暂不通知",value:""})):this.notifyGroupOptions=[{value:"",label:"暂不通知"}]}catch(e){console.error(e),this.$message.error("获取通知群数据失败")}finally{this.notifyGroupLoading=!1}},async searchNotifyGroups(e){if(!e){await this.fetchNotifyGroups();return}this.notifyGroupLoading=!0;try{const a=await this.$request.get("https://ui-test.tsign.cn/variables/search",{params:{keyword:e}});a.data&&Array.isArray(a.data)&&(this.notifyGroupOptions=a.data.map(t=>({label:t.variableName,value:t.data[0].value.trim()})))}catch(a){console.error(a)}finally{this.notifyGroupLoading=!1}},handleConfirmExecute(){const e=this.currentCase;if(!this.executeForm.projectType){this.$message.error("请选择项目类型");return}if(!this.executeForm.env){this.$message.error("请选择执行环境");return}const a=this.$dialog.confirm({header:"请确认执行用例",body:`确定要执行用例 ${e.caseName} 吗?`,confirmBtn:"确认",onConfirm:()=>{a.destroy(),this.executeDialogVisible=!1,this.$request.post("/cases/runTest",{...e,operator:this.$store.state.user.userInfo.alias,notifyUrl:this.executeForm.notifyUrl,browser:this.executeForm.browser,projectType:this.executeForm.projectType,concurrency:this.executeForm.concurrency,env:this.executeForm.env}).then(t=>{t.data.code===500&&this.$message.error(t.data.message+t.data.error),t.data.data.status=="success"?(this.$message.success("用例开始执行中"),this.$router.push("/record")):this.$message.error(t.data.data.message)}).catch(t=>{console.log(t),this.$message.error("用例执行失败,稍后再试")})},onClose:()=>{a.destroy()}})},async fetchAppOptions(){this.appLoading=!0;try{const e=await this.$request.get("/cases/apps");e.data&&Array.isArray(e.data)&&(this.appOptions=[{label:"全部业务域",value:""},...e.data.map(a=>({label:a,value:a}))])}catch(e){console.error(e),this.$message.error("获取业务域数据失败")}finally{this.appLoading=!1}},async fetchProductLineOptions(){this.productLineLoading=!0;try{const e=await this.$request.get("/cases/products");e.data&&Array.isArray(e.data)&&(this.productOptions=[{label:"全部产品线",value:""},...e.data.map(a=>({label:a,value:a}))])}catch(e){console.error(e),this.$message.error("获取产品线数据失败")}finally{this.productLineLoading=!1}},async handleBatchExecute(){if(this.batchExecuting){this.$message.warning("正在执行中，请勿重复提交");return}try{if(!this.batchForm.testSuiteName){this.$message.error("请输入测试集名称");return}if(this.batchForm.strategy==="group"&&!this.batchForm.groupName){this.$message.error("请选择用例分组");return}if(this.batchForm.strategy==="cases"&&!this.selectedCases.length){this.$message.error("请选择用例");return}if(!this.batchForm.projectType){this.$message.error("请选择项目类型");return}if(!this.batchForm.env){this.$message.error("请选择执行环境");return}this.batchExecuting=!0;let e=[];this.batchForm.strategy==="cases"&&(e=this.selectedCases.map(n=>JSON.parse(n)).map(n=>n.id));const a=`${this.batchForm.groupName||"custom"}_${Math.floor(new Date().getTime()/1e3)}`,t=await this.$request.post("/cases/runTestGroup",{cases:this.batchForm.strategy==="cases"?e:"",runId:a,testSuiteName:this.batchForm.testSuiteName||a,operator:this.$store.state.user.userInfo.alias,strategy:this.batchForm.strategy,groupName:this.batchForm.strategy==="group"&&this.batchForm.groupName||"",notifyUrl:this.batchForm.notifyUrl,browser:this.batchForm.browser,projectType:this.batchForm.projectType,concurrency:this.batchForm.concurrency,env:this.batchForm.env});if(t.data.code===500){this.$message.error(t.data.message+t.data.error);return}this.$message.success("批量执行任务已创建"),this.$router.push("/record"),this.batchDialogVisible=!1}catch(e){console.error(e),this.$message.error("批量执行失败")}finally{this.batchExecuting=!1}}}},b={};var D=v(O,k,L,!1,T,"9d6ca6b6",null,null);function T(e){for(let a in b)this[a]=b[a]}const q=function(){return D.exports}(),E={name:"ListFilter",components:{CommonTable:q}};var A=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("t-card",{attrs:{bordered:!1}},[t("common-table")],1)},j=[];const y={};var V=v(E,A,j,!1,G,null,null,null);function G(e){for(let a in y)this[a]=y[a]}const U=function(){return V.exports}();export{U as default};
