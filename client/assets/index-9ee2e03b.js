import{V as s,n as o}from"./index-8c8571d2.js";var i=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("span",{class:e.containerCls},[t("span",{class:e.iconCls},[e.type==="down"?t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M11.5 8L8 11.5L4.5 8",stroke:"currentColor","stroke-width":"1.5"}}),t("path",{attrs:{d:"M8 11L8 4",stroke:"currentColor","stroke-width":"1.5"}})]):t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("path",{attrs:{d:"M4.5 8L8 4.5L11.5 8",stroke:"currentColor","stroke-width":"1.5"}}),t("path",{attrs:{d:"M8 5V12",stroke:"currentColor","stroke-width":"1.5"}})])]),t("span",[e._v(e._s(e.describe))])])},a=[];const c=s.extend({name:"Trend",props:{type:String,describe:[String,Number],isReverseColor:Boolean},computed:{containerCls(){return["trend-container",{"trend-container__reverse":this.isReverseColor,"trend-container__up":!this.isReverseColor&&this.type==="up","trend-container__down":!this.isReverseColor&&this.type==="down"}]},iconCls(){return["trend-icon-container"]}}}),n={};var l=o(c,i,a,!1,_,"774aa0fa",null,null);function _(e){for(let r in n)this[r]=n[r]}const p=function(){return l.exports}();export{p as T};
