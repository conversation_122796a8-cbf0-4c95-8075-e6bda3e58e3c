import{n as l,p as n}from"./index-8c8571d2.js";var c=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"list-common-table"},[e("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:t.formData,"label-width":80,colon:""},on:{reset:t.onR<PERSON>t,submit:t.onSubmit}},[e("t-row",{attrs:{gutter:[16,24]}},[e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"用例名称",name:"caseName"}},[e("t-input",{staticClass:"form-item-content",attrs:{type:"search",placeholder:"请输入用例名称"},model:{value:t.formData.caseName,callback:function(r){t.$set(t.formData,"caseName",r)},expression:"formData.caseName"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"用例组",name:"groupName"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择用例组",clearable:"",loading:t.groupNameLoading,options:t.groupNameOptions},model:{value:t.formData.groupName,callback:function(r){t.$set(t.formData,"groupName",r)},expression:"formData.groupName"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"业务域",name:"app"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择业务域",clearable:"",loading:t.appLoading,options:t.appOptions},model:{value:t.formData.app,callback:function(r){t.$set(t.formData,"app",r)},expression:"formData.app"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"产品线",name:"product"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择产品线",clearable:"",loading:t.productLineLoading,options:t.productOptions},model:{value:t.formData.product,callback:function(r){t.$set(t.formData,"product",r)},expression:"formData.product"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"执行进度",name:"status"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择执行进度",clearable:"",options:[{label:"成功",value:"success"},{label:"失败",value:"failed"},{label:"手动停止",value:"stop"},{label:"队列中",value:"pending"},{label:"执行中",value:"running"}]},model:{value:t.formData.status,callback:function(r){t.$set(t.formData,"status",r)},expression:"formData.status"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"执行结果",name:"runResult"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择执行结果",clearable:"",options:[{label:"成功",value:"success"},{label:"失败",value:"fail"}]},model:{value:t.formData.runResult,callback:function(r){t.$set(t.formData,"runResult",r)},expression:"formData.runResult"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"项目类型",name:"projectType"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择执行结果",clearable:"",options:[{label:"调试",value:"debug"},{label:"项目",value:"project"},{label:"迭代",value:"iteration"}]},model:{value:t.formData.projectType,callback:function(r){t.$set(t.formData,"projectType",r)},expression:"formData.projectType"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"执行人",name:"operator"}},[e("t-input",{staticClass:"form-item-content",attrs:{clearable:"",type:"search",placeholder:"请输入执行人"},model:{value:t.formData.operator,callback:function(r){t.$set(t.formData,"operator",r)},expression:"formData.operator"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"执行时间",name:"timeRange"}},[e("t-date-range-picker",{staticClass:"form-item-content",attrs:{enableTimePicker:"",valueType:"time-stamp","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",clearable:""},model:{value:t.formData.timeRange,callback:function(r){t.$set(t.formData,"timeRange",r)},expression:"formData.timeRange"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"失败原因",name:"reason"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择失败原因",clearable:"",options:t.reasonOptions},model:{value:t.formData.reason,callback:function(r){t.$set(t.formData,"reason",r)},expression:"formData.reason"}})],1)],1),e("t-col",{attrs:{span:3}},[e("t-form-item",{attrs:{label:"执行环境",name:"env"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择执行环境",clearable:"",options:t.envOptions},model:{value:t.formData.env,callback:function(r){t.$set(t.formData,"env",r)},expression:"formData.env"}})],1)],1)],1),e("t-row",{staticStyle:{"margin-top":"8px"}},[e("t-col",{staticClass:"operation-container",attrs:{span:24}},[e("t-button",{attrs:{theme:"primary",type:"submit"}},[t._v(" 查询 ")]),e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[t._v(" 重置 ")]),t.selectedRowKeys.length>0?e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{theme:"primary"},on:{click:t.showBatchMarkDialog}},[t._v(" 批量确认原因 ")]):t._e()],1)],1)],1),e("div",{staticClass:"table-container"},[e("t-table",{attrs:{data:t.data,columns:t.columns,"row-key":"id",verticalAlign:t.verticalAlign,hover:t.hover,pagination:t.pagination,loading:t.dataLoading,"selected-row-keys":t.selectedRowKeys},on:{"page-change":t.onPageChange,"select-change":t.rehandleSelectChange},scopedSlots:t._u([{key:"op",fn:function(r){var s=r.row;return[e("t-button",{attrs:{theme:"primary",variant:"text",disabled:s.runResult!=="fail"},on:{click:function(o){return t.showMarkDialog(s)}}},[t._v("失败原因")]),e("t-button",{attrs:{theme:"primary",variant:"text",disabled:s.status==="pending"},on:{click:function(o){return t.handle(s)}}},[t._v("用例信息")]),s.runResult==="fail"&&s.status!=="pending"?e("t-button",{attrs:{theme:"primary",variant:"text"},on:{click:function(o){return t.handleRetest(s)}}},[t._v("重试")]):t._e()]}}])})],1),e("t-dialog",{attrs:{header:"失败原因",visible:t.markDialogVisible,onConfirm:t.handleMarkConfirm},on:{"update:visible":function(r){t.markDialogVisible=r}}},[e("t-form",{attrs:{data:t.markForm,"label-width":"80px"}},[e("t-form-item",{attrs:{label:"原因"}},[e("t-select",{attrs:{options:t.reasonOptions,placeholder:"请选择未通过原因"},model:{value:t.markForm.reason,callback:function(r){t.$set(t.markForm,"reason",r)},expression:"markForm.reason"}})],1),e("t-form-item",{attrs:{label:"备注"}},[e("t-textarea",{attrs:{placeholder:"请输入备注信息"},model:{value:t.markForm.remark,callback:function(r){t.$set(t.markForm,"remark",r)},expression:"markForm.remark"}})],1)],1)],1),e("t-dialog",{attrs:{header:"批量确认原因",visible:t.batchMarkDialogVisible,onConfirm:t.handleBatchMarkConfirm},on:{"update:visible":function(r){t.batchMarkDialogVisible=r}}},[e("div",[t._v(" "+t._s(t.selectedRowKeys.map(function(r){return r}).join(",")))]),e("t-form",{attrs:{data:t.markForm,"label-width":"80px"}},[e("t-form-item",{attrs:{label:"原因"}},[e("t-select",{attrs:{options:t.reasonOptions,placeholder:"请选择未通过原因"},model:{value:t.markForm.reason,callback:function(r){t.$set(t.markForm,"reason",r)},expression:"markForm.reason"}})],1),e("t-form-item",{attrs:{label:"备注"}},[e("t-textarea",{attrs:{placeholder:"请输入备注信息"},model:{value:t.markForm.remark,callback:function(r){t.$set(t.markForm,"remark",r)},expression:"markForm.remark"}})],1)],1)],1)],1)},m=[];const p={name:"test-record",data(){return{markDialogVisible:!1,batchMarkDialogVisible:!1,currentRow:null,selectedRowKeys:[],reasonOptions:[{label:"业务BUG",value:"biz_bug"},{label:"业务变更",value:"biz_change"},{label:"环境异常",value:"env_bug"},{label:"脚本问题",value:"script_bug"},{label:"其他原因",value:"other"},{label:"未标记",value:"unmarked"}],markForm:{runResult:"success",reason:"",runInfo:"",remark:""},prefix:n,formData:{caseName:"",groupName:"",app:"",product:"",status:"",runResult:"",operator:"",timeRange:[],reason:"",env:""},envOptions:[{label:"全部环境",value:""},{label:"测试环境",value:"test"},{label:"模拟环境",value:"pre"},{label:"生产环境",value:"prod"}],groupNameOptions:[],groupNameLoading:!1,appOptions:[],appLoading:!1,productOptions:[],productLineLoading:!1,data:[],dataLoading:!1,verticalAlign:"top",hover:!0,pagination:{current:1,pageSize:10,total:1},columns:[{colKey:"row-select",type:"multiple",checkProps:({row:t,rowIndex:a})=>({disabled:t.runResult!=="fail"}),width:50},{title:"执行ID",colKey:"id",width:90,fixed:"left"},{title:"用例名称",colKey:"caseName",width:110,fixed:"left",cell:(t,{row:a})=>t("t-link",{props:{theme:"primary",hover:"color"},style:{cursor:"pointer"},on:{click:()=>this.goToCaseEdit(a.caseId)}},a.caseName)},{title:"执行进度",colKey:"status",width:120,cell:(t,{row:a})=>{const r={success:{name:"完成",theme:"success",icon:"check-circle-filled"},failed:{name:"未完成",theme:"danger",icon:"error-circle-filled"},stop:{name:"已终止",theme:"default",icon:"stop-circle"},pending:{name:"队列中",theme:"warning",icon:"pause-circle"},running:{name:"执行中",theme:"primary",icon:"loading"}}[a.status]||{name:a.status,theme:"default"};return t("t-tag",{props:{theme:r.theme,shape:"round"}},[t("t-icon",{props:{name:r.icon},style:{marginRight:"6px",verticalAlign:"text-top"}}),r.name])}},{title:"执行结果",colKey:"result",width:130,cell:(t,{row:a})=>{if(!a.runInfo)return t("t-tag",{props:{theme:"default",size:"small"}},"无数据");try{const e=a.runResult;if(e==="fail"){const r=this.reasonOptions.find(s=>s.value===a.reason);return t("t-tag",{props:{theme:"warning",size:"small"}},(r==null?void 0:r.label)||"未通过")}else return e==="success"?t("t-tag",{props:{theme:"primary",size:"small"}},"通过"):t("t-tag",{props:{theme:"default",size:"small"}},"无数据")}catch{return t("t-tag",{props:{theme:"default",size:"small"}},"无数据")}}},{title:"业务域",colKey:"app",width:140},{title:"产品线",colKey:"product",width:120},{title:"执行环境",colKey:"env",width:100,cell:(t,{row:a})=>{const r={test:"测试环境",pre:"模拟环境",prod:"生产环境"}[a.env]||a.env||"未知";return t("t-tag",{props:{theme:a.env==="prod"?"danger":a.env==="pre"?"warning":"primary",variant:"outline",size:"small"}},r)}},{title:"项目类型",colKey:"projectType",width:100,cell:(t,{row:a})=>{if(!a.projectType)return t("span","无");let e=a.projectType==="debug"?"调试":a.projectType==="project"?"项目":"迭代";const r=[t("t-tag",{props:{theme:"primary",variant:"outline",size:"small"}},e)];return t("div",{style:{display:"flex",flexWrap:"wrap",gap:"6px"}},r)}},{title:"用例组",colKey:"groupName",width:180,cell:(t,{row:a})=>{if(!a.groupName)return t("span","无");try{JSON.parse(a.groupName).length>0&&(a.groupName=JSON.parse(a.groupName))}catch{a.groupName=a.groupName}const r=Array.isArray(a.groupName)?a.groupName.map(s=>t("t-tag",{props:{theme:"primary",size:"small"},style:{marginRight:"6px"}},s)):[t("t-tag",{props:{theme:"primary",size:"small"}},a.groupName)];return t("div",{style:{display:"flex",flexWrap:"wrap",gap:"6px"}},r)}},{title:"执行时间",colKey:"startTime",width:180,cell:(t,{row:a})=>new Date(a.startTime).toLocaleString()},{title:"执行人",colKey:"operator",width:100},{title:"操作",colKey:"op",width:320,fixed:"right",align:"center"}]}},computed:{offsetTop(){return this.$store.state.setting.isUseTabsRouter?48:0}},mounted(){var t;this.fetchData(),this.fetchAppOptions(),this.fetchproductOptions(),this.fetchGroupNameOptions(),this.formData.operator=(t=this.$store.state.user.userInfo)==null?void 0:t.alias},methods:{onPageChange(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.fetchData({...this.formData,startTime:this.formData.timeRange[0],endTime:this.formData.timeRange[1]})},handle(t){window.open(`/record/result?id=${t.id}&caseId=${t.caseId}`)},showMarkDialog(t){this.currentRow=t;try{this.markForm.runInfo=t.runInfo,this.markForm.runResult=t.runResult||"fail",this.markForm.reason=t.reason}catch{this.markForm.runInfo=""}this.markForm.remark=t.remark||"",this.markDialogVisible=!0},showBatchMarkDialog(){this.markForm.reason="",this.markForm.remark="",this.batchMarkDialogVisible=!0},rehandleSelectChange(t,{selectedRowData:a}){this.selectedRowKeys=t,console.log(t,a)},handleMarkConfirm(){if(!this.markForm.reason){this.$message.warning("请选择原因");return}this.$request.post(`/records/${this.currentRow.id}`,{runInfo:JSON.stringify({...this.markForm.runInfo?JSON.parse(this.markForm.runInfo):{},runResult:this.markForm.runResult}),reason:this.markForm.reason,remark:this.markForm.remark}).then(()=>{this.markDialogVisible=!1,this.$message.success("标记成功"),this.fetchData()}).catch(t=>{console.error(t),this.$message.error("标记失败")})},handleBatchMarkConfirm(){if(!this.markForm.reason){this.$message.warning("请选择原因");return}this.$request.post("/records/bulkUpdate",{ids:this.selectedRowKeys,data:{reason:this.markForm.reason,remark:this.markForm.remark}}).then(()=>{this.batchMarkDialogVisible=!1,this.selectedRowKeys=[],this.$message.success("批量标记成功"),this.fetchData()}).catch(t=>{console.error(t),this.$message.error("批量标记失败")})},fetchData(t={}){this.dataLoading=!0,this.$request.get("/records/list",{params:{page:this.pagination.current,limit:this.pagination.pageSize,sort:"id",...this.formData,operator:this.formData.operator,...t}}).then(a=>{a&&(a.data&&a.data.data?(this.data=a.data.data,this.pagination.total=a.data.total||this.data.length):(this.data=[],this.pagination.total=0))}).catch(a=>{console.log(a)}).finally(()=>{this.dataLoading=!1})},async fetchAppOptions(){this.appLoading=!0;try{const t=await this.$request.get("/cases/apps");t.data&&Array.isArray(t.data)&&(this.appOptions=[{label:"全部业务域",value:""},...t.data.map(a=>({label:a,value:a}))])}catch(t){console.error(t),this.$message.error("获取业务域数据失败")}finally{this.appLoading=!1}},async fetchproductOptions(){this.productLineLoading=!0;try{const t=await this.$request.get("/cases/products");t.data&&Array.isArray(t.data)&&(this.productOptions=[{label:"全部产品线",value:""},...t.data.map(a=>({label:a,value:a}))])}catch(t){console.error(t),this.$message.error("获取产品线数据失败")}finally{this.productLineLoading=!1}},onReset(){this.formData={...this.formData,caseName:"",groupName:"",app:"",product:"",status:"",runResult:"",env:""}},onSubmit(){this.pagination.current=1,this.fetchData({...this.formData,startTime:this.formData.timeRange[0],endTime:this.formData.timeRange[1],reason:this.formData.reason,env:this.formData.env})},getContainer(){return document.querySelector(".tdesign-starter-layout")},async fetchGroupNameOptions(){this.groupNameLoading=!0;try{const t=await this.$request.get("/cases/groupNames");t.data&&Array.isArray(t.data)&&(this.groupNameOptions=t.data.map(a=>({label:`${a.groupName}`,value:a.groupName})))}catch(t){console.error(t),this.$message.error("获取用例组数据失败")}finally{this.groupNameLoading=!1}},viewReport(t){if(t.status==="running"){this.$message.error("正在执行中，请稍后");return}window.open(`/notify/getReports?caseName=${t.caseName}&fileTag=${t.fileTag}&groupName=${t.groupName}`,"_blank")},handleRetest(t){var a;this.$request.post("/cases/retest",{id:t.id,caseId:t.caseId,operator:(a=this.$store.state.user.userInfo)==null?void 0:a.alias}).then(()=>{this.$message.success("重新测试已触发"),this.fetchData()}).catch(()=>{this.$message.error("重新测试失败")})},goToCaseEdit(t){t?this.$router.push(`/userCase/edit?id=${t}`):this.$message.warning("用例ID不存在")}}},i={};var u=l(p,c,m,!1,d,"1f6c8f9c",null,null);function d(t){for(let a in i)this[a]=i[a]}const f=function(){return u.exports}();export{f as default};
