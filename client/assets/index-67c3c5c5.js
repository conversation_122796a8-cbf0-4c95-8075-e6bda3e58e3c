import{V as d,_ as h,u,I as f,a as m,n as L,S as I,p as M}from"./index-8c8571d2.js";import{S as A,J,E as v,b as K,j as g,o as B,l as F,i as H}from"./JsonEditor-1ff7e9a1.js";var q=["size"];function y(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function O(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?y(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):y(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var T={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7.35 8.65v3.85h1.3V8.65h3.85v-1.3H8.65V3.5h-1.3v3.85H3.5v1.3h3.85z",fillOpacity:.9}}]},R=d.extend({name:"AddIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,q),i=u(n),c=i.className,p=i.style,l=O(O({},o||{}),{},{id:"add",icon:T,staticClass:c,style:p});return s.props=l,a(f,s)}}),U=["size"];function b(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function w(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?b(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):b(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var G={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10 3H6V1.5H5V3H3a1 1 0 00-1 1v9a1 1 0 001 1h10a1 1 0 001-1V4a1 1 0 00-1-1h-2V1.5h-1V3zM5 5h1V4h4v1h1V4h2v2H3V4h2v1zM3 7h10v6H3V7z",fillOpacity:.9}}]},W=d.extend({name:"CalendarIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,U),i=u(n),c=i.className,p=i.style,l=w(w({},o||{}),{},{id:"calendar",icon:G,staticClass:c,style:p});return s.props=l,a(f,s)}}),Q=["size"];function _(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function S(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?_(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):_(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var X={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2.5 12a1 1 0 01-1-1V4a1 1 0 011-1h11a1 1 0 011 1v7a1 1 0 01-1 1h-11zm0-1h11V4h-11v7zM15 13H1v1h14v-1z",fillOpacity:.9}}]},Y=d.extend({name:"LaptopIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,Q),i=u(n),c=i.className,p=i.style,l=S(S({},o||{}),{},{id:"laptop",icon:X,staticClass:c,style:p});return s.props=l,a(f,s)}}),Z=["size"];function P(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function j(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?P(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):P(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var ee={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8 4a1 1 0 100-2 1 1 0 000 2zM8 9a1 1 0 100-2 1 1 0 000 2zM9 13a1 1 0 11-2 0 1 1 0 012 0z",fillOpacity:.9}}]},te=d.extend({name:"MoreIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,Z),i=u(n),c=i.className,p=i.style,l=j(j({},o||{}),{},{id:"more",icon:ee,staticClass:c,style:p});return s.props=l,a(f,s)}}),re=["size"];function C(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function $(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?C(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):C(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var ae={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2.52 6.37a5.5 5.5 0 0110.98.13v4c0 .05 0 .1-.02.15A4.5 4.5 0 019 14.7H8v-1h1a3.5 3.5 0 003.4-2.7h-1.9a.5.5 0 01-.5-.5v-4c0-.28.22-.5.5-.5h1.93a4.5 4.5 0 00-8.86 0H5.5c.28 0 .5.22.5.5v4a.5.5 0 01-.5.5H3a.5.5 0 01-.5-.5v-4c0-.04 0-.09.02-.13zM12.5 7H11v3h1.5V7zm-9 0v3H5V7H3.5z",fillOpacity:.9}}]},se=d.extend({name:"ServiceIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,re),i=u(n),c=i.className,p=i.style,l=$($({},o||{}),{},{id:"service",icon:ae,staticClass:c,style:p});return s.props=l,a(f,s)}}),ie=["size"];function z(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function D(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?z(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):z(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var ne={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8 1a2.5 2.5 0 00-2.5 2.5V5h-2a.5.5 0 00-.5.5v9c0 .*********.5h9a.5.5 0 00.5-.5v-9a.5.5 0 00-.5-.5h-2V3.5A2.5 2.5 0 008 1zm1.5 5v2h1V6H12v8H4V6h1.5v2h1V6h3zm0-1h-3V3.5a1.5 1.5 0 113 0V5z",fillOpacity:.9}}]},oe=d.extend({name:"ShopIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,ie),i=u(n),c=i.className,p=i.style,l=D(D({},o||{}),{},{id:"shop",icon:ne,staticClass:c,style:p});return s.props=l,a(f,s)}}),ce=["size"];function V(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,r)}return t}function x(e){for(var a=1;a<arguments.length;a++){var t=arguments[a]!=null?arguments[a]:{};a%2?V(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):V(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var pe={tag:"svg",attrs:{fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8 10.5c1.24 0 2.42.31 3.5.88v1.12h1v-1.14a.94.94 0 00-.49-.84 8.48 8.48 0 00-8.02 0 .94.94 0 00-.49.84v1.14h1v-1.12A7.47 7.47 0 018 10.5zM10.5 6a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-1 0a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z"}},{tag:"path",attrs:{fill:"currentColor",d:"M2.5 1.5a1 1 0 00-1 1v11a1 1 0 001 1h11a1 1 0 001-1v-11a1 1 0 00-1-1h-11zm11 1v11h-11v-11h11z"}}]},le=d.extend({name:"UserAvatarIcon",functional:!0,props:{size:{type:String},onClick:{type:Function}},render:function(a,t){var r=t.props,s=t.data,n=r.size,o=h(r,ce),i=u(n),c=i.className,p=i.style,l=x(x({},o||{}),{},{id:"user-avatar",icon:pe,staticClass:c,style:p});return s.props=l,a(f,s)}});const de={name:"ListCard",components:{ShopIcon:oe,CalendarIcon:W,ServiceIcon:se,UserAvatarIcon:le,LaptopIcon:Y,MoreIcon:te,AddIcon:R},props:{product:{type:Object}},data(){return{typeMap:["A","B","C","D","E"]}},methods:{handleManageProduct(e){this.$emit("manage-product",e)},handleDeleteItem(e){this.$emit("delete-item",e)}}};var he=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("t-card",{attrs:{theme:"poster2",bordered:!1},scopedSlots:e._u([{key:"avatar",fn:function(){return[t("t-avatar",{attrs:{size:"56px"},scopedSlots:e._u([{key:"icon",fn:function(){return[e._v(" "+e._s(e.product.id)+" ")]},proxy:!0}])})]},proxy:!0},{key:"content",fn:function(){return[t("p",{staticClass:"list-card-item_detail--name"},[e._v(e._s(e.product.scriptName))]),t("p",{staticClass:"list-card-item_detail--desc"},[e._v(e._s(e.product.description))])]},proxy:!0},{key:"actions",fn:function(){return[t("t-button",{attrs:{theme:"primary",shape:"square",variant:"text"},on:{click:function(r){return e.handleManageProduct(e.product)}}},[e._v(" 编辑")]),t("t-button",{attrs:{theme:"primary",shape:"square",variant:"text"},on:{click:function(r){return e.handleDeleteItem(e.product)}}},[e._v(" 删除")])]},proxy:!0}])})},ue=[];const N={};var fe=L(de,he,ue,!1,me,"7af9549c",null,null);function me(e){for(let a in N)this[a]=N[a]}const ve=function(){return fe.exports}(),E={scriptName:"",status:"",description:"",app:"",remark:"",content:'[{"text": "示例","type": "wait","value": "5000","desc": "等待5秒"}]',amount:0,editor:""},ge={name:"ListCard",components:{SearchIcon:I,ProductCard:ve,ScriptEditor:A,JsonEditor:J},data(){return{isFullscreen:!1,searchData:{scriptName:"",app:"",editor:""},pagination:{current:1,pageSize:10,total:1},prefix:M,productList:[],value:"first",rowKey:"index",tableLayout:"auto",verticalAlign:"top",bordered:!0,hover:!0,rowClassName:e=>`${e}-class`,columns:[{colKey:"id",title:"脚本ID",width:100,fixed:"left",align:"center"},{colKey:"scriptName",title:"脚本名称",width:220,fixed:"left"},{colKey:"app",title:"业务域",ellipsis:!0,width:120},{colKey:"description",title:"描述",width:220},{colKey:"editor",title:"编辑人",width:120},{colKey:"updatedAt",title:"编辑时间",width:150,cell:(e,{row:a})=>a.updatedAt?e("span",new Date(a.updatedAt).toLocaleString()):e("span","-")},{colKey:"usedInCases",title:"关联用例",width:150,cell:"usedInCases"},{colKey:"operation",title:"操作",cell:"operation",width:220,align:"center",fixed:"right"}],formData:{...E},options:[{label:"网关",value:"1"},{label:"人工智能",value:"2"},{label:"CVM",value:"3"}],searchApp:"",appOptions:[],appLoading:!1,textareaValue:"",rules:{scriptName:[{required:!0,message:"请输入脚本名称",type:"error"}],content:[{required:!0,message:"请输入脚本内容",type:"error"}]},searchValue:"",debounceTimer:null,confirmVisible:!1,deleteProduct:void 0,dataLoading:!1,visualEditorVisible:!1,jsonEditorVisible:!1}},computed:{confirmBody(){const{deleteProduct:e}=this;return e?`删除后，脚本 "${e.scriptName}" 和脚本历史变更记录将被永久删除，无法找回。请确认是否继续删除？`:""},scriptInfoData(){return{id:this.formData.id,scriptName:this.formData.scriptName||"",description:this.formData.description||"",app:this.formData.app||"",remark:this.formData.remark||""}}},mounted(){this.handleEscape=this.handleEscape.bind(this),document.addEventListener("keydown",this.handleEscape),this.$once("hook:beforeDestroy",()=>{document.removeEventListener("keydown",this.handleEscape)}),this.dataLoading=!0,this.fetchAppOptions(),this.fetchScriptList()},methods:{handleEscape(e){console.log(e),e.key==="Escape"&&this.isFullscreen&&(e.stopPropagation(),this.toggleFullscreen())},toggleFullscreen(){this.isFullscreen=!this.isFullscreen,this.$nextTick(()=>{const e=this.$refs.editorContainer;this.isFullscreen?(e.classList.add("fullscreen-editor"),this.editorView.requestMeasure()):e.classList.remove("fullscreen-editor"),this.editorView.requestMeasure()})},onReset(){this.searchData={scriptName:"",app:"",editor:""},this.fetchScriptList()},onSearchSubmit(){this.pagination.current=1,this.fetchScriptList({...this.searchData,page:this.pagination.current,limit:this.pagination.pageSize})},onPageChange(e){this.pagination.current=e.current,this.pagination.pageSize=e.pageSize,this.fetchScriptList()},validateJson(e){if(!e)return!1;try{return JSON.parse(e),!0}catch{return!1}},handleDeleteItem(e){if(e.usedByCases&&e.usedByCases.length>0){this.$message.warning(`脚本 "${e.scriptName}" 存在关联用例，无法删除`);return}this.confirmVisible=!0,this.deleteProduct=e},handleSearchChange(){this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(()=>{this.pagination.current=1,this.$request.get("/scripts/search",{params:{keyword:this.searchData.scriptName,app:this.searchData.app,editor:this.searchData.editor,page:this.pagination.current,limit:this.pagination.pageSize}}).then(e=>{e.data&&(this.productList=e.data.data,this.pagination.total=e.data.total||e.data.data.length)}).catch(()=>{this.$message.error("获取脚本列表失败")}).finally(()=>{this.dataLoading=!1})},300)},handleAppChange(){this.handleSearchChange()},async fetchAppOptions(){this.appLoading=!0;try{const e=await this.$request.get("https://m.esign.cn/infocenter-manager/forward/bizDomain/list");e.data&&Array.isArray(e.data.data)&&e.data.code==0?this.appOptions=e.data.data.map(a=>({label:a.name,value:a.name})):this.appOptions=[]}catch{this.appOptions=[]}finally{this.appLoading=!1}},onConfirmDelete(){const{id:e}=this.deleteProduct;this.dataLoading=!0,this.$request.delete(`/scripts/${e}`).then(a=>{a.data.code==400?this.$message.error(a.data.message):(this.$message.success("删除成功"),this.fetchScriptList())}).catch(a=>{console.error(a),this.$message.error("删除失败")}).finally(()=>{this.confirmVisible=!1,this.dataLoading=!1})},onCancel(){this.deleteProduct=void 0,this.formData={}},handleNewScript(){this.jsonEditorVisible=!0,this.formData={...E},this.$nextTick(()=>{this.editorView&&this.editorView.destroy(),this.initCodeMirror()})},handleManageProduct(e){this.formVisible=!0,this.formData={...e,index:e.index,status:e!=null&&e.isSetup?"1":"0"},this.$nextTick(()=>{this.editorView&&this.editorView.destroy(),this.initCodeMirror()})},manageScript(e,a){this.dataLoading=!0,this.$request.post(`/scripts/${e}`,a).then(()=>{this.$message.success("编辑脚本成功"),this.formVisible=!1,this.fetchScriptList()}).catch(t=>{console.error(t),this.$message.error("编辑脚本失败")}).finally(()=>{this.dataLoading=!1})},initCodeMirror(){this.$refs.editorContainer&&(this.editorView=new v({doc:this.formatCompressedJSON(this.formData.content)||"",extensions:[K,g(),B,F(),g(),H.of("  "),v.updateListener.of(e=>{e.docChanged&&(this.formData.content=e.state.doc.toString())})],parent:this.$refs.editorContainer}))},formatCompressedJSON(e){try{const a=JSON.parse(e);return JSON.stringify(a,(t,r)=>r,2)}catch(a){return console.error("JSON格式化失败:",a.message),this.$message.error("当前脚本格式不正确，JSON格式化失败"),this.$message.error(a.message),e}},fetchScriptList(){this.dataLoading=!0,this.$request.get("/scripts/list",{params:{page:this.pagination.current,limit:this.pagination.pageSize,keyword:this.searchData.scriptName,app:this.searchData.app,editor:this.searchData.editor}}).then(e=>{e.data&&(this.productList=e.data.data.map(a=>({...a,usedByCases:a.usedByCases||[]})),this.pagination={...this.pagination,total:e.data.total||e.data.data.length})}).catch(e=>{console.error(e),this.$message.error("获取脚本列表失败")}).finally(()=>{this.dataLoading=!1})},openVisualEditor(e){e&&(this.formData={...this.formData,id:e.id,scriptName:e.scriptName||"",description:e.description||"",app:e.app||"",remark:e.remark||"",content:e.content||'[{"text": "示例","type": "wait","value": "5000","desc": "等待5秒"}]'},this.editorView&&this.editorView.dispatch({changes:{from:0,to:this.editorView.state.doc.length,insert:this.formatCompressedJSON(this.formData.content)}})),this.visualEditorVisible=!0},onVisualEditorSave(e){if(this.formData={...this.formData,...e},this.editorView&&e.content&&this.editorView.dispatch({changes:{from:0,to:this.editorView.state.doc.length,insert:this.formatCompressedJSON(e.content)}}),!this.validateJson(e.content)){this.$message.error("内容必须是有效的JSON格式，请检查后重试");return}const a={scriptName:e.scriptName,content:e.content,app:e.app,description:e.description,remark:e.remark,editor:e.editor};e.id?this.manageScript(e.id,a):this.$request.post("/scripts/createData",a).then(()=>{this.$message.success("脚本保存成功"),this.fetchScriptList()}).catch(t=>{console.error(t),this.$message.error("脚本保存失败")})},onEditJson(){this.jsonEditorVisible=!0},onJsonEditorSave(e){if(this.formData={...this.formData,...e},this.editorView&&e.content&&this.editorView.dispatch({changes:{from:0,to:this.editorView.state.doc.length,insert:this.formatCompressedJSON(e.content)}}),!this.validateJson(e.content)){this.$message.error("内容必须是有效的JSON格式，请检查后重试");return}const a={scriptName:e.scriptName,content:e.content,app:e.app,description:e.description,remark:e.remark,editor:e.editor};e.id?this.manageScript(e.id,a):this.$request.post("/scripts/createData",a).then(()=>{this.$message.success("脚本保存成功"),this.fetchScriptList()}).catch(t=>{console.error(t),this.$message.error("脚本保存失败")})}}};var ye=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"list-card"},[t("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:e.searchData,"label-width":80,colon:""},on:{reset:e.onReset,submit:e.onSearchSubmit}},[t("t-row",{attrs:{gutter:[16,24]}},[t("t-col",{attrs:{span:4}},[t("t-form-item",{attrs:{label:"脚本名称",name:"scriptName"}},[t("t-input",{staticClass:"form-item-content",attrs:{type:"search",placeholder:"请输入脚本名称"},model:{value:e.searchData.scriptName,callback:function(r){e.$set(e.searchData,"scriptName",r)},expression:"searchData.scriptName"}})],1)],1),t("t-col",{attrs:{span:4}},[t("t-form-item",{attrs:{label:"业务域",name:"app"}},[t("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择业务域",filterable:"",clearable:"",loading:e.appLoading,options:e.appOptions},model:{value:e.searchData.app,callback:function(r){e.$set(e.searchData,"app",r)},expression:"searchData.app"}})],1)],1),t("t-col",{attrs:{span:4}},[t("t-form-item",{attrs:{label:"编辑人",name:"editor"}},[t("t-input",{staticClass:"form-item-content",attrs:{type:"search",placeholder:"请输入编辑人"},model:{value:e.searchData.editor,callback:function(r){e.$set(e.searchData,"editor",r)},expression:"searchData.editor"}})],1)],1)],1),t("t-row",{staticStyle:{"margin-top":"8px"}},[t("t-col",{staticClass:"operation-container",attrs:{span:24}},[t("t-button",{attrs:{theme:"primary",type:"submit"}},[e._v("查询")]),t("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[e._v("重置")]),t("t-button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleNewScript}},[e._v("新建脚本")])],1)],1)],1),e.pagination.total>0&&!e.dataLoading?[t("div",{staticClass:"list-card-items"},[t("t-table",{attrs:{data:e.productList,columns:e.columns,"row-key":e.rowKey,hover:e.hover,"row-class-name":e.rowClassName,pagination:e.pagination},on:{"page-change":e.onPageChange},scopedSlots:e._u([{key:"operation",fn:function(r){var s=r.row;return[t("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(n){return n.stopPropagation(),e.openVisualEditor(s)}}},[e._v("编辑")]),t("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(n){return n.stopPropagation(),e.handleDeleteItem(s)}}},[e._v("删除")])]}},{key:"usedInCases",fn:function(r){var s=r.row;return[s.usedInCases&&s.usedInCases.length?t("div",[s.usedInCases.length===1?t("t-tag",[e._v(e._s(s.usedInCases[0]))]):t("t-tooltip",{attrs:{placement:"top",content:s.usedInCases.join("、")}},[t("t-tag",{attrs:{theme:"primary"}},[e._v(e._s(s.usedInCases[0])+"+"+e._s(s.usedInCases.length)+" ")])],1)],1):t("span",{staticClass:"no-cases"},[e._v("无")])]}}],null,!1,876215601)})],1)]:e.dataLoading?t("div",{staticClass:"list-card-loading"},[t("t-loading",{attrs:{text:"加载中..."}})],1):e._e(),t("t-dialog",{attrs:{header:"删除脚本",body:e.confirmBody,visible:e.confirmVisible,onCancel:e.onCancel},on:{"update:visible":function(r){e.confirmVisible=r},confirm:e.onConfirmDelete}}),t("script-editor",{attrs:{visible:e.visualEditorVisible,"script-content":e.formData.content,"script-info":e.scriptInfoData},on:{"update:visible":function(r){e.visualEditorVisible=r},save:e.onVisualEditorSave,"edit-json":e.onEditJson}}),t("json-editor",{attrs:{visible:e.jsonEditorVisible,"script-content":e.formData.content,"script-info":e.scriptInfoData,"app-options":e.appOptions,"app-loading":e.appLoading},on:{"update:visible":function(r){e.jsonEditorVisible=r},save:e.onJsonEditorSave}})],2)},Oe=[];const k={};var be=L(ge,ye,Oe,!1,we,"7d6e7ae9",null,null);function we(e){for(let a in k)this[a]=k[a]}const Pe=function(){return be.exports}();export{Pe as default};
