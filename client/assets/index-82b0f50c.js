import{C as r}from"./index-8d65e48a.js";import{n as o}from"./index-8c8571d2.js";var c=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"config-list-container"},[e("t-card",[e("t-form",{ref:"form",style:{marginBottom:"8px"},attrs:{data:t.searchData,"label-width":80,colon:""},on:{reset:t.onReset,submit:t.onSearchSubmit}},[e("t-row",{attrs:{gutter:[16,24]}},[e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"配置名称",name:"configName"}},[e("t-input",{staticClass:"form-item-content",attrs:{type:"search",placeholder:"请输入配置名称"},model:{value:t.searchData.configName,callback:function(i){t.$set(t.searchData,"configName",i)},expression:"searchData.configName"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"创建人",name:"creator"}},[e("t-input",{staticClass:"form-item-content",attrs:{type:"search",placeholder:"请输入创建人"},model:{value:t.searchData.creator,callback:function(i){t.$set(t.searchData,"creator",i)},expression:"searchData.creator"}})],1)],1),e("t-col",{attrs:{span:6}},[e("t-form-item",{attrs:{label:"业务域",name:"app"}},[e("t-select",{staticClass:"form-item-content",attrs:{placeholder:"请选择业务域",filterable:"",clearable:"",loading:t.appLoading,options:t.appOptions},model:{value:t.searchData.app,callback:function(i){t.$set(t.searchData,"app",i)},expression:"searchData.app"}})],1)],1)],1),e("t-row",{staticStyle:{"margin-top":"8px"}},[e("t-col",{staticClass:"operation-container",attrs:{span:24}},[e("t-button",{attrs:{theme:"primary",type:"submit"}},[t._v("查询")]),e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"reset",variant:"base",theme:"default"}},[t._v("重置")]),e("t-button",{staticStyle:{"margin-left":"8px"},on:{click:t.handleAdd}},[t._v("新增配置")])],1)],1)],1),e("t-table",{attrs:{data:t.tableData,columns:t.columns,"row-key":"id",pagination:t.pagination,loading:t.tableLoading},on:{"page-change":t.onPageChange},scopedSlots:t._u([{key:"usedByCases",fn:function(i){return[i.row.usedByCases&&i.row.usedByCases.length>0?e("div",[i.row.usedByCases.length===1?e("t-tag",{attrs:{theme:"primary",variant:"light"}},[t._v(" "+t._s(i.row.usedByCases[0].caseName)+" ")]):e("t-tooltip",{attrs:{content:t.getCasesTooltipContent(i.row.usedByCases),placement:"top"}},[e("t-tag",{attrs:{theme:"primary",variant:"light"}},[t._v(" "+t._s(i.row.usedByCases[0].caseName)+" (+"+t._s(i.row.usedByCases.length-1)+") ")])],1)],1):e("span",{staticClass:"no-usedByCases"},[t._v("暂无用例")])]}},{key:"operation",fn:function(i){return[e("t-space",[e("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(s){return t.handlePreview(i.row)}}},[t._v("预览")]),e("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(s){return t.handleEdit(i.row)}}},[t._v("编辑")]),e("t-button",{attrs:{variant:"text",theme:"primary"},on:{click:function(s){return t.handleCopy(i.row)}}},[t._v("复制")]),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.handleDelete(i.row)}}},[t._v("删除")])],1)]}}])})],1),e("config-preview",{attrs:{visible:t.previewDialogVisible,"preview-data":t.previewData},on:{close:function(i){t.previewDialogVisible=!1}}})],1)},l=[];const h={components:{ConfigPreview:r},data(){return{searchData:{configName:"",creator:"",app:""},appOptions:[],appLoading:!1,tableData:[],tableLoading:!1,columns:[{colKey:"id",title:"配置ID",width:60,fixed:"left"},{colKey:"configName",title:"配置名称",width:100,fixed:"left"},{colKey:"creator",title:"创建人",width:80},{colKey:"app",title:"业务域",width:120},{colKey:"usedByCases",title:"关联用例",width:150,cell:"usedByCases"},{colKey:"updatedAt",title:"更新时间",width:180,cell:(t,{row:a})=>t("span",this.$dayjs(a.updatedAt).format("YYYY-MM-DD HH:mm"))},{colKey:"operation",title:"操作",cell:"operation",fixed:"right",width:240}],pagination:{current:1,pageSize:10,total:1},dialogVisible:!1,formData:{name:""},previewDialogVisible:!1,previewData:null}},methods:{onReset(){this.searchData={configName:"",creator:"",app:""},this.pagination.current=1,this.fetchConfigList()},onSearchSubmit(){this.pagination.current=1,this.fetchConfigList()},onPageChange(t){this.pagination.current=t.current,this.pagination.pageSize=t.pageSize,this.fetchConfigList()},handleEdit(t){window.open(`/config/edit?id=${t.id}`)},async handleDelete(t){const a=await this.$dialog.confirm({header:"确认删除",body:`确定要删除配置 ${t.configName} 吗？`,confirmBtn:"确定",cancelBtn:"取消",onConfirm:async()=>{try{const e=await this.$request.delete(`/configs/${t.id}`);if(e.data.code===400){this.$message.error(e.data.message),a.destroy();return}this.$message.success("删除成功"),this.fetchConfigList(),a.destroy()}catch{this.$message.error("删除失败"),a.destroy()}}})},async handleCopy(t){try{await this.$request.post("/configs/create",{configName:t.configName+"copy",creator:this.$store.state.user.userInfo.alias,url:t.url,height:t.height,width:t.width,env:t.env,variables:t.variables.map(a=>a.id),localstorage:t.localstorage,session:t.session,cookie:t.cookie,request:t.request}),this.$message.success("复制成功"),this.fetchConfigList()}catch{this.$message.error("复制失败")}},handleAdd(){this.$router.push("/config/edit")},async handlePreview(t){try{const{data:a}=await this.$request.get(`/configs/${t.id}`);this.previewData=a,this.previewDialogVisible=!0}catch{this.$message.error("获取配置详情失败")}},getCasesTooltipContent(t){return t.map(a=>a.caseName).join("、")},async fetchConfigList(){this.tableLoading=!0;try{const{data:t}=await this.$request.get("/configs/list",{params:{page:this.pagination.current,limit:this.pagination.pageSize,configName:this.searchData.configName,creator:this.searchData.creator,app:this.searchData.app}});this.tableData=t.data.map(a=>({...a,usedByCases:a.usedByCases||[]})),this.pagination.total=t.total}catch{this.$message.error("获取配置列表失败")}finally{this.tableLoading=!1}},async fetchAppOptions(){this.appLoading=!0;try{const t=await this.$request.get("https://m.esign.cn/infocenter-manager/forward/bizDomain/list");t.data&&Array.isArray(t.data.data)&&t.data.code==0?this.appOptions=t.data.data.map(a=>({label:a.name,value:a.name})):this.appOptions=[]}catch{this.appOptions=[]}finally{this.appLoading=!1}}},mounted(){this.fetchAppOptions(),this.fetchConfigList()}},n={};var p=o(h,c,l,!1,d,"82d0291c",null,null);function d(t){for(let a in n)this[a]=n[a]}const u=function(){return p.exports}();export{u as default};
