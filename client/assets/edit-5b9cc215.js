import{V as p,u as h,a as m,C as d,n as f}from"./index-8c8571d2.js";function v(t,a){if(window){if(!document||!t||typeof t!="string"||document.querySelectorAll(".".concat(a,'[src="').concat(t,'"]')).length>0)return;var e=document.createElement("script");e.setAttribute("class",a),e.setAttribute("src",t),document.body.appendChild(e)}}var b={name:{type:String,default:""},size:{type:String,default:void 0},url:{type:[String,Array],default:void 0},loadDefaultIcons:{type:Boolean,default:!0},onClick:Function};function o(t,a){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);a&&(i=i.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),e.push.apply(e,i)}return e}function g(t){for(var a=1;a<arguments.length;a++){var e=arguments[a]!=null?arguments[a]:{};a%2?o(Object(e),!0).forEach(function(i){m(t,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):o(Object(e)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))})}return t}var c=d.classPrefix,l="".concat(c,"-icon"),y="https://tdesign.gtimg.com/icon/0.1.4/fonts/index.js",k=p.extend({name:"Icon",props:b,computed:{iconName:function(){return this.url?this.name:"".concat(l,"-").concat(this.name)},classes:function(){var a=h(this.size),e=a.className,i=this.url?this.name:"".concat(l,"-").concat(this.name),r=[l,i,e];return r},iconStyle:function(){return["small","medium","large"].includes(this.size)?{}:{"font-size":this.size}}},methods:{handleClick:function(a){var e;this.$emit("click",{e:a}),(e=this.onClick)===null||e===void 0||e.call(this,{e:a})}},mounted:function(){var a=[];this.url&&(a=this.url instanceof Array?this.url.concat():[this.url]),this.loadDefaultIcons&&a.push(y),Array.from(new Set(a)).forEach(function(e){v(e,"".concat(c,"-svg-js-stylesheet--unique-class"))})},render:function(){var a=arguments[0],e={href:"#".concat(this.iconName)};return a("svg",{class:this.classes,style:this.iconStyle,on:{click:this.handleClick}},[a("use",{attrs:g({},e)})])}}),S=k,x=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"config-edit-container"},[e("t-card",[e("t-form",{attrs:{data:t.formData,"label-width":"120px"}},[e("t-form-item",{attrs:{label:"配置名称"}},[e("t-input",{attrs:{placeholder:"请输入配置名称"},model:{value:t.formData.name,callback:function(i){t.$set(t.formData,"name",i)},expression:"formData.name"}})],1),e("t-form-item",{attrs:{label:"业务域"}},[e("t-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择业务域",filterable:"",clearable:"",loading:t.appLoading,options:t.appOptions},model:{value:t.formData.app,callback:function(i){t.$set(t.formData,"app",i)},expression:"formData.app"}})],1),e("t-form-item",{attrs:{label:"环境配置"}},[e("t-tabs",{staticClass:"env-config-tabs",attrs:{theme:"card"},model:{value:t.currentEnv,callback:function(i){t.currentEnv=i},expression:"currentEnv"}},t._l(t.envOptions,function(i){return e("t-tab-panel",{key:i.value,attrs:{value:i.value,label:i.label}},[e("t-space",{staticStyle:{width:"100%",gap:"16px"},attrs:{direction:"vertical"}},[e("t-form-item",{attrs:{label:"初始URL"}},[e("t-space",{attrs:{align:"center"}},[e("t-input",{staticStyle:{width:"400px"},attrs:{placeholder:"请输入初始URL"},model:{value:t.formData.envConfigs[i.value].url,callback:function(r){t.$set(t.formData.envConfigs[i.value],"url",r)},expression:"formData.envConfigs[env.value].url"}}),e("t-tooltip",{attrs:{content:"未配置url和环境值，当前环境将无法执行测试",placement:"top"}},[e("icon",{staticStyle:{color:"#999",cursor:"pointer"},attrs:{name:"help-circle"}})],1)],1)],1),e("t-form-item",{attrs:{label:"自定义变量"}},[t.currentEnv==="global"?e("t-select",{staticStyle:{width:"400px"},attrs:{multiple:"",filterable:"",placeholder:"请输入搜索",options:t.remoteOptions,loading:t.remoteLoading},on:{search:t.remoteSearch,change:t.onVariableChange},model:{value:t.selectedVariableIds,callback:function(r){t.selectedVariableIds=r},expression:"selectedVariableIds"}}):t._e(),t.formData.variables.length>0&&t.currentEnv!="global"?e("div",{staticStyle:{"margin-top":"12px"}},[e("t-alert",{staticStyle:{"margin-bottom":"8px"},attrs:{theme:"info",message:"环境变量值预览，未配置值，将无法执行测试"}}),t._l(t.formData.variables,function(r){return e("div",{key:r.id,staticStyle:{"margin-bottom":"8px"}},[e("t-space",{attrs:{align:"center"}},[e("span",{staticStyle:{width:"120px",display:"inline-block"}},[t._v(t._s(r.variableName)+":")]),e("t-input",{staticStyle:{width:"200px"},attrs:{disabled:"",value:t.getVariableValueByEnv(r,t.currentEnv),placeholder:t.getVariableValueByEnv(r,"global")||"请输入变量值"},on:{input:function(s){return t.updateVariableValue(r,t.currentEnv,s)}}}),t.getVariableValueByEnv(r,t.currentEnv)?t._e():e("t-button",{staticStyle:{"margin-left":"8px"},attrs:{theme:"primary",variant:"outline",size:"small"},on:{click:function(s){return t.goToVariableEdit(r.id)}}},[t._v(" 设置 ")])],1)],1)})],2):t._e()],1)],1)],1)}),1)],1),e("t-form-item",{attrs:{label:"初始宽高"}},[e("t-space",{attrs:{align:"center"}},[e("t-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择设备尺寸",clearable:""},on:{change:t.onSizeChange},model:{value:t.selectedSize,callback:function(i){t.selectedSize=i},expression:"selectedSize"}},[e("t-option-group",{attrs:{label:"PC设备"}},t._l(t.sizeOptions.slice(0,4),function(i){return e("t-option",{key:i.value,attrs:{value:i.value,label:i.label}})}),1),e("t-option-group",{attrs:{label:"移动设备"}},t._l(t.sizeOptions.slice(4),function(i){return e("t-option",{key:i.value,attrs:{value:i.value,label:i.label}})}),1),e("t-option",{attrs:{value:"custom",label:"自定义尺寸"}})],1),t.selectedSize==="custom"||!t.selectedSize?e("span",{staticStyle:{"margin-left":"16px",display:"flex"}},[e("t-input",{staticStyle:{width:"100px"},attrs:{placeholder:"宽度"},model:{value:t.formData.width,callback:function(i){t.$set(t.formData,"width",i)},expression:"formData.width"}}),e("span",{staticStyle:{margin:"0 8px"}},[t._v("×")]),e("t-input",{staticStyle:{width:"100px"},attrs:{placeholder:"高度"},model:{value:t.formData.height,callback:function(i){t.$set(t.formData,"height",i)},expression:"formData.height"}})],1):t._e()],1)],1),e("t-form-item",{attrs:{label:"Mock配置"}},[e("t-tabs",{style:{marginBottom:"16px",width:"600px"},model:{value:t.formData.mockType,callback:function(i){t.$set(t.formData,"mockType",i)},expression:"formData.mockType"}},[e("t-tab-panel",{attrs:{value:"localstorage",label:"LocalStorage"}},[e("t-alert",{staticStyle:{"margin-bottom":"12px"},attrs:{theme:"info",message:"用于模拟localStorage存储的键值对"}}),e("t-space",{staticStyle:{width:"100%",gap:"8px"},attrs:{direction:"vertical"}},[t._l(t.formData.localstorage,function(i,r){return e("t-form-item",{key:r},[e("t-input",{staticStyle:{width:"200px"},attrs:{placeholder:"键名"},model:{value:i.key,callback:function(s){t.$set(i,"key",s)},expression:"item.key"}}),e("t-input",{staticStyle:{width:"200px","margin-left":"8px"},attrs:{placeholder:"键值"},model:{value:i.value,callback:function(s){t.$set(i,"value",s)},expression:"item.value"}}),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.removeLocalstorageItem(r)}}},[e("icon",{attrs:{name:"delete"}})],1)],1)}),e("t-button",{attrs:{theme:"primary",variant:"outline"},on:{click:t.addLocalstorageItem}},[t._v("添加键值对")])],2)],1),e("t-tab-panel",{attrs:{value:"session",label:"Session"}},[e("t-alert",{staticStyle:{"margin-bottom":"12px"},attrs:{theme:"info",message:"用于模拟sessionStorage存储的键值对"}}),e("t-space",{staticStyle:{width:"100%",gap:"8px"},attrs:{direction:"vertical"}},[t._l(t.formData.session,function(i,r){return e("t-form-item",{key:r},[e("t-input",{staticStyle:{width:"200px"},attrs:{placeholder:"键名"},model:{value:i.key,callback:function(s){t.$set(i,"key",s)},expression:"item.key"}}),e("t-input",{staticStyle:{width:"200px","margin-left":"8px"},attrs:{placeholder:"键值"},model:{value:i.value,callback:function(s){t.$set(i,"value",s)},expression:"item.value"}}),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.removeSessionItem(r)}}},[e("icon",{attrs:{name:"delete"}})],1)],1)}),e("t-button",{attrs:{theme:"primary",variant:"outline"},on:{click:t.addSessionItem}},[t._v("添加键值对")])],2)],1),e("t-tab-panel",{attrs:{value:"cookie",label:"Cookie"}},[e("t-alert",{staticStyle:{"margin-bottom":"12px"},attrs:{theme:"info",message:"用于模拟cookie存储的键值对"}}),e("t-space",{staticStyle:{width:"100%",gap:"8px"},attrs:{direction:"vertical"}},[t._l(t.formData.cookie,function(i,r){return e("t-form-item",{key:r},[e("t-input",{staticStyle:{width:"150px"},attrs:{placeholder:"键名"},model:{value:i.key,callback:function(s){t.$set(i,"key",s)},expression:"item.key"}}),e("t-input",{staticStyle:{width:"150px","margin-left":"8px"},attrs:{placeholder:"键值"},model:{value:i.value,callback:function(s){t.$set(i,"value",s)},expression:"item.value"}}),e("t-input",{staticStyle:{width:"150px","margin-left":"8px"},attrs:{placeholder:"域名"},model:{value:i.domain,callback:function(s){t.$set(i,"domain",s)},expression:"item.domain"}}),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.removeCookieItem(r)}}},[e("icon",{attrs:{name:"delete"}})],1)],1)}),e("t-button",{attrs:{theme:"primary",variant:"outline"},on:{click:t.addCookieItem}},[t._v("添加键值对")])],2)],1),e("t-tab-panel",{attrs:{value:"request",label:"Request"}},[e("t-alert",{staticStyle:{"margin-bottom":"12px"},attrs:{theme:"info",message:"1. 包含匹配:给出匹配的路径的字符串2. 正则匹配:给出正则表达式 3. 返回值配置需要确定是否合并 4. 不合并时需要给出完整的响应体"}}),e("t-space",{staticStyle:{width:"100%",gap:"8px"},attrs:{direction:"vertical"}},[t._l(t.formData.request,function(i,r){return e("t-form-item",{key:r},[e("t-space",{staticStyle:{width:"100%",gap:"8px"},attrs:{direction:"vertical"}},[e("t-input",{staticStyle:{width:"100%"},attrs:{placeholder:"路径"},model:{value:i.path,callback:function(s){t.$set(i,"path",s)},expression:"item.path"}}),e("t-space",[e("t-select",{staticStyle:{width:"140px"},attrs:{placeholder:"方法"},model:{value:i.method,callback:function(s){t.$set(i,"method",s)},expression:"item.method"}},[e("t-option",{attrs:{value:"GET"}},[t._v("GET")]),e("t-option",{attrs:{value:"POST"}},[t._v("POST")]),e("t-option",{attrs:{value:"PUT"}},[t._v("PUT")]),e("t-option",{attrs:{value:"DELETE"}},[t._v("DELETE")])],1),e("t-select",{staticStyle:{width:"140px"},attrs:{placeholder:"匹配方式"},model:{value:i.type,callback:function(s){t.$set(i,"type",s)},expression:"item.type"}},[e("t-option",{attrs:{value:"regexp"}},[t._v("正则匹配")]),e("t-option",{attrs:{value:"include"}},[t._v("包含匹配")])],1),e("t-select",{staticStyle:{width:"140px"},attrs:{placeholder:"是否合并"},model:{value:i.merge,callback:function(s){t.$set(i,"merge",s)},expression:"item.merge"}},[e("t-option",{attrs:{value:"true"}},[t._v("合并")]),e("t-option",{attrs:{value:"false"}},[t._v("不合并")])],1)],1),e("t-textarea",{staticStyle:{width:"100%"},attrs:{placeholder:"响应内容",autosize:!0,height:124},model:{value:i.response,callback:function(s){t.$set(i,"response",s)},expression:"item.response"}})],1),e("t-button",{attrs:{variant:"text",theme:"danger"},on:{click:function(s){return t.removeRequestItem(r)}}},[e("icon",{attrs:{name:"delete"}})],1)],1)}),e("t-button",{attrs:{theme:"primary",variant:"outline"},on:{click:t.addRequestItem}},[t._v("添加请求")])],2)],1)],1)],1),e("t-form-item",[e("t-button",{attrs:{theme:"primary"},on:{click:t.handleSubmit}},[t._v("保存")]),e("t-button",{staticStyle:{"margin-left":"12px"},on:{click:t.handleCancel}},[t._v("取消")])],1)],1)],1)],1)},w=[];const D={components:{Icon:S},data(){return{envOptions:[{label:"全局",value:"global"},{label:"测试环境",value:"test"},{label:"预发环境",value:"pre"},{label:"生产环境",value:"prod"},{label:"项目环境1",value:"project1"},{label:"项目环境2",value:"project2"}],currentEnv:"global",remoteLoading:!1,remoteOptions:[],remoteOptionsRaw:[],appOptions:[],appLoading:!1,sizeOptions:[{label:"1366×768 (笔记本)",value:"1366x768",width:1366,height:768},{label:"1280×720 (HD)",value:"1280x720",width:1280,height:720},{label:"1024×768 (标准屏)",value:"1024x768",width:1024,height:768},{label:"1400×900 (宽屏)",value:"1400x900",width:1400,height:900},{label:"375×667 (iPhone 8)",value:"375x667",width:375,height:667},{label:"414×896 (iPhone X)",value:"414x896",width:414,height:896}],selectedSize:"",formData:{name:"",app:"",width:"1024",height:"768",variables:[],envConfigs:{global:{url:"",variableValues:{}},test:{url:"",variableValues:{}},pre:{url:"",variableValues:{}},prod:{url:"",variableValues:{}},project1:{url:"",variableValues:{}},project2:{url:"",variableValues:{}}},mockType:"localstorage",localstorage:[],session:[],cookie:[],request:[]}}},computed:{selectedVariableIds:{get(){return this.formData.variables.map(t=>t.id)||[]},set(t){}}},created(){const t=this.$route.query.id;t&&this.fetchConfigDetail(t)},mounted(){this.fetchAppOptions(),this.$nextTick(()=>{this.remoteSearch()}),this.initSelectedSize()},watch:{currentEnv:async function(t,a){t!==a&&await this.remoteSearch("")},"formData.variables":{handler(){},deep:!0}},methods:{async fetchConfigDetail(t){try{await this.remoteSearch("");const{data:a}=await this.$request.get(`/configs/detail?id=${t}`),e={global:{url:"",variableValues:{}},test:{url:"",variableValues:{}},pre:{url:"",variableValues:{}},prod:{url:"",variableValues:{}},project1:{url:"",variableValues:{}},project2:{url:"",variableValues:{}}};let i=[];a.envConfigs&&a.variables?(i=a.variables,Object.keys(a.envConfigs).forEach(r=>{e[r]&&(e[r]={url:a.envConfigs[r].url||"",variableValues:a.variables.map(s=>s.id)||{}})})):(a.variables&&Array.isArray(a.variables)&&a.variables.length>0&&(typeof a.variables[0]=="object"?i=a.variables:i=a.variables.map(r=>({id:r,variableName:`变量${r}`,data:[]}))),e[a.env||"global"]={url:a.url||"",variableValues:[]}),this.formData={name:a.configName,app:a.app||"",width:a.width,height:a.height,variables:i,envConfigs:e,mockType:"localstorage",localstorage:a.localstorage||[],session:a.session||[],cookie:a.cookie||[],request:a.request||[]},this.initSelectedSize(),this.currentEnv=a.env||"global"}catch{this.$message.error("获取配置详情失败")}},async handleSubmit(){try{await this.$request.post(`/configs/${this.$route.query.id?this.$route.query.id:"create"}`,{configName:this.formData.name,app:this.formData.app,creator:this.$store.state.user.userInfo.alias,variables:this.selectedVariableIds,envConfigs:this.formData.envConfigs,height:this.formData.height,width:this.formData.width,env:this.currentEnv,localstorage:this.formData.localstorage,session:this.formData.session,cookie:this.formData.cookie,request:this.formData.request}),this.$message.success("保存成功"),this.$router.push("/config/list")}catch(t){console.error(t),this.$message.error("保存失败")}},handleCancel(){this.$router.go(-1)},addLocalstorageItem(){this.formData.localstorage.push({key:"",value:""})},removeLocalstorageItem(t){this.formData.localstorage.splice(t,1)},addSessionItem(){this.formData.session.push({key:"",value:""})},removeSessionItem(t){this.formData.session.splice(t,1)},addCookieItem(){this.formData.cookie.push({key:"",value:"",domain:""})},removeCookieItem(t){this.formData.cookie.splice(t,1)},addRequestItem(){this.formData.request.push({path:"",method:"GET",type:"include",merge:!1,response:""})},removeRequestItem(t){this.formData.request.splice(t,1)},addCustomVar(){this.formData.variable.push("")},async remoteSearch(t){this.remoteLoading=!0;try{const{data:a}=await this.$request.get(`/variables/search?keyword=${t||""}`);this.remoteOptionsRaw=a,console.log(a),this.remoteOptions=a.map(e=>{let i="未配置",r="";if(e.data&&Array.isArray(e.data)){let s=e.data.find(u=>u.env==="global");s&&s.value&&(i=s.value,r=s.value),console.log(s)}return{value:e.id,label:`${e.variableName} (${i})`,name:e.variableName,rawValue:r}}),console.log(this.remoteOptions)}catch(a){console.error(a),this.$message.error("搜索变量失败")}finally{this.remoteLoading=!1}},removeCustomVar(t){this.formData.variable.splice(t,1)},onSizeChange(t){if(t&&t!=="custom"){const a=this.sizeOptions.find(e=>e.value===t);a&&(this.formData.width=a.width,this.formData.height=a.height)}},initSelectedSize(){if(this.formData.width&&this.formData.height){const t=this.sizeOptions.find(a=>a.width==this.formData.width&&a.height==this.formData.height);t?this.selectedSize=t.value:this.selectedSize="custom"}else this.selectedSize=""},getVariableName(t){const a=this.remoteOptions.find(e=>e.value===t);return a?a.label.split(" (")[0]:`变量${t}`},getVariableDefaultValue(t){const a=this.remoteOptions.find(e=>e.value===t);return a?a.rawValue:""},onVariableChange(t){this.formData.variables=t.map(a=>this.remoteOptionsRaw.find(i=>i.id===a)||{id:a,variableName:`变量${a}`,data:[]})},removeVariable(t){const a=this.formData.variables[t];this.formData.variables.splice(t,1),this.envOptions.forEach(e=>{this.$delete(this.formData.envConfigs[e.value].variableValues,a)})},getVariableValueByEnv(t,a){if(t.data&&Array.isArray(t.data)){const e=t.data.find(i=>i.env===a);return e?e.value:""}return""},updateVariableValue(t,a,e){t.data||this.$set(t,"data",[]);const i=t.data.find(r=>r.env===a);i?i.value=e:t.data.push({env:a,value:e})},goToVariableEdit(t){const a=`/variable/variableEdit?id=${t}`;window.open(a,"_blank")},async fetchAppOptions(){this.appLoading=!0;try{const t=await this.$request.get("https://m.esign.cn/infocenter-manager/forward/bizDomain/list");t.data&&Array.isArray(t.data.data)&&t.data.code==0?this.appOptions=t.data.data.map(a=>({label:a.name,value:a.name})):this.appOptions=[]}catch{this.appOptions=[]}finally{this.appLoading=!1}}}},n={};var _=f(D,x,w,!1,$,"50b44bc8",null,null);function $(t){for(let a in n)this[a]=n[a]}const O=function(){return _.exports}();export{O as default};
