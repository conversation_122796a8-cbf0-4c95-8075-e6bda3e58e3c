"[\n  {\n    curl: `curl --location 'http://in-testopenapi.tsign.cn/v2/identity/auth/web/indivAuthUrl' \\\n    --header 'Content-Type: application/json' \\\n    --header 'X-Tsign-Open-App-Id: **********' \\\n    --header 'X-Tsign-Open-Auth-Mode: simple' \\\n    --data '{   \n    \"authType\":\"PSN_FACEAUTH_BYURL\",\n      \"availableAuthTypes\":[\n          \"PSN_TELECOM_AUTHCODE\",\n          \"PSN_FACEAUTH_BYURL\",\n          \"INDIVIDUAL_BANKCARD_4_FACTOR\"\n      ],\n      \"contextInfo\":{\n          \"contextId\":\"993de698a82b43d9ba6a4fb26093629e\",\n          \"notifyUrl\":\"http://172.20.62.10:8080/testnotify/msgRecive\",\n          \"redirectUrl\":\"https://www.esign.cn/aboutUs/join_us.html\",\n          \"showResultPage\":true\n      },\n      \"indivInfo\":{\n          \"certNo\":\"******************\"\n      },\n      \"configParams\": {\n        \"indivEditableInfo\": [\n          \"idNo\",\"name\"\n        ]\n      }\n    }'`,\n    data: {\n      url: \"data.shortLink\",\n    },\n  },\n]"