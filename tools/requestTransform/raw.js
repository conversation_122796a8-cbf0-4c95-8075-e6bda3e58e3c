const raw =
[
  {
    curl: `curl --location --request POST 'http://in-testopenapi.tsign.cn/v2/identity/auth/web/indivAuthUrl' \
    --header 'Content-Type: application/json' \
    --header 'X-Tsign-Open-App-Id: **********' \
    --header 'X-Tsign-Open-Auth-Mode: simple' \
    --data '{   
    "authType":"PSN_FACEAUTH_BYURL",
      "availableAuthTypes":[
          "PSN_TELECOM_AUTHCODE",
          "PSN_FACEAUTH_BYURL",
          "INDIVIDUAL_BANKCARD_4_FACTOR"
      ],
      "contextInfo":{
          "contextId":"993de698a82b43d9ba6a4fb26093629e",
          "notifyUrl":"http://172.20.62.10:8080/testnotify/msgRecive",
          "redirectUrl":"https://www.esign.cn/aboutUs/join_us.html",
          "showResultPage":true
      },
      "indivInfo":{
          "certNo":"******************"
      },
      "configParams": {
        "indivEditableInfo": [
          "idNo","name"
        ]
      }
    }'`,
    data: {
      url: "data.shortLink",
    },
  },
]

module.exports = raw