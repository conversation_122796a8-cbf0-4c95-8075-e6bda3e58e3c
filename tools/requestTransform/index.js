const fs = require('fs')
const path = require('path')
const raw = require('./raw')
const { request } = require('../../core/actions/composite/request');

const genFile = (absolutePath, content) => {
  // 获取文件所在的目录
  const dir = path.dirname(absolutePath)

  try {
    // 如果目录不存在，则创建它（包括任何必要的父级目录）
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // 写入文件，如果文件已经存在则覆盖它
    fs.writeFileSync(absolutePath, content)
  } catch (err) {
    console.error(`发生错误：${err.message}`)
  }
}

genFile(path.resolve(__dirname, './transform.js'), JSON.stringify(raw))

const a = {}

request("", json, raw, a, true).then(() => { 
  console.log('----------------- 最终结果 -----------------')
  console.log(a)
  console.log('-------------------------------------------')
});