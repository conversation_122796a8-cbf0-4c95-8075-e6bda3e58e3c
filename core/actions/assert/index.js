const { ClientFunction } = require('testcafe');
const { setAssertCurrentElement } = require('../../currentElement/index')

const adjustValueByType = (value) => {
  if (isNaN(value)) {
    if (value === 'true' || value === 'false' || value === 'null') return value
    return `'${value}'`
  }
  return parseFloat(value);
}

const getUrl = ClientFunction(() => window.location.href);

const assertDomain = async (t, step) => {
  const url = await getUrl.with({ boundTestRun: t })();
  await t.expect(url).contains(step.value)
}

const assertStyle = async (t, selector, step, isFlow) => {
  const element = await setAssertCurrentElement(t, selector)
  const style = await element.style
  await t.expect(style[step.style])[step.isEql ? 'eql' : 'notEql'](adjustValueByType(step.value))
  if (step.isStartIFFlow) isFlow.value = true
}

const assertAttr = async (t, selector, step, isFlow) => {
  const element = await setAssertCurrentElement(t, selector)
  const attr = await element.getAttribute(step.attribute)
  await t.expect(attr)[step.isEql ? 'eql' : 'notEql'](adjustValueByType(step.value))
  if (step.isStartIFFlow) isFlow.value = true
}

const assert = async (t, selector, step, isFlow) => {
  const element = await setAssertCurrentElement(t, selector)
  const [assertType, assertValue] = step.value.split(':');
  const assertTypeArr = assertType.split('.');
  const assertNoun = assertTypeArr.pop();
  const assertVerb = assertTypeArr.pop();
  const assertAdv = assertTypeArr.pop();
  const isBoolType = ['checked', 'disabled'].includes(assertNoun);

  if (assertNoun === 'exists') {
    const isNot = assertVerb === 'not'
    await t.expect(element.exists)[isNot ? 'notOk' : 'ok']()
  } else if (assertNoun === 'enable') {
    const isNot = assertVerb === 'not'
    const attr = await element.getAttribute('disabled')
    await t.expect(!!attr)[isNot ? 'ok' : 'notOk']()
  } else {
    const isNot = assertAdv === 'not';
    let fn = '';
    if (assertVerb === 'have') {
      fn = isNot ? 'notEql' : 'eql';
    } else if (assertVerb === 'include') {
      fn = isNot ? 'notContains' : 'contains';
    }
    
    if (assertNoun === 'className') {
      await t.expect((await element())['classNames'])[fn](assertValue, 'fail')
    } else {
      await t.expect((await element())['value'] || (await element())['innerText'])[fn](assertValue, 'fail')
    }
  }
  if (step.isStartIFFlow) isFlow.value = true
}

exports.assertStyle = assertStyle
exports.assertAttr = assertAttr
exports.assert = assert
exports.assertDomain = assertDomain