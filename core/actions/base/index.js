const { genIgnoreError, genOptions, genSelector } = require('../utils.js');

const click = (selector, options, ignore) => {
  return ignore ?
    `await t.wait(1000); if (await getElement(${genSelector(selector)}).exists) { await t.click(await setCurrentElement(${genSelector(selector)}), ${genOptions(options)}) }` :
    `await t.click(await setCurrentElement(${genSelector(selector)}), ${genOptions(options)})`
}

const repeatedClick = (selector, times, options, ignore) => {
  return ignore ?
    `if (await setCurrentElement(${genSelector(selector)}).exists) {
      for (let i = 1; i <= ${times}; i++) {
        await t.click(await getCurrentElement(), ${genOptions(options)})
      }
    }` :
    `for (let i = 1; i <= ${times}; i++) {
      await t.click(await setCurrentElement(${genSelector(selector)}), ${genOptions(options)})
    }`
}

const rightClick = (selector, ignore) => {
  return genIgnoreError(`await t.rightClick(await setCurrentElement(${genSelector(selector)}))`, ignore)
}

const doubleClick = (selector, ignore) => {
  return genIgnoreError(`await t.doubleClick(await setCurrentElement(${genSelector(selector)}))`, ignore)
}

const keypress = (value, ignore) => {
  return genIgnoreError(`await t.pressKey('${value}')`, ignore)
}

const input = (value, ignore) => {
  return genIgnoreError(`${keypress('ctrl+a')}\n${keypress('delete')}\nawait t.typeText(await getCurrentElement(), \`${value}\`, { speed: 0.5, paste: true })`, ignore)
}

const choose = (value, ignore) => {
  const [text, target, className] = value.split('.');
  if (target) {
    return ignore ?
      `await t.wait(1000); if (await Selector('${target}').filterVisible().withText('${text}').exists) { await t.click(await setCurrentElement('${target}', true, \`${text}\`)) }` :
      `await t.click(await setCurrentElement('${target}', true, \`${text}\`))`
  }
  return ignore ?
    `await t.wait(1000); if (await Selector('.${className}').filterVisible().withText('${text}').exists) { await t.click(await setCurrentElement('.${className}', true, \`${text}\`)) }` :
    `await t.click(await setCurrentElement('.${className}', true, \`${text}\`))`
}

const hover = (selector, ignore) => {
  return genIgnoreError(`await t.hover(await setCurrentElement(${genSelector(selector)}))`, ignore)
}

const wait = (value) => {
  return `await t.wait(${value})`
}

const debug = () => {
  return `await t.debug()`
}

const refresh = () => {
  return `await t.eval(() => location.reload(true));`
}

const resize = (value) => {
  let temp = value
  if (typeof value === 'string') {
    temp = JSON.parse(value)
  }
  const { width, height } = temp
  return `await t.resizeWindow(${width}, ${height})`
}

const resizeToFitDevice = () => {
  return `await t.resizeWindowToFitDevice('iPhone XR', { portraitOrientation: true })`
}

const navigateTo = (value) => {
  return `await t.navigateTo(\`${value}\`)`
}

const drag = (step) => {

}

exports.click = click
exports.repeatedClick = repeatedClick
exports.rightClick = rightClick
exports.doubleClick = doubleClick
exports.input = input
exports.keypress = keypress
exports.choose = choose
exports.hover = hover
exports.wait = wait
exports.debug = debug
exports.refresh = refresh
exports.resize = resize
exports.resizeToFitDevice = resizeToFitDevice
exports.navigateTo = navigateTo
