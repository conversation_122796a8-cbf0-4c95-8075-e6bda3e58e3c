const pagesMap = new Map()

const setWindowName = async (t, name, width, height) => {
  const pageObj = await t.getCurrentWindow()
  if (!pagesMap.has(name)) {
    await t.resizeWindow(width, height)
    pagesMap.set(name, pageObj)
  }
}

const switchWindow = async (t, name) => {
  if (pagesMap.has(name)) {
    await t.switchToWindow(pagesMap.get(name))
  }
}

const closeWindow = async (t, name) => {
  if (pagesMap.has(name)) {
    await t.closeWindow(pagesMap.get(name))
    pagesMap.delete(name)
  }
}

const clearWindowsMap = () => {
  pagesMap.clear()
}

exports.setWindowName = setWindowName
exports.switchWindow = switchWindow
exports.closeWindow = closeWindow
exports.clearWindowsMap = clearWindowsMap