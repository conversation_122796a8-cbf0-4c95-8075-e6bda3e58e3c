const axios = require('axios');
const curlToAxiosObject = require('../../utils/curlToAxiosObject.js')

const deylay = (s) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, Number(s) * 1000)
  })
}

const request = async (t, log, requestQueue, requestVariable, showLog = true) => {
  let requestPreValue = { ...requestVariable }
  for (let i = 0; i < requestQueue.length; i++) {
    const request = requestQueue[i];

    let curl = request.curl;
    const responseData = request.data || {};

    if (requestPreValue) {
      for (let [k, v] of Object.entries(requestPreValue)) {
        curl = curl.replaceAll(`{${k}}`, v);
      }
    }

    try {
      const axiosObj = curlToAxiosObject(curl)
      if (showLog) {
        console.log('----------------- 请求参数 -----------------')
        console.log(axiosObj)
      }
      const { data } = await axios(axiosObj)
      if (showLog) {
        console.log('----------------- 请求结果 -----------------')
        console.log(data)
      }
      log.info && log.info(`请求结果：${ JSON.stringify(data) }`)
      Object.entries(responseData).forEach(([paramName, paramPath]) => {
        let temp = data
        // 新增路径解析逻辑，同时支持点语法和数组语法
        paramPath.match(/(\w+)|\[(\d+)\]/g).forEach(part => {
          if (part.startsWith('[')) { // 数组索引访问
            temp = temp[part.replace(/\D/g, '')]
          } else { // 对象属性访问
            temp = temp[part]
          }
        })
        requestPreValue[paramName] = typeof temp === 'object' ? JSON.stringify(temp) : temp
        requestVariable[paramName] = requestPreValue[paramName]
      })
    } catch (e) {
      console.log('request error', e)
      try {
        log.error && log.error(`请求失败: ${JSON.stringify({ url: axiosObj.url, method: axiosObj.method, error: e })}`)
      } catch (e) {}
    }
  }

  return requestPreValue
}

const requestAndRedirect = async (t, log, requestQueue, requestVariable) => {
  const result = await request(t, log, requestQueue, requestVariable)
  const url = result?.url || null
  if (!url) return
  log.info && log.info(`请求重定向到: ${url}`)
  await t.navigateTo(url)
}

exports.request = request
exports.requestAndRedirect = requestAndRedirect