const { Selector } = require('testcafe')
const { setCurrentElement } = require('../../currentElement/index')
const fs = require('fs')
const path = require('path')
// const { click, input, choose, wait } = require('../base/index')

let settingConfig = null



const choose = async (t, value) => {
  const [text, target, className] = value.split('.');
  if (target) {
    await t.click(await setCurrentElement(target, true, text))
  } else {
    await t.click(await setCurrentElement(`.${className}`, true, text))
  }
}

const getSettingConfig = async () => {
  try {
    const data = await fs.promises.readFile(path.resolve(process.cwd(), './config/setting/epaas.json'), 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading or parsing the JSON file:', error);
    throw error;
  }
}

const setting = async (t, { name, value }) => {
  if (!settingConfig) {
    settingConfig = await getSettingConfig()
  }
  const { props } = settingConfig

  const propConfig = props[name] || {}
  const parentSelector = propConfig.parentSelector || settingConfig.parentSelector || 'body'
  const textSelector = propConfig.textSelector || settingConfig.textSelector || 'span'
  const targetSelector = propConfig.targetSelector || settingConfig.targetSelectors[propConfig.type] || 'span'
  const chooseSelector = propConfig.chooseSelector || 'li'

  // 先找到对应元素
  let element = Selector(textSelector).withText(name).parent(parentSelector).find(targetSelector)
  const booleanValue = value === '真'

  if (propConfig.type === 'input') {
    await t.click(element)
    await t.pressKey('ctrl+a')
    await t.pressKey('delete')
    await t.typeText(element, value)
  } else if (propConfig.type === 'select') {
    await t.click(element)
    await t.wait(1000)
    await choose(t, `${value}.${chooseSelector}`)
  } else if (propConfig.type === 'textarea') {
    await t.click(element)
    await t.pressKey('ctrl+a')
    await t.pressKey('delete')
    await t.typeText(element, value)
  } else if (propConfig.type === 'checkbox') {
    const isChecked = (await element.getAttribute('class')).includes('is-checked')
    if (isChecked !== booleanValue) {
      await t.click(element)
    }
  } else if (propConfig.type === 'switch') {
    const isChecked = (await element.getAttribute('class')).includes('is-checked')
    if (isChecked !== booleanValue) {
      await t.click(t, element)
    }
  } else if (propConfig.type === 'injectSelect') {
    await t.click(`[data-esign-inject-name="${name}"]`)
    await t.wait(1000)
    await choose(t, `${value}.${chooseSelector}`)
  } else if (propConfig.type === 'injectButton') {
    element = Selector(`[data-esign-inject-name="${name}"]`)
    const isChecked = (await element.find('div').getAttribute('class')).includes('isSelected')
    if (isChecked !== booleanValue) {
      await t.click(element.find('div'))
    }
  } else if (propConfig.type === 'injectAlignButton') {
    element = Selector(`[data-esign-inject-name="${name}"]`)
    if (value === '左对齐') {
      await t.click(element.child(0))
    } else if (value === '右对齐') {
      await t.click(element.child(2))
    } else if (value === '居中') {
      await t.click(element.child(1))
    }
  } else if (propConfig.type === 'injectVerticalAlignButton') {
    element = Selector(`[data-esign-inject-name="${name}"]`)
    if (value === '顶部对齐') {
      await t.click(element.child(0))
    } else if (value === '底部对齐') {
      await t.click(element.child(2))
    } else if (value === '居中') {
      await t.click(element.child(1))
    }
  }
}

const quickSetting = async (t, { name, value }) => {
  if (!settingConfig) {
    settingConfig = await getSettingConfig()
  }
  const { quickProps } = settingConfig

  const propConfig = quickProps[name] || {}
  const targetSelector = propConfig.targetSelector || settingConfig.targetSelectors[propConfig.type] || 'span'
  const chooseSelector = propConfig.chooseSelector || 'li'

  const element = Selector(targetSelector)
  const booleanValue = value === '真'

  if (propConfig.type === 'input') {
    await t.click(element)
    await t.pressKey('ctrl+a')
    await t.pressKey('delete')
    await t.typeText(element, value)
  } else if (propConfig.type === 'select') {
    await t.click(element)
    await t.wait(1000)
    await choose(t, `${value}.${chooseSelector}`)
  } else if (propConfig.type === 'checkbox') {
    const isChecked = (await element.getAttribute('class')).includes('is-checked')
    if (isChecked !== booleanValue) {
      await t.click(element)
    }
  }
}

exports.quickSetting = quickSetting

exports.setting = setting
