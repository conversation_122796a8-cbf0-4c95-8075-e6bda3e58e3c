const { ClientFunction, Selector } = require('testcafe');

const getIframeUrl = ClientFunction(() => document.querySelector('iframe').src);
const getOrigin = ClientFunction(() => window.location.origin);
const reloadIframe = ClientFunction(() => {
  window.location.reload()
})

const openWillAuthWindow = async (t) => {
  let iframeUrl = await getIframeUrl.with({ boundTestRun: t })();
  if (!iframeUrl.startsWith('http')) {
    const origin = await getOrigin();
    iframeUrl = origin + iframeUrl;
  }
  const willAuthPage = await t.openWindow(iframeUrl);

  await t.wait(1000);

  await reloadIframe.with({ boundTestRun: t })();

  await t.wait(1000);

  return {
    iframeUrl,
    closeWillAuthWindow: async () => {
      await t.closeWindow(willAuthPage);
    }
  }
}

const changWillAuthType = async (t, type) => {
  const authTitle = (await Selector('[class*="authTitle"]').with({ boundTestRun: t }));
  let authTitleExists = await authTitle.exists;
  if (authTitleExists) {
    const isTargetWillAuthType = (await authTitle.innerText).includes(type);
  
    if (!isTargetWillAuthType) {
      // 当前不是对应认证类型就判断是否有对应认证类型按钮
      const isTargetWillAuthButton = await Selector('[class*="bottomAuthSwitch"]').withText(type).with({ boundTestRun: t }).exists;
      // 没有对应认证类型按钮 继续在更多里查找
      if (isTargetWillAuthButton) {
        await t.click(Selector('[class*="authTypeLabel"]').withText(type).with({ boundTestRun: t }))
      } else {
        await t
          .click('.es-dropdown-selfdefine')
          .click(Selector('.es-dropdown-menu__item').withText(type).with({ boundTestRun: t }))
      }
    }
  } else {
    await t.click(`[data-esign-inject-name="单选${type}"]`)
  }
  await t.wait(1000)
}

const willAuth = async (t) => {
  await t.wait(5000);
  const { iframeUrl, closeWillAuthWindow } = await openWillAuthWindow(t)
  if (iframeUrl.includes('/ec-manage-web/authenticationPc')) {
    await t.wait(20000);
    // 天印意愿
    await t
      .click(Selector('.authenTypeItem').withText('短信认证'))
      .wait(500)
      .click(Selector('.verCodeBut'))
      .wait(1000)
      .typeText(Selector('.es-input__inner'), '123456', { replace: true })
      .wait(500)
      .click(Selector('.es-form-item__content .submit').withText('确认提交'))
      .wait(2000);
  } else {
    await t.wait(5000);
    // saas意愿
    await changWillAuthType(t, '短信认证')

    await t.click(Selector('button').withText('获取验证码'))
    await t.wait(1000)
    await t.typeText(Selector('[data-esign-inject-name="表单项验证码"]'), '123456', { replace: true })
    if (await Selector('.es-checkbox__inner').with({ boundTestRun: t }).exists) await t.click(Selector('.es-checkbox__inner', { timeout: 500 }))
    await t.click(Selector('[data-esign-inject-name*="按钮确"]'))
    await t.wait(2000);
  }
  await closeWillAuthWindow()
  await t.wait(2000);
}

const willAuthPassword = async (t, password) => {
  await t.wait(10000);
  const { closeWillAuthWindow } = await openWillAuthWindow(t)
  // saas意愿
  await changWillAuthType(t, '密码认证')

  await t.typeText(Selector('[type="password"]'), password, { replace: true })
  if (await Selector('.es-checkbox__inner').with({ boundTestRun: t }).exists) await t.click(Selector('.es-checkbox__inner', { timeout: 500 }))
  await t.click(Selector('[data-esign-inject-name*="按钮确"]'))
  await t.wait(2000)
  await closeWillAuthWindow()
  await t.wait(2000);
}

exports.willAuth = willAuth
exports.willAuthPassword = willAuthPassword