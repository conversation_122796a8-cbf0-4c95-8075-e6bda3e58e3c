const { ClientFunction } = require('testcafe');

const setWindowName = ClientFunction(({ key, value }) => {
  let obj = {}
  try {
    obj = JSON.parse(window.name) || {}
    if (typeof obj !== 'object') obj = {}
  } catch (error) {}
  obj[key] = value
  window.name = JSON.stringify(obj)
});

const switchCustomUserAgent = async (t, customUserAgent = '') => {
  await t.wait(2000)
  await t.resizeWindowToFitDevice('iPhone XR', {
    portraitOrientation: true,
  })

  const ua = customUserAgent || 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'

  await setWindowName.with({ boundTestRun: t })({ key: '__ui_test_userAgent__', value: ua });

  await t.wait(1000);
}

exports.switchCustomUserAgent = switchCustomUserAgent