const { datepicker } = require('./datepicker')
const { upload, getUploadFilePath } = require('./upload')
const { setting, quickSetting } = require('./setting')
const { compare } = require('./compare')
const { setWindowName, switchWindow, closeWindow, clearWindowsMap } = require('./window')
const { willAuth, willAuthPassword } = require('./willAuth')
const { request, requestAndRedirect } = require('./request')
const { switchCustomUserAgent } = require('./userAgent')
const { captureURL } = require('./variable')

const compositeActions = {
  datepicker,
  upload,
  getUploadFilePath,
  setting,
  compare,
  quickSetting,
  setWindowName,
  switchWindow,
  closeWindow,
  clearWindowsMap,
  willAuth,
  willAuthPassword,
  request,
  requestAndRedirect,
  switchCustomUserAgent,
  captureURL,
}

exports.composite = compositeActions