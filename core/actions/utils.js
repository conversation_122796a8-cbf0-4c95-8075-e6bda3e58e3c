const genIgnoreError = (code, isIgnore, reportCode = '') => {
  if (!isIgnore) return code
  return `try {\n  ${code}\n} catch(e) { ${reportCode} }`
}

const genOptions = (code) => {
  if (!code) return '{}'
  try {
    return code
  } catch (e) {}
  return '{}'
}

const genSelector = (selector) => {
  if (typeof selector === 'object') {
    return JSON.stringify(selector)
  }
  return selector
}

exports.genIgnoreError = genIgnoreError
exports.genSelector = genSelector
exports.genOptions = genOptions