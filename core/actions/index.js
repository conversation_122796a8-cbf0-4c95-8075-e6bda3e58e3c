const { click, repeatedClick, rightClick, doubleClick, input, keypress, choose, hover, wait, debug, refresh, resize, resizeToFitDevice, navigateTo } = require('./base/index')
const { assert, assertStyle, assertAttr, assertDomain } = require('./assert/index')
const { composite } = require('./composite/index')
const { genIgnoreError } = require('./utils')

const actions = {
  base: {
    click,
    repeatedClick,
    rightClick,
    doubleClick,
    input,
    keypress,
    choose,
    hover,
    wait,
    debug,
    refresh,
    resize,
    resizeToFitDevice,
    navigateTo,
  },
  assert: {
    base: assert,
    style: assertStyle,
    attr: assertAttr,
    domain: assertDomain,
  },
  composite,
  utils: {
    genIgnoreError
  },
}

module.exports = actions