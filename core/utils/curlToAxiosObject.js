const parser = require('yargs-parser')
const FormData = require('form-data');

const convertToJson = (curl_request) => {
  const argvs = parser(curl_request);

  const json = {
    headers: {},
    method: 'GET',
  };

  const isJson = (str) => {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  };

  const removeQuotes = (str) => str.replace(/['"]+/g, '');

  const stringIsUrl = (url) => {
    return /^(ftp|http|https):\/\/[^ "]+$/.test(url);
  };

  const parseField = (string) => {
    return string.split(/:(.+)/).map(text => text.trim());
  };

  const parseHeader = (headers) => {
    let parsedHeader = {};
    if (!Array.isArray(headers)) {
      headers = [headers]
    }
    headers.forEach((item, index) => {
      const field = parseField(item);
      parsedHeader[field[0]] = field[1];
    });

    return parsedHeader;
  };

  const parseData = (data) => {
    let jsonObj = {};
    json.headers['Content-Type'] = 'application/json';

    if (Array.isArray(data)) {
      if (isJson(data[0])) {
        data.forEach((item) => {
          const parsedItem = JSON.parse(item);
          jsonObj = {
            ...jsonObj,
            ...parsedItem,
          };
        });

        return jsonObj;
      }

      if (data[0].includes('=')) {
        return parseDataUrlEncode(data);
      }
    } else {
      if (isJson(data)) {
        return JSON.parse(data);
      }
      if (data.includes('=')) {
        return parseDataUrlEncode(data);
      }
      return data;
    }
  };

  const parseDataUrlEncode = (data) => {
    let jsonUrlEncoded = '';
    json.headers['Content-Type'] = 'application/x-www-form-urlencoded';

    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        if (index === 0) {
          jsonUrlEncoded = encodeURI(item);
        } else {
          jsonUrlEncoded = jsonUrlEncoded + '&' + encodeURI(item);
        }
      });
      return jsonUrlEncoded;
    } else {
      return data;
    }
  };

  const parseFormData = (data) => {
    json.headers['Content-Type'] = 'multipart/form-data';
    const formData = new FormData();
    if (Array.isArray(data)) {
      data.forEach((item) => {
        const [key, value] = item.split('=');
        formData.append(key, value.replace(/['"]+/g, ''));
      });
    } else {
      const [key, value] = data.split('=');
      formData.append(key, value.replace(/['"]+/g, ''));
    }
    return formData;
  };

  for (const argv in argvs) {
    switch (argv) {
      case '_':
        {
          const _ = argvs[argv];
          _.forEach((item) => {
            item = removeQuotes(item);

            if (stringIsUrl(item)) {
              json.url = item;
            }
          });
        }
        break;

      case 'X':
      case 'request':
        json.method = argvs[argv];
        break;

      case 'H':
      case 'header':
        {
          const parsedHeader = parseHeader(argvs[argv]);
          json.headers = {
            ...json.headers,
            ...parsedHeader,
          };
        }
        break;

      case 'u':
      case 'user':
        json.headers['Authorization'] = argvs[argv];
        break;

      case 'A':
      case 'user-agent':
        json.headers['user-agent'] = argvs[argv];
        break;

      case 'I':
      case 'head':
        json.method = 'HEAD';
        break;

      case 'b':
      case 'cookie':
        json.headers['Set-Cookie'] = argvs[argv];
        break;

      case 'd':
      case 'data':
      case 'data-raw':
      case 'data-ascii':
        json.data = parseData(argvs[argv]);
        break;

      case 'data-urlencode':
        json.data = parseDataUrlEncode(argvs[argv]);
        break;

      case 'F':
      case 'form':
        json.data = parseFormData(argvs[argv]);
        break;

      case 'compressed':
        if (!json.headers['Accept-Encoding']) {
          json.headers['Accept-Encoding'] = argvs[argv] || 'deflate, gzip';
        }
        break;

      default:
        break;
    }
  }

  return json;
};

module.exports = exports.default = convertToJson;
