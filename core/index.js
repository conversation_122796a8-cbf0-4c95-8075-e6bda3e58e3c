const fs = require('fs')
const path = require('path')
const createTestCafe = require('testcafe')
const { getXmindInfo } = require('./xmindParser/index.js')
const { genCases } = require('./genCases/index.js')
const { genFile } = require('./genFile/index.js')
const { genInjectFile } = require('./genInjectFile/index.js')

// 路径常量
const CASE_DIR = path.resolve(process.cwd(), './core/test/case')
const INJECT_DIR = path.resolve(process.cwd(), './core/test/inject')
const XMIND_DIR = path.resolve(process.cwd(), './core/xmind')
let usedPorts = new Set();
let reportData = {};
let curRunner = new Set();
const createTmpDir = () => {
  const tempDirPath = path.resolve(process.cwd(), './tmp')

  // 同步创建目录
  if (!fs.existsSync(tempDirPath)) {
    fs.mkdirSync(tempDirPath, { recursive: true })
    console.log('---------- 创建临时目录 ----------')
  } else {
    console.log('---------- 临时目录已存在 ----------')
  }

  return () => {
    console.log('---------- 删除临时目录 ----------')
    fs.rmSync(tempDirPath, { recursive: true, force: true }) // 使用现代API
  }
}
const getRandomPortPair = () => {
  let port1, port2;
  do {
    port1 = 1337 + Math.floor(Math.random() * 1000);
    port2 = port1 + 1;
  } while (usedPorts.has(port1) || usedPorts.has(port2));

  usedPorts.add(port1);
  usedPorts.add(port2);
  return port1; // 返回起始端口
};



// 生成随机字符串
const generateRandomString = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 创建自定义报告器
const createCustomReporter = () => ({
  async reportTaskStart(startTime, userAgents, testCount) {
    reportData = {
      caseList: [],
      userAgent: userAgents[0],
      startTime: startTime,
      totalCases: testCount,
    };
    console.log('开始测试', startTime, userAgents, testCount);
    return reportData;
  },
  async reportFixtureStart(name, path, meta) {
    console.log('开始用例测试', name, path, meta);
  },
  async reportTestDone(name, testRunInfo, meta) {
    console.log('用例测试完成', name, testRunInfo, meta);
    reportData.runResult = testRunInfo.errs.length === 0?'success' : 'fail';
    reportData.errorInfo = testRunInfo.errs?JSON.stringify(testRunInfo.errs):'';
    reportData.duration = testRunInfo.durationMs;
    reportData.caseList = [{
      name,
      status: testRunInfo.errs.length === 0 ? 'success' : 'fail',
      duration: testRunInfo.durationMs
    }];
  },
  async reportTaskDone(endTime, passed, warnings, result) {
    console.log('测试完成', endTime, passed, warnings, result);
    reportData.isSuccess = result.failedCount;
  }
});

// 执行测试
const executeTests = async (runner, injectFilePath, reportHTMLPath,reportJSONPath, customReporter, caseFilePath, browser, fileTag) => {
  return await runner
    .clientScripts(injectFilePath)
    .reporter([{ name: 'custom', output: reportHTMLPath },{ name: 'json', output: reportJSONPath }, customReporter])
    .src([caseFilePath])
    .browsers([browser])
    .screenshots({
      path: 'screenshots',
      takeOnFails: true,
      thumbnails: false
    })
    .video('reports/videos', {
      failedOnly: true,
      singleFile: true,
      pathPattern: `/${fileTag}.mp4`,
    })
    .run({
      skipJsErrors: true,
      skipUncaughtErrors: true,
      testExecutionTimeout: 600000,
      runExecutionTimeout: 1200000,
      pageRequestTimeout: 50000,
      selectorTimeout: 20000,
      assertionTimeout: 20000,
      developmentMode: true,
      retryTestPages: true,
      disableNativeAutomation: true,
      // "quarantineMode": {
      //   "successThreshold": 1,
      //   "attemptLimit": 
      // }
    });
};

const stop = async (name) => {
  const entry = Array.from(curRunner).find(entry => entry.caseName === name);
  
  if (entry) {
    try {
      console.log(`[${name}] 正在终止测试任务...`);

      // 添加端口释放验证
      if (usedPorts.has(entry.currentPort)) {
        usedPorts.delete(entry.currentPort);
        usedPorts.delete(entry.currentPort + 1);
        console.log(`[${name}] 已释放端口: ${entry.currentPort}, ${entry.currentPort + 1}`);
      }

      // 先关闭TestCafe实例再停止runner
      if (entry.testcafe) {
        await entry.testcafe.close();
        console.log(`[${name}] TestCafe实例已关闭`);
      }

      // 增加延迟确保资源释放
      await new Promise(resolve => setTimeout(resolve, 1000));

      curRunner.delete(entry);
      console.log(`[${name}] 测试任务已终止`);

      throw Object.assign(new Error(`[${name}] 测试任务已终止`), { __TERMINATED: true });
    } catch (error) {
      
      console.error(`[${name}] 终止任务失败:`, error);
      // 强制清理残留资源
      if (entry?.testcafe) {
        await entry.testcafe.close();
      }
      curRunner.delete(entry);
      // 确保最终释放端口
      usedPorts.delete(entry.currentPort);
      usedPorts.delete(entry.currentPort + 1);
      throw Object.assign(new Error(`[${name}] 终止任务失败`), { __TERMINATED: true });
    }
  }
}

const startWithJson = async (data, func) => {
  const { id, caseName, browser, groupName, jsonData, fileTag } = data;
  console.log(`---------- 开始读取[${id},${groupName}${caseName}用例，运行环境${browser}浏览器] ----------`);

  const removeTmpDir = createTmpDir();

  try {
    // 获取 XMind 信息
    let xmindInfo = {}
    if (jsonData) {
      xmindInfo = jsonData;
    } else {
      const xmindPath = path.join(XMIND_DIR, `${caseName || 't0'}.xmind`);
      xmindInfo = await getXmindInfo(xmindPath);
    }

    // 生成用例文件
    const caseInfoPath = path.join(CASE_DIR, `${'caseId_' + id}.json`);
    await genFile(caseInfoPath, JSON.stringify(xmindInfo, null, 2));

    // 生成测试用例
    const caseStr = genCases(xmindInfo,fileTag);
    const caseFilePath = path.join(CASE_DIR, `${'caseId_' + id}.js`);
    await genFile(caseFilePath, caseStr);

    // 生成注入文件
    const injectFilePath = path.join(INJECT_DIR, `${'caseId_' + id}.inject.js`);
    await genInjectFile(xmindInfo, injectFilePath);

    const currentPort = getRandomPortPair();

    const createOptions = {
      hostname: 'localhost',
      port1: currentPort,
      port2: currentPort + 1,
      retryTestPages: true,
      cache: false,
    }

    if (browser.includes('--allow-insecure-localhost')) {
      createOptions.ssl = {
        key: fs.readFileSync(path.join(__dirname, './ssl/testingdomain.key')),
        cert: fs.readFileSync(path.join(__dirname, './ssl/testingdomain.crt')),
        pfx: fs.readFileSync(path.join(__dirname, './ssl/testingdomain.pfx')),
        rejectUnauthorized: true,
        passphrase: '123456'
      }
    }

    // 创建 TestCafe 实例
    const testcafe = await createTestCafe(createOptions);
    const entry = {
      id,
      caseName,
      runner: testcafe.createRunner(),
      testcafe,  // 新增testcafe实例引用
      currentPort // 保存当前使用的端口
    };
    curRunner.add(entry);

    const reportHTMLPath = path.resolve(process.cwd(), 'reports/html', `report-${caseName}-${fileTag}.html`);
    const reporJSONPath = path.resolve(process.cwd(), 'reports/json', `${fileTag}.json`);


    console.log('运行端口', currentPort, currentPort + 1);

    await executeTests(entry.runner, injectFilePath, reportHTMLPath,reporJSONPath, createCustomReporter, caseFilePath, browser, fileTag);
    await testcafe.close();
    const reportData1 = {
      ...reportData,
      id,
      browser,
      caseName,
      groupName,
      fileTag,
      port: currentPort
    };
    console.log('---------- 测试结束 ----------');
    // await func(reportData1); //发送测试结果
    return reportData1;
  } catch (error) {
    console.error('---------- 流程执行异常 ----------',error);
    // await func(error);
    throw error;
  } finally {
    removeTmpDir();
  }
};
module.exports = { stop, startWithJson }


