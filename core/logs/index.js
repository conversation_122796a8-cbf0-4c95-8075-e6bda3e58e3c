const fs = require("fs");
const path = require("path");
const logPath = path.resolve(process.cwd(), "./reports");

class Log {
  constructor(name) {
      this.name = name;
  }
  write(content, level = 'info') {
    const date = new Date();
    // const dateStr = date.toISOString().slice(0, 10).replace(/-/g, "");
    const logDir = path.resolve(logPath, "logs");
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
    const logFile = path.resolve(logDir, `${this.name}.log`);
    const timestamp = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
    const logContent = `[${timestamp}][${level.toUpperCase()}] ${content}\n`;
    fs.appendFileSync(logFile, logContent);
  }

  info(content) {
    this.write(content, 'info');
  }
  error(content) {
    this.write(content, 'error');
  }
  warn(content) {
    this.write(content, 'warn');
  }
  running(content) {
    this.write(content, 'running');
  }
}

module.exports = { Log };
