const fs = require('fs')
const path = require('path')

const genFile = (absolutePath, content) => {
  // 获取文件所在的目录
  const dir = path.dirname(absolutePath)

  try {
    // 如果目录不存在，则创建它（包括任何必要的父级目录）
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // 写入文件，如果文件已经存在则覆盖它
    fs.writeFileSync(absolutePath, content)
  } catch (err) {
    console.error(`发生错误：${err.message}`)
  }
}

exports.genFile = genFile