const { Selector } = require('testcafe')

let currentElement = null
let currentAssertElement = null

const getElement = (selectorStrOrObj, filterVisible, textStr) => {
  const selector = typeof selectorStrOrObj === 'object' ? selectorStrOrObj.tagName || `.${selectorStrOrObj.className}` : selectorStrOrObj
  const text = typeof selectorStrOrObj === 'object' ? selectorStrOrObj.text || '' : textStr

  let element = null
  if (typeof selector === 'string') {
    element = Selector(selector)
  
    if (filterVisible) element = element.filterVisible()
  
    if (text) element = element.withText(text)
  } else if (typeof selector === 'object') {
    element = Selector(selector.tagName || selector.className)

    if (filterVisible) element = element.filterVisible()

    if (text) element = element.withText(text)
  } else {
    element = selector
  }

  return element
}

const setCurrentElement = async (selectorStrOrObj, filterVisible = true, text = '') => {
  const el = getElement(selectorStrOrObj, filterVisible, text)
  currentElement = await el
  return currentElement
}

const getCurrentElement = () => currentElement

const setAssertCurrentElement = async (t, selectorStrOrObj) => {
  const isSelectorObj = typeof selectorStrOrObj === 'object'
  if (isSelectorObj) {
    const selector = selectorStrOrObj.tagName || `.${selectorStrOrObj.className}`
    const text = selectorStrOrObj.text || ''
    currentAssertElement = Selector(selector).filterVisible().withText(text).with({ boundTestRun: t })
    return currentAssertElement
  }
  currentAssertElement = Selector(selectorStrOrObj).with({ boundTestRun: t })
  return currentAssertElement 
}

const getAssertCurrentElement = () => currentAssertElement

exports.getElement = getElement
exports.setCurrentElement = setCurrentElement
exports.getCurrentElement = getCurrentElement
exports.setAssertCurrentElement = setAssertCurrentElement
exports.getAssertCurrentElement = getAssertCurrentElement