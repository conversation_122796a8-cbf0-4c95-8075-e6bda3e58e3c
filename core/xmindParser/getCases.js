const path = require('path')
const { getText, getNode, getChildrenNode, getChildNodeTextByIndex } = require('./utils')
const { getAssertInfo } = require('./getAssertInfo')
const { getVariableConfig, getPreRequestConfig } = require('./getGlobalConfig')
const { getTime } = require('./getTime')
const { transformNodes } = require('./transformNodes')

const replacePath = (node, globalSelector, globalSelectorKeys) => {
  globalSelectorKeys.forEach(key => {
    const keyword = '${' + key + '}'
    const text = node.text
    if (getText(node).includes(keyword)) {
      node.text = text.replace(keyword, globalSelector[key])
    }
  })
}

const checkIsTitle = text => text.toLocaleUpperCase().startsWith('TL-')
const checkIsAssert = text => text.toLocaleUpperCase().startsWith('ER-')
// const checkIsFlowFile = text => text.toLocaleUpperCase().startsWith('F-')
// const checkIsFlow = text => text.toLocaleUpperCase().startsWith('FLOW:')
const checkIsFile = text => text.toLocaleUpperCase().startsWith('FILE:')
const checkIsOptions = text => text.toLocaleUpperCase().startsWith('OPTIONS:')
const checkIsIgnore = text => text.toLocaleUpperCase().startsWith('IGNORE:')
const checkIsConfig = text => text.toLocaleUpperCase() === 'CONFIG'
const checkIsSkipNode = text => text.startsWith('#')
const checkIsSkipNodeTree = text => text.startsWith('##')
const checkIsRequestNode = text => text.startsWith('请求接口')

const fileRegex = /^使用(.+)上传文件$/
const repeatedClickRegex = /^重复点击(.+)元素(\d+)次$/
const dragAbsoluteRegex = /^拖拽(.+)至(-?\d+\.\d+)[,，](-?\d+\.?\d*)$/
const dragRelativeRegex = /^拖拽(.+)移动(-?\d+\.?\d*)[,，](-?\d+\.?\d*)$/
const settingRegex = /^设置(.+)为(.+)$/
const quickSettingRegex = /^快速设置(.+)为(.+)$/
const compareRegex = /^比对节点(.+)与(.+)$/
const settingOptionRegex = /^第(\d+)个选项$/
const windowRegex = /^(当前窗口为|切换窗口|关闭窗口)(.*)/
const captureURLRegex = /^使用正则(.+)提取URL中的数据作为变量(.+)$/

const genWindowStep = (text, result) => {
  const windowMatches = text.match(windowRegex)
  if (windowMatches) {
    result.value = windowMatches[2]
  }
}

const parseSubFlow = async (node, filePath, parser) => {
  const flowFileRelativePath = getText(node).substring(5)
  const flowFileAbsolutePath = path.resolve(path.dirname(filePath), flowFileRelativePath)
  const subFlowInfo = await parser.xmindToJSON(flowFileAbsolutePath)
  const { root } = subFlowInfo
  transformNodes(root)
  node.children = [root]
}

const traverseNode = async ({ node, globalSelector, globalSelectorKeys, results, parentNode, filePath, parser }) => {
  let result = {}
  const desc = getText(node)

  // 处理当前节点
  replacePath(node, globalSelector, globalSelectorKeys)

  let text = getText(node)

  // 判断标题节点
  const isTitle = checkIsTitle(text)

  // 是否断言
  const isAssert = checkIsAssert(text)

  // 是否流程
  // const isFlow = checkIsFlow(text)

  // 是否流程文件
  // const isFlowFlie = checkIsFlowFile(text)

  // 是否文件
  const isFile = checkIsFile(text)

  // 是否动作配置
  const isOptions = checkIsOptions(text)

  // 是否配置
  const isConfig = checkIsConfig(text)

  // 是否忽略错误
  const isIgnore = checkIsIgnore(text)

  // 是否跳过节点
  const isSkipNode = checkIsSkipNode(text)

  // 是否跳过节点树
  const isSkipNodeTree = checkIsSkipNodeTree(text)

  // 是否请求节点
  const isRequestNode = checkIsRequestNode(text)

  if (isSkipNode) {
    // 如果跳过节点则什么都不做 跳过节点树必定为跳过节点
  } else if (isTitle) {
    result.title = text.substring(3)
    result.steps = []
    results = result.steps
  } else if (isAssert) {
    const tempText = text.substring(3)
    const res = getAssertInfo(tempText)
    result = {
      text: tempText,
      type: 'assert',
      ...res
    }
    results.push(result)
  } else if (isConfig) {
    const variable = getVariableConfig(getNode(node, 'variable'))
    if (variable) parentNode.variable = variable
    const url = await getPreRequestConfig(getNode(node, 'preRequest'))
    if (url) parentNode.url = url
    return
  // } else if (isFlow) {
  //   await parseSubFlow(node, filePath, parser)
  // } else if (isFlowFlie) {
    // 暂时什么都不干
  } else if (isFile) {
    if (parentNode.type === 'upload') {
      parentNode.filePath = text.substring(5)
    } else {
      // 子流程
    }
  } else if (isOptions) {
    parentNode.options = text.substring(8)
  } else {
    result.text = text
    if (isIgnore) {
      result.ignore = isIgnore
      text = text.substring(7)
    }
    if (text.startsWith('点击')) {
      result.type = 'click'
      result.element = text.substring(2)
    } else if (text.startsWith('重复点击')) {
      result.type = 'repeatedClick'
      const repeatedClickMatches = text.match(repeatedClickRegex)
      if (repeatedClickMatches) {
        result.element = repeatedClickMatches[1]
        result.value = Number(repeatedClickMatches[2])
      }
    } else if (text.startsWith('右键')) {
      result.type = 'rightClick'
      result.element = text.substring(2)
    } else if (text.startsWith('双击')) {
      result.type = 'doubleClick'
      result.element = text.substring(2)
    } else if (text.startsWith('输入')) {
      result.type = 'input'
      result.value = text.substring(2)
    } else if (text.startsWith('悬停')) {
      result.type = 'hover'
      result.element = text.substring(2)
      result.value = text.substring(2)
    } else if (text.startsWith('选择时间')) {
      result.type = 'datepicker'
      result.value = getTime(text.substring(4))
    } else if (text.startsWith('选择')) {
      result.type = 'choose'
      result.value = text.substring(2)
    } else if (text.startsWith('点击文字')) {
      result.type = 'choose'
      result.value = text.substring(4)
    } else if (text.startsWith('快捷键')) {
      result.type = 'keypress'
      result.value = text.substring(3)
    } else if (text.startsWith('断点')) {
      result.type = 'debug'
      result.value = ''
    } else if (text.startsWith('等待')) {
      result.type = 'wait'
      const getWaitTime = inputString => {
        const matches = inputString.match(new RegExp('^(?:等待)(\\d+)秒'))
        if (matches) return `${Number(matches[1]) * 1000}`
        return '3000'
      }
      result.value = getWaitTime(text)
    } else if (text.startsWith('拖拽')) {
      const absoluteMatches = text.match(dragAbsoluteRegex)
      const relativeMatches = text.match(dragRelativeRegex)
      if (absoluteMatches) {
        result.element = absoluteMatches[1]
        result.type = 'drag'
        result.value = {
          type: 'absolute',
          x: absoluteMatches[2],
          y: absoluteMatches[3],
        }
      } else if (relativeMatches) {
        result.element = relativeMatches[1]
        result.type = 'drag'
        result.value = {
          type: 'relative',
          x: relativeMatches[2],
          y: relativeMatches[3],
        }
      }
    } else if (text.startsWith('比对节点')) {
      result.type = 'compare'
      const compareMatches = text.match(compareRegex)
      if (compareMatches) {
        result.value = {
          selector: compareMatches[1],
          file: compareMatches[2]
        }
      }
    } else if (text.startsWith('设置')) {
      result.type = 'setting'
      const settingMatches = text.match(settingRegex)
      if (settingMatches) {
        result.value = {
          name: settingMatches[1],
          value: settingMatches[2]
        }
      }
    } else if (text.startsWith('快速设置')) {
      result.type = 'quickSetting'
      const settingMatches = text.match(quickSettingRegex)
      if (settingMatches) {
        result.value = {
          name: settingMatches[1],
          value: settingMatches[2]
        }
      }
    } else if (text.startsWith('当前窗口为')) {
      result.type = 'setWindowName'
      genWindowStep(text, result)
    } else if (text.startsWith('切换窗口')) {
      result.type = 'switchWindow'
      genWindowStep(text, result)
    } else if (text.startsWith('关闭窗口')) {
      result.type = 'closeWindow'
      genWindowStep(text, result)
    } else if (text.startsWith('意愿认证')) {
      result.type = 'willAuth'
    } else if (text.startsWith('密码意愿认证')) {
      const password = text.substring(6)
      result.type = 'willAuthPassword'
      result.value = password
    } else if (text.startsWith('刷新页面')) {
      result.type = 'refresh'
    } else if (text.startsWith('调整窗口大小')) {
      const res = text.match(/\d+/g)
      if (res && res[1]) {
        result.type = 'resize'
        result.value = {
          width: res[0],
          height: res[1]
        }
      }
    } else if (text.startsWith('调整为移动端窗口')) {
      result.type = 'resizeToFitDevice'
    } else if (text.startsWith('跳转至')) {
      result.type = 'navigateTo'
      result.value = text.substring(3)
    } else if (text.match(fileRegex)) {
      result.type = 'upload'
      const getCompare = inputString => {
        const matches = inputString.match(fileRegex)
        if (matches) return matches[1]
        return {}
      }
      result.element = getCompare(text)
    } else if (text.startsWith('切换用户代理')) {
      result.type = 'switchCustomUserAgent'
      result.value = text.substring(6)
    } else if (text.startsWith('使用正则')) {
      result.type = 'captureURL'
      const captureURLMatches = text.match(captureURLRegex)
      if (captureURLMatches) {
        result.value = {
          regex: captureURLMatches[1],
          variableName: captureURLMatches[2]
        }
      }
    } else if (isRequestNode) {
      result.type = text.startsWith('请求接口并跳转') ? 'requestAndRedirect' : 'request'
      result.value = getChildNodeTextByIndex(node)
    }
    results.push(result)
  }

  const children = getChildrenNode(node)

  const promises = []

  if (children && children.length && !isSkipNodeTree && !isRequestNode) {
    for (let i = 0; i < children.length; i++) {
      const child = children[i]
      await traverseNode({
        node: child,
        globalSelector,
        globalSelectorKeys,
        results,
        parentNode: result,
        filePath,
        parser,
      })
    }
  }

  if (!isTitle) {
    result.desc = desc
  }

  return isTitle ? result : results
}

const getCases = async ({ root, globalSelector, globalSelectorKeys, propName, filePath, parser }) => {
  const rawFuncs = getChildrenNode(getNode(root, propName))
  const promises = []

  rawFuncs
    .filter(node => getText(node).toLocaleUpperCase().startsWith('TL-'))
    .forEach(node => {
      promises.push(traverseNode({ node, globalSelector, globalSelectorKeys, filePath, parser }))
    })

  const funcs = await Promise.all(promises)
  return funcs
}

exports.getCases = getCases

