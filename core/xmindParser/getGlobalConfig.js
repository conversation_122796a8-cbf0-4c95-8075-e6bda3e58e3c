const axios = require('axios')
const { getNode, getChildNodeTextByIndex, getChildNodeByIndex, getValue, getText, getChildrenNode, isUndefined } = require('./utils')
const curlToAxiosObject = require('../utils/curlToAxiosObject.js')

const getBaseConfig = config => {
  const url = getValue(config, 'url')
  const env = getValue(config, 'env')
  const height = getValue(config, 'height')
  const width = getValue(config, 'width')
  const pageLoadTimeout = getValue(config, 'pageLoadTimeout')
  const assertionTimeout = getValue(config, 'assertionTimeout')

  return {
    url,
    env,
    height,
    width,
    pageLoadTimeout,
    assertionTimeout,
  }
}

const cookiePropsArr = ['name', 'domain', 'path', 'expires']
const genCookies = config => {
  const result = {
    name: getText(config)
  }
  getChildrenNode(config).forEach(item => {
    const valueOrProp = getText(item)
    if (cookiePropsArr.includes(valueOrProp)) {
      result[valueOrProp] = getChildNodeTextByIndex(item)
    } else {
      result.value = valueOrProp
    }
  })
  return result
}

const genStorage = config => ({ key: getText(config), value: getChildNodeTextByIndex(config) })

const genRequest = config => {
  let [type, path] = getText(config).split(':')
  if (isUndefined(path)) {
    path = type
    type = 'include'
  }
  const result = { path, type }
  getChildrenNode(config).forEach(item => {
    const prop = getText(item)
    if (['merge'].includes(prop)) {
      result[prop] = true
    } else {
      result[prop] = getChildNodeTextByIndex(item)
    }
  })
  return result
}

const genUserAgent = config => getText(config)

const getMockConfig = mock => {
  const cookies = getChildrenNode(getNode(mock, 'cookies')).map(genCookies)
  const localStorage = getChildrenNode(getNode(mock, 'localStorage')).map(genStorage)
  const sessionStorage = getChildrenNode(getNode(mock, 'sessionStorage')).map(genStorage)
  const userAgent = getChildNodeTextByIndex(getNode(mock, 'userAgent'))
  const request = getChildrenNode(getNode(mock, 'request')).map(genRequest)

  return {
    cookies,
    localStorage,
    sessionStorage,
    userAgent,
    request,
  }
}

const variablePropsArr = ['prefix', 'suffix']
const getVariableConfig = config => {
  if (!config) return null

  const variables = getChildrenNode(config)
  return variables.map(variable => {
    const name = getText(variable)
    const result = {
      name,
    }
    getChildrenNode(variable).forEach((item) => {
      const text = getText(item)
      let [prop, value] = text.split(':')
      // 单独处理url
      if (prop.startsWith('http')) {
        result.value = text
      } else if (isUndefined(value)) {
        result.value = prop
      } else if (variablePropsArr.includes(prop)) {
        result[prop] = {
          type: value
        }
        if (value === 'custom') {
          getChildrenNode(item).forEach((customItem) => {
            let [customProp, customValue] = getText(customItem).split(':')
            result[prop][customProp] = customValue || true
          })          
        }
      } else {
        // 不同环境的
        result[prop] = value
      }
    })
    return result
  })
}

const getPreRequestConfig = async config => {
  if (!config) return null
  const requestQueue = getChildrenNode(config)
  let requestPreValue = null

  for (let i = 0; i < requestQueue.length; i++) {
    const request = requestQueue[i]
    const curlNode = getChildNodeByIndex(request, 0)
    let curl = getText(curlNode).replace(/\n/gi, ' ')
    if (requestPreValue) {
      for (let [k, v] of Object.entries(requestPreValue)) {
        curl = curl.replaceAll(`{${k}}`, v);
      }
    }
    const axiosObj = curlToAxiosObject(curl)
    const { data } = await axios(axiosObj)
    const paramsNode = getChildrenNode(curlNode)
    requestPreValue = paramsNode.length ? {} : null
    paramsNode.forEach((paramNode) => {
      const paramName = getText(paramNode)
      const paramPath = getText(getChildNodeByIndex(paramNode, 0)).split('.')
      let temp = data
      paramPath.forEach(k => { temp = temp[k] })
      requestPreValue[paramName] = typeof temp === 'object' ? JSON.stringify(temp) : temp
    })
  }

  return requestPreValue?.url || null
}

const getGlobalConfig = async root => {
  const config = getNode(root, '配置')
  const base = getBaseConfig(config)
  const mock = getMockConfig(getNode(config, 'mock'))
  const variable = getVariableConfig(getNode(config, 'variable'))
  const preRequest = await getPreRequestConfig(getNode(config, 'preRequest'))
  if (preRequest) base.url = preRequest
  return {
    base,
    mock,
    variable,
    preRequest,
  }
}

exports.getGlobalConfig = getGlobalConfig
exports.getVariableConfig = getVariableConfig
exports.getPreRequestConfig = getPreRequestConfig