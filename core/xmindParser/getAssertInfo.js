const CLICK_EVENT_KEYWORDS = ['点击']
const DRAG_EVENT_KEYWORDS = ['拖拽']
const DBCLICK_EVENT_KEYWORDS = ['双击']
const LONGPRESS_EVENT_KEYWORDS = ['长按']
const ASSERT_EVENT_KEYWORDS = ['判断']
const INPUT_EVENT_KEYWORDS = ['输入']
const KEYPRESS_EVENT_KEYWORDS = ['键入']
const PREPOSITION_KEYWORDS = ['至', '为', '是']
const SETTING_KEYWORDS = ['设置', '设定']
const WAIT_KEYWORDS = ['等待']
const FLOW_KEYWORDS = ['流程']
const HIERARCHY_KEYWORDS = ['下的', '中的']
const TESTING_KEYWORDS = ['检测', '检查']
const CHOOSE_KEYWORDS = ['选择', '选中']
const DATE_KEYWORDS = ['日期']
const FLOW = '流程'
const SECOND = '秒'

const KEYWORDS = {
  CLICK_EVENT_KEYWORDS,
  DRAG_EVENT_KEYWORDS,
  DBCLICK_EVENT_KEYWORDS,
  LONGPRESS_EVENT_KEYWORDS,
  ASSERT_EVENT_KEYWORDS,
  INPUT_EVENT_KEYWORDS,
  KEYPRESS_EVENT_KEYWORDS,
  PREPOSITION_KEYWORDS,
  SETTING_KEYWORDS,
  TESTING_KEYWORDS,
  CHOOSE_KEYWORDS,
  WAIT_KEYWORDS,
  FLOW_KEYWORDS,
  HIERARCHY_KEYWORDS,
  DATE_KEYWORDS,
  FLOW,
  SECOND,
}

const existRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)存在`)
const notExistRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)不存在`)
const enabledStateRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)(?:启用|禁用)`)
const textRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)的内容不?为(.*?)$`)
const textIncludeRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)的内容不?包含(.*?)$`)
const valueRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)的值不?为(.*?)$`)
const valueIncludeRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)的值不?包含(.*?)$`)
const classNameIncludeRegExp = new RegExp(`^(?:${[...KEYWORDS.ASSERT_EVENT_KEYWORDS].join('|')})(.+?)的类名不?包含(.*?)$`)

const getAssertInfo = inputString => {
  let matches = null
  if (inputString.endsWith('存在')) {
    // (not.)exist
    let match = null
    let value = 'exists'
    const assertKeywordsRegExp = new RegExp(`^(${KEYWORDS.ASSERT_EVENT_KEYWORDS.join('|')})`)
    if (inputString.endsWith('不存在')) {
      match = inputString.match(notExistRegExp)
      value = 'not.exists'
    } else {
      match = inputString.match(existRegExp)
    }
    if (match) {
      const element = match[1]
      return {
        element,
        value,
      }
    }
  } else if (inputString.endsWith('启用') || inputString.endsWith('禁用')) {
    // (not.)enable
    let value = 'enable'
    if (inputString.endsWith('禁用')) value = 'not.enable'
    const matches = inputString.match(enabledStateRegExp)
    if (matches) return { element: matches[1], value }
  } else if (inputString.startsWith('判断域名的内容包含')) {
    const value = inputString.replace('判断域名的内容包含', '')
    return {
      type: 'assertDomain',
      value,
    }
  } else if (inputString.startsWith('判断属性')) {
    const regex = /判断属性(?:([^的]+?)的)?(.*?)不?为(.*)/
    const match = inputString.match(regex)
    if (match) {
      return {
        type: 'assertAttr',
        element: match[1] ? match[1].trim() : '', // 元素名称，可能为空
        attribute: match[2].trim(), // 属性名
        value: match[3].trim(), // 属性值
        isEql: !inputString.includes('不为'),
      }
    }
  } else if (inputString.startsWith('判断样式')) {
    const regex = /判断样式(?:([^的]+?)的)?(.*?)不?为(.*)/
    const match = inputString.match(regex)

    if (match) {
      return {
        type: 'assertStyle',
        element: match[1] ? match[1].trim() : '', // 元素名称，可能为空
        style: match[2].trim(), // 样式名
        value: match[3].trim(), // 样式值
        isEql: !inputString.includes('不为'),
      }
    }
  } else if ((matches = inputString.match(textRegExp))) {
    // (not.)have.text
    let value = `have.text:${matches[2]}`
    if (inputString.includes('不为')) value = 'not.' + value
    return { element: matches[1], value }
  } else if ((matches = inputString.match(textIncludeRegExp))) {
    // (not.)include.text
    let value = `include.text:${matches[2]}`
    if (inputString.includes('不包含')) value = 'not.' + value
    return { element: matches[1], value }
  } else if ((matches = inputString.match(valueRegExp))) {
    // (not.)have.value
    let value = `have.value:${matches[2]}`
    if (inputString.includes('不为')) value = 'not.' + value
    return { element: matches[1], value }
  } else if ((matches = inputString.match(valueIncludeRegExp))) {
    // (not.)include.value
    let value = `include.value:${matches[2]}`
    if (inputString.includes('不包含')) value = 'not.' + value
    return { element: matches[1], value }
  } else if ((matches = inputString.match(classNameIncludeRegExp))) {
    // (not.)include.class
    let value = `include.className:${matches[2]}`
    if (inputString.includes('不包含')) value = 'not.' + value
    return { element: matches[1], value }
  }
  throw new Error(`${inputString} assert`)
}

exports.getAssertInfo = getAssertInfo