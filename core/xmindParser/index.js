const Xmindparser = require('xmindparser')
const crypto = require('crypto')
const { transformNodes } = require('./transformNodes')
const { getGlobalConfig } = require('./getGlobalConfig')
const { getGlobalSelector } = require('./getGlobalSelector')
const { getCases } = require('./getCases')
const { getText } = require('./utils')
const parser = new Xmindparser()

function generateHmacId(input) {
  return crypto.createHmac('sha256', 'secretKey').update(input).digest('hex');
}

const getXmindInfo = async (filePath) => {
  const xmindInfo = await parser.xmindToJSON(filePath)
  const { root } = xmindInfo

  const rootText = getText(root.data)

  transformNodes(root)
  const globalConfig = await getGlobalConfig(root)
  const globalSelector = getGlobalSelector(root)
  const globalSelectorKeys = Object.keys(globalSelector)

  const cases = [
    ...await getCases({ root, globalSelector, globalSelectorKeys, propName: '功能', filePath, parser }),
    ...await getCases({ root, globalSelector, globalSelectorKeys, propName: '样式', filePath, parser })
  ]
  
  const injectId = `${generateHmacId(filePath)}`
  const id = `${rootText}_${injectId}`

  return {
    id,
    injectId,
    config: {
      name: id,
      ...globalConfig,
    },
    selector: globalSelector,
    cases,
  }
}

exports.getXmindInfo = getXmindInfo