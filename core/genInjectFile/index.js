const { genFile } = require('../genFile')

const genStr = (obj) => {
  return `function setStorageAfterLoad(storageData) {
    function setCookie(name, value, options) {
      var cookieString = name + "=" + value + "; path=/";
      if (options.path) cookieString += "; path=" + options.path;
      if (options.domain) cookieString += "; domain=" + options.domain;
      if (options.expires) cookieString += "; expires=" + options.expires;
      document.cookie = cookieString;
    }

    function setLocalStorage(items) {
      items.forEach(function(item) {
        localStorage.setItem(item.key, item.value);
      });
    }

    function setSessionStorage(items) {
      items.forEach(function(item) {
        sessionStorage.setItem(item.key, item.value);
      });
    }

    function applyStorageSettings() {
      // Set cookies
      storageData.cookies.forEach(function(cookie) {
        setCookie(cookie.name, cookie.value, {
          path: cookie.path,
          domain: cookie.domain,
          expires: cookie.expires
        });
      });

      // Set localStorage items
      if (storageData.localStorage) {
        setLocalStorage(storageData.localStorage);
      }

      // Set sessionStorage items
      if (storageData.sessionStorage) {
        setSessionStorage(storageData.sessionStorage);
      }

      // 强制隐藏vconsole
      const style = document.createElement('style');
      document.head.appendChild(style);
      // 注入 CSS 规则，隐藏 vconsole
      const css = \`
        /* 隐藏 vconsole 的主要容器 */
        #__vconsole {
            display: none !important;
        }
      \`;
      // 将 CSS 规则添加到 <style> 标签中
      style.sheet.insertRule(css, style.sheet.cssRules.length);
    }

    // Check if the page has already loaded
    if (document.readyState === "complete") {
      applyStorageSettings();
    } else {
      window.addEventListener("load", applyStorageSettings);
    }
  }
  setStorageAfterLoad(${JSON.stringify(obj)});
  try {
    const data = JSON.parse(window.name);
    console.log(data)
    if (data.__ui_test_userAgent__) {
      Object.defineProperty(navigator, "userAgent", {
        value: data.__ui_test_userAgent__,
        writable: true
      })
    }
  } catch (e) {}
  `
}

const genInjectFile = (xmindInfo, filePath) => {
  const mock = xmindInfo.config.mock || {}
  const str = genStr(mock)
  genFile(filePath, str)
}

exports.genInjectFile = genInjectFile