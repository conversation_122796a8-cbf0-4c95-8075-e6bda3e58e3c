const fs = require('fs');
const path = require('path');
const ACTIONS = require(path.resolve(process.cwd(), './core/actions/index'))

let seed = 0

// const genTestcafeCases = (cases, file) => {
const genCases = ({ config, selector, cases },fileTag) => {
  const { base, mock, variable, name } = config
  const varMap = new Map()

  const result = cases.map(testcase => {
    const { steps, title, variable: caseVariable, url = '' } = testcase;

    let executionBlock = `test('${title}', async t => {
      log.info('-------------------- 开始测试 --------------------')
      await t.wait(100);
      await actions.composite.clearWindowsMap();
      const requestVariable = {}
      const assertErrorArr = []
      let elementSelector = ''
      let assertElementSelector = ''
      let performanceTiming = ''
      const isFlow = { value: false }
      // url
      // actions
      await t.expect(assertErrorArr.length).eql(0, assertErrorArr.join(';'));
      log.info('-------------------- 结束测试 --------------------')
    });`;

    if (url) {
      executionBlock = executionBlock.replace('// url', `await t.navigateTo('${url}');`)
    }

    const selectorFormatter = (el, removeQuotes, stringify = false) => {
      if (!el) return '';
      let result = ''
      
      if (el.startsWith('text://')) {
        const value = el.replace('text://', '')
        const [text, tagName, ...classNameArr] = value.split('.');
        const res = {
          text,
          tagName,
          className: (classNameArr || []).join('.'),
        }
        return stringify ? JSON.stringify(res) : res
      } else if (el.startsWith('css://')) {
        result = el.replace('css://', '')
      } else {
        result = `[data-esign-inject-name="${el}"]`
      }

      if (removeQuotes) return result
      return `'${result}'`
    };


    const toggleElement = (el, filterVisible = true) => {
      if (!el) return '';

      const tail = `${ filterVisible ? '.filterVisible()' : '' }\n`

      let res = ''
      
      if (el.startsWith('css://')) {
        const realSelector = el.replace('css://', '')
        res = ` elementSelector = Selector('${realSelector}')${tail}`;
      } else if (el.startsWith('text://')) {
        res = ` elementSelector = await getElement(${JSON.stringify(selectorFormatter(el, true))})`;
      } else {
        const realSelector = `[data-esign-inject-name="${el}"]`
        res = ` elementSelector = Selector('${realSelector}')${tail}`;
      }

      return res
    };


    const toggleAssertElement = el => {
      if (!el) return '';
      if (el.startsWith('css://')) {
        return ` assertElementSelector = Selector('${el.replace('css://', '')}')\n`;
      }
      return ` assertElementSelector = Selector('[data-esign-inject-name="${el}"]')\n`;
    };

    function generateCombinedVariable(rule) {
      if (varMap.has(rule.name)) return varMap.get(rule.name);
      function generateRandomString(length, options = {}) {
        let result = '';
        const { letters, number, caseSensitive } = options;
        const lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
        const upperCaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const digits = '0123456789';
        
        let characters = '';
        if (letters) {
          characters += caseSensitive ? lowerCaseLetters + upperCaseLetters : lowerCaseLetters;
        }
        if (number) {
          characters += digits;
        }
    
        for (let i = 0; i < length; i++) {
          result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
    
        return result;
      }
    
      let prefix = ''
      if (rule.prefix) {
        if (rule.prefix.type === 'custom') {
          prefix = generateRandomString(rule.prefix.length || 4, rule.prefix);
        } else if (rule.prefix.type === 'timestamp') {
          prefix = Date.now().toString();
        }
      }
    
      let value = rule.value || '';
    
      let suffix = '';
      if (rule.suffix) {
        if (rule.suffix.type === 'custom') {
          suffix = generateRandomString(rule.suffix.length || 4, rule.suffix);
        } else if (rule.suffix.type === 'timestamp') {
          suffix = Date.now().toString();
        }
      }

      const res = `${prefix}${value}${suffix}`
      varMap.set(rule.name, res);
      return res;
    }

    const extractPlaceholders = (str) => {
      const regex = /\$\{([^}]+)\}/g;
      const matches = [];
      let match;

      while ((match = regex.exec(str)) !== null) {
        matches.push(match[1]);
      }

      return matches;
    }

    const getVariable = (strOrObj, ignore) => {
      let res = strOrObj;
      if (typeof strOrObj === 'object') {
        res = JSON.stringify(strOrObj)
      }
      const placeholders = extractPlaceholders(res)
      placeholders.forEach(item => {
        const itemStr = `\${${item}}`
        const variables = (caseVariable || variable || [])
        const rule = variables.find(v => v.name === item)
        if (rule) {
          const value = generateCombinedVariable(rule)
          res = res.replace(itemStr, value)
        } else {
          res = res.replace(itemStr, `\${requestVariable.${item} || ''}`)
        }
      })
      return res
    }

    const stepsLen = steps.length

    const actions = steps.map((step, index) => {
      const currentIndex = index + 1
      const stepDesc = `第${currentIndex}步: ${['request', 'requestAndRedirect'].includes(step.type) ? '请求' : step.desc}`.replaceAll('\n', ' ')
      let res = `\n// ${stepDesc}\n`

      // 如果step需要flow控制，则添加条件判断
      const flowControlPrefix = step.isFlow ? 'if (isFlow.value) {\n' : '';
      const flowControlSuffix = step.isFlow ? '\n}' : '';

      res += flowControlPrefix;

      if (step.type === 'click') {
        res += ACTIONS.base.click(selectorFormatter(step.element), step.options || '', step.ignore);
      } else if (step.type === 'repeatedClick') {
        res += ACTIONS.base.repeatedClick(selectorFormatter(step.element), step.value || 1, step.options || '{ speed: 0.95 }', step.ignore);
      } else if (step.type === 'rightClick') {
        res += ACTIONS.base.rightClick(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'doubleClick') {
        res += ACTIONS.base.doubleClick(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'input') {
        const inputValue = getVariable(step.value, step.ignore);
        res += ACTIONS.base.input(inputValue);
      } else if (step.type === 'keypress') {
        res += ACTIONS.base.keypress(step.value, step.ignore);
      } else if (step.type === 'refresh') {
        res += ACTIONS.base.refresh();
      } else if (step.type === 'resize') {
        res += ACTIONS.base.resize(step.value);
      } else if (step.type === 'resizeToFitDevice') {
        res += ACTIONS.base.resizeToFitDevice(step.value);
      } else if (step.type === 'choose') {
        res += ACTIONS.base.choose(getVariable(step.value), step.ignore);
      } else if (step.type === 'debug') {
        res += ACTIONS.base.debug(step.value, step.ignore);
      } else if (step.type === 'hover') {
        res += ACTIONS.base.hover(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'navigateTo') {
        res += ACTIONS.base.navigateTo(getVariable(step.value));
      } else if (step.type === 'wait') {
        res += ACTIONS.base.wait(step.value, step.ignore);
      } else if (step.type === 'drag') {
        if (step.value.type === 'absolute') {
          res += ACTIONS.utils.genIgnoreError(`${toggleElement(step.element)} ;const { x, y } = await getElementCenterFN(elementSelector); await t.drag(elementSelector, x - ${step.value.x}, y - ${step.value.y})`, step.ignore)
        } else if (step.value.type === 'relative') {
          res += ACTIONS.utils.genIgnoreError(`${toggleElement(step.element)} await t.drag(elementSelector, ${step.value.x}, ${step.value.y}, { speed: 0.5 })`, step.ignore);
        }
      } else if (step.type === 'assertDomain') {
        const errorMsg = `域名断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.domain(t, ${getVariable(JSON.stringify(step))});`, true, `await t.report('assert', '${errorMsg}'); assertErrorArr.push('${errorMsg}');`);
      } else if (step.type === 'assertStyle') {
        const errorMsg = `样式断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.style(t, ${selectorFormatter(step.element, false, true)}, ${getVariable(JSON.stringify(step))}, isFlow);`, true, `await t.report('assert', '${errorMsg}'); assertErrorArr.push('${errorMsg}');`);
      } else if (step.type === 'assertAttr') {
        const errorMsg = `属性断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.attr(t, ${selectorFormatter(step.element, false, true)}, ${getVariable(JSON.stringify(step))}, isFlow);`, true, `await t.report('assert', '${errorMsg}'); assertErrorArr.push('${errorMsg}');`);
      } else if (step.type === 'assert') {
        const errorMsg = `断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.base(t, ${selectorFormatter(step.element, false, true)}, ${getVariable(JSON.stringify(step))}, isFlow);`, true, `await t.report('assert', '${errorMsg}'); assertErrorArr.push('${errorMsg}');`);
      } else if (['upload', 'file'].includes(step.type)) {
        const variableName = `file_${++seed}`
        res += ACTIONS.utils.genIgnoreError(`const ${variableName} = await actions.composite.getUploadFilePath('${step.filePath || ''}');\n`, step.ignore);
        // res += `await actions.composite.upload(${selectorFormatter(step.element)});\n`
        res += ACTIONS.composite.upload(selectorFormatter(step.element), variableName);
        res += ACTIONS.base.wait(10000);
      } else if (step.type === 'datepicker') {
        res += ACTIONS.utils.genIgnoreError(`await datepickerFN(t, ${JSON.stringify(step.value)})`, step.ignore);
        // res += `await actions.composite.datepicker(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'setting') {
        res += `await actions.composite.setting(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'quickSetting') {
        res += `await actions.composite.quickSetting(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'compare') {
        step.value.selector = selectorFormatter(step.value.selector, true)
        res += `await actions.composite.compare(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'setWindowName') {
        const windowName = JSON.stringify(step.value)
        res += `await actions.composite.setWindowName(t, ${windowName}, windowWidth, windowHeight)`
        res += `\nperformanceTiming = await t.eval(() => JSON.stringify(window.performance.timing));\nlog.info(\`[timing] \${performanceTiming}\`)\n`
      } else if (step.type === 'switchWindow') {
        res += `await actions.composite.switchWindow(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'closeWindow') {
        res += `await actions.composite.closeWindow(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'willAuth') {
        res += `await actions.composite.willAuth(t)`
      } else if (step.type === 'willAuthPassword') {
        res += `await actions.composite.willAuthPassword(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'request') {
        res += `await actions.composite.request(t, log, ${getVariable(step.value)}, requestVariable)`
      } else if (step.type === 'requestAndRedirect') {
        res += `await actions.composite.requestAndRedirect(t, log, ${getVariable(step.value)}, requestVariable)`
      } else if (step.type === 'switchCustomUserAgent') {
        res += `await actions.composite.switchCustomUserAgent(t, "${step.value}")`
      } else if (step.type === 'captureURL') {
        res += `await actions.composite.captureURL(t, ${JSON.stringify(step.value)}, requestVariable)`
      }

      res += `\nlog.running('[${Math.floor(currentIndex / stepsLen * 1000/ 10) }%][${currentIndex}][${stepsLen}] 完成${stepDesc}')`

      res += flowControlSuffix;

      return res;
    });

    return executionBlock.replace('// actions', actions.join('\n'));
  });

  const tempDirPath = path.resolve(process.cwd(), './tmp')
  const compareFileDirPath = path.resolve(process.cwd(), './compare/${name}')

  const casesStr = `
    const { Selector, ClientFunction } = require('testcafe');
    const path = require('path');
    const { Log } = require(path.resolve(process.cwd(), './core/logs/index'));
    const log = new Log('${fileTag}');

    // 请求相关
    const { requestInterceptionFN, getElementCenterFN, compareFN, datepickerFN } = require(path.resolve(process.cwd(), './core/specialMethod/index'))
    const { CustomHeaderHook } = require(path.resolve(process.cwd(), './core/specialMethod/requestHook'))
    const customHeaderHook = new CustomHeaderHook(log);

    const actions = require(path.resolve(process.cwd(), './core/actions/index'))
    const { getElement, setCurrentElement, getCurrentElement, setAssertCurrentElement, getAssertCurrentElement } = require(path.resolve(process.cwd(), './core/currentElement/index'))

    const tempDirPath = "${tempDirPath}"
    const compareFileDirPath = "${compareFileDirPath}"

    const mock = ${JSON.stringify(mock)}
    const windowWidth = ${base.width || 1440}
    const windowHeight = ${base.height || 800}
    fixture \`${name}\`
      .page \`${base.url}\`
      .requestHooks(customHeaderHook, requestInterceptionFN(mock?.request))
      .skipJsErrors();

      ${result.join('\n\n\n')}
    `;

  return casesStr;
};

exports.genCases = genCases