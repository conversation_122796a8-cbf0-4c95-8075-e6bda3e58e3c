const http = require('http');
const https = require('https');
const Path = require('path');
const fs = require('fs');

const uploadFN = async (t, path, value = '') => {
  // try
  let downloadSuccess = false;
  let useRemote = false;

  if (value.startsWith('https://')) {
    useRemote = 'https';
  } else if (value.startsWith('http://')) {
    useRemote = 'http';
  }

  if (useRemote) {
    console.log(`----- 正在从 ${value} 下载文件 -----`);
    const protocol = useRemote === 'http' ? http : https;
    const temp = value.split('.');
    const suffix = temp[temp.length - 1];
    const name = `${Date.now()}.${suffix}`;
    downloadSuccess = await new Promise(resolve => {
      protocol
        .get(value, res => {
          const dir = Path.resolve(process.cwd(), './tmp');
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          const filePath = Path.resolve(process.cwd(), `./tmp/${name}`);
          const file = fs.createWriteStream(filePath);
          res.pipe(file);
          file.on('finish', () => {
            file.close();
            console.log('----- 文件下载完成 -----');
            resolve(filePath);
          });
          file.on('error', resolve);
        })
        .on('error', resolve);
    });

    if (!downloadSuccess) throw new Error('文件下载失败');

    await t.setFilesToUpload(path, [ downloadSuccess ]);
  } else {
    await t.setFilesToUpload(path, [ Path.resolve(process.cwd(), value ? value : './asserts/file/testFile.pdf') ]);
  }
  await t.wait(10000);
  // catch
};

exports.uploadFN = uploadFN