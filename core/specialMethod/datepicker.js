const { Selector } = require('testcafe');

const datepickerFN = async (t, obj) => {

  const clickTimes = async (el, _t) => {
    let times = _t;
    while (times--) {
      await t.click(el);
    }
  }

  const isRelative = obj.type === 'relative';
  const isDateRange = obj.value.length > 1
  const DateFn = async ({ panelSelector, panelBodySelector, panelHeaderSelector, targetDate
    , prefix }) => {
    const targetYear = targetDate.getFullYear();
    const targetMonth = targetDate.getMonth() + 1;
    const targetDay = targetDate.getDate();
    const panel = await Selector(panelSelector, { timeout: 1000 }).filterVisible();
    const panelBody = await Selector(panelBodySelector, { timeout: 1000 }).filterVisible();
    const yearAndMonthEl = await panel.find(`${panelHeaderSelector}`).filterVisible();
    const [ yearStr, monthStr ] = (await yearAndMonthEl.innerText).split('-');
    // 计算年
    const yearOffset = targetYear - parseInt(yearStr);
    if (yearOffset) await clickTimes(panel.find(`.${prefix}-icon-d-arrow-${yearOffset > 0 ? 'right' : 'left'}`).filter(node => !node.className.includes('is-disabled')), Math.abs(yearOffset));
    // 计算月
    const monthOffset = targetMonth - parseInt(monthStr);
    if (monthOffset) await clickTimes(panel.find(`.${prefix}-icon-arrow-${monthOffset > 0 ? 'right' : 'left'}`).filter(node => !node.className.includes('is-disabled')), Math.abs(monthOffset));
    // 选择日期
    const day = await Selector(panelBody.find(`.${prefix}-date-table__row .available`), { timeout: 1000 }).withText(targetDay + '');
    await t.click(day);
  };


  const prefix = obj.isEl ? 'el' : 'es';
  const panelBodySelector = `.${prefix}-picker-panel__body`;

  if (isDateRange) {
    const now = Date.now();

    const beignTimestamp = isRelative ? now + obj.value[0] : obj.value[0]
    const endimestamp = isRelative ? now + obj.value[1] : obj.value[1]

    const beginDate = new Date(beignTimestamp)
    const endDate = new Date(endimestamp)
    await DateFn({
      panelSelector: `.${prefix}-date-range-picker`,
      panelHeaderSelector: `.${prefix}-date-range-picker__header`,
      panelBodySelector: `${panelBodySelector} .is-left`,
      targetDate: beginDate,
      prefix,
    });
    await DateFn({
      panelSelector: `.${prefix}-date-range-picker`,
      panelHeaderSelector: `.${prefix}-date-range-picker__header`,
      panelBodySelector: `${panelBodySelector} .is-right`,
      targetDate: endDate,
      prefix,
    });
  } else {
    const now = Date.now();

    const timestamp = isRelative ? now + obj.value[0] : obj.value[0]
    await DateFn({
      panelSelector: `.${prefix}-picker-panel`,
      panelHeaderSelector: `.${prefix}-date-picker__header`,
      panelBodySelector,
      targetDate: new Date(timestamp),
      prefix,
    });
  }
}

exports.datepickerFN = datepickerFN
