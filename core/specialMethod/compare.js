const fs = require('fs');
const axios = require('axios');
const fileType = require('file-type');
const { createHash } = require('crypto');

const getMD5 = fd => {
  return new Promise((resolve, reject) => {
    const readStream = fd.createReadStream()
    const md5Hash = createHash('md5')
    readStream.on('data', chunk => {
      md5Hash.update(chunk)
    })

    readStream.on('end', () => {
      const contentMd5HashValue = md5Hash.digest('base64')
      resolve(contentMd5HashValue)
    })

    readStream.on('error', error => {
      console.error('计算 Content-MD5 时出错：', error)
      reject(error)
    })
  })
}

const genRequestData = obj => {
  return {
    ip: ['*************', '**************'],
    accountId: '',
    serviceId: 'e35f3884-cef4-4bc1-81a3-540bdae87a28',
    serviceType: 'zsy',
    internal: true,
    expire: 7200000,
    callback:
      'http://************:8086/evi-service/evidence/v1/uploaded/presevation/notify',
    queue: 'file_system_callback_test1',
    convertToHTML: true,
    convertToPDF: false,
    projectId: 'UI-FE',
    ...obj
  }
}

const upload = async file => {
  const { mime: contentType } = await fileType.fromFile(file)
  const fd = await fs.promises.open(file)
  const { size: fileSize } = await fd.stat()
  const contentMd5 = await getMD5(fd)
  const fileName = path.basename(file)
  const fileStream = fs.createReadStream(file)

  const data = genRequestData({ contentType, contentMd5, fileSize, fileName })

  const {
    data: { url, fileKey }
  } = await axios({
    method: 'POST',
    url: 'http://file-system.testk8s.tsign.cn/file-system/fileService/getSignUrl',
    data,
    headers: {
      'Content-Type': 'application/json',
      'X-timevale-project-id': '1111563774'
    }
  })

  await axios({
    method: 'PUT',
    url,
    data: fileStream,
    headers: {
      'Content-Type': contentType
    }
  })

  return fileKey
}

const compareFN = async (t, { selector, file }) => {
  const imageFileName = Date.now() + '.png'
  const screenShotPath = path.resolve(tempDirPath, imageFileName)
  await t.takeElementScreenshot(selector, imageFileName)

  const fileKey1 = await upload(screenShotPath)
  // 判断比对文件所在的文件夹是否存在
  if (!fs.existsSync(compareFileDirPath)) {
    console.log('比对文件夹不存在 创建文件夹:', compareFileDirPath)
    fs.mkdirSync(compareFileDirPath)
  }

  const filePath = path.resolve(
    compareFileDirPath,
    file + '_' + t.browser.name + '.png'
  )

  // 判断比对文件是否存在
  if (!fs.existsSync(filePath)) {
    console.log('未找到比对图片 新增比对图片:', compareFileDirPath)
    fs.copyFileSync(screenShotPath, filePath)
  }

  const fileKey2 = await upload(filePath)

  const { data } = await axios({
    method: 'POST',
    url: 'http://file-microscope.testk8s.tsign.cn/compareFile',
    data: {
      env: 'test',
      fileKey1,
      fileKey2,
      compareType: 2,
      compareLevel: 1,
      taskName: '前端UI自动化测试用',
      sourceTypeCode: 'EPAAS_DOC_TEMPLATE_UI_AUTOTEST'
    }
  })

  const similarityList = JSON.parse(data.result.similarityList)

  if (similarityList[0] < 0.99)
    throw new Error('阈值 0.99, 比对值 ' + similarityList[0] + ', 比对不通过')
}

exports.compareFN = compareFN