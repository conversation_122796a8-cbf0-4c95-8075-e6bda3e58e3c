const { ClientFunction } = require('testcafe')

// 定义一个 ClientFunction 来计算元素的中心点坐标
const getElementCenter = ClientFunction((selector) => {
    const element = selector;

    // 获取元素的位置和大小
    const rect = element.getBoundingClientRect();

    // 计算中心点坐标
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;

    return { x, y };
}, {
    dependencies: { selector: null }
});

exports.getElementCenterFN = getElementCenter