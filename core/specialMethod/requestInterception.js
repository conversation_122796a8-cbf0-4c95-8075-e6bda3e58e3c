const { RequestMock, RequestLogger } = require('testcafe');
const axios = require('axios');

const cache = new Map();

const splitEq = (inputStr) => {  
  // 找到等号的位置
  const equalIndex = inputStr.indexOf("=");
  
  // 分割字符串
  const firstPart = inputStr.substring(0, equalIndex).trim();
  const secondPart = inputStr.substring(equalIndex + 1).trim();
  
  return [firstPart, secondPart];
}

const requestInterceptionFN = interfaces => {
  if (!interfaces) return [];

  const { POST, GET, DELETE, PUT } = interfaces.reduce(
    (res, item) => {
      const m = item.method;
      res[m].push(item);

      return res;
    },
    { POST: [], GET: [], DELETE: [], PUT: [] }
  );

  // 接口 MAP
  const mockMap = new Map([
    [ 'POST', POST ],
    [ 'GET', GET ],
    [ 'DELETE', DELETE ],
    [ 'PUT', PUT ],
  ]);

  const logger = RequestLogger(/https:\/\/.+?(\.esign\.cn|\.tsign\.cn)/, {
    logRequestHeaders: true,
    logRequestBody: true,
    logResponseHeaders: true,
    logResponseBody: true,
  });

  const editResponse = async ({ method, headers, url, bufferData }, response) => {
    // 是否直接拦截 即不发送请求
    let isReplaceAll = true;
    try {
      // 可以被解析出来 说明需要直接拦截
      JSON.parse(response);
    } catch (e) {
      isReplaceAll = false;
    }
    let result = response;
    if (!isReplaceAll) {
      const options = {
        withCredentials: true,
        method,
        headers,
        url,
      };

      if (method.toUpperCase() === 'POST') {
        options.data = JSON.parse(bufferData.toString());
      }

      // 根据拦截的请求数据 发送请求
      const { data } = await axios(options);

      // 进行需要 mock 的部分数据的切割
      const arr = response.split('\n');
      const isArrayPath = path => /\[[0-9]+\]$/.test(path);
      const getArrayPath = path => {
        const [ propPath, indexPath ] = path.replace(']', '').split('[');
        return { propPath, indexPath };
      };

      const setValue = (obj, path, value) => {
        if (isArrayPath(path)) {
          const { propPath, indexPath } = getArrayPath(path);
          if (!obj[propPath]) obj[propPath] = [];
          const arr = obj[propPath];
          arr[Number(indexPath)] = value;
          return;
        }
        obj[path] = value;
      };

      const mergeValue = (data, pathArr, value) => {
        let temp = data;
        for (let i = 0; i < pathArr.length; i++) {
          // 是否为数组路径 例如 data.a[123] 中的 a[123]
          const path = pathArr[i];
          if (i === pathArr.length - 1) {
            setValue(temp, path, value);
          } else {
            if (isArrayPath(path)) {
              const { propPath, indexPath } = getArrayPath(path);
              if (!temp[propPath]) temp[propPath] = [];
              if (!temp[propPath][indexPath]) temp[propPath][indexPath] = {};
              temp = temp[propPath][indexPath];
            } else {
              if (!temp[path]) temp[path] = {};
              temp = temp[path];
            }
          }
        }
      };
      for (let i = 0; i < arr.length; i++) {
        const [ path, value ] = splitEq(arr[i]);
        mergeValue(data, path.split('.'), JSON.parse(value));
      }
      // 重新赋值
      result = JSON.stringify(data);
    }

    return result;
  };

  const mock = RequestMock()
    .onRequestTo(async ({ url, method: _method, isAjax, headers, body }) => {
      if (isAjax) {
        const method = _method.toLocaleUpperCase();

        // 根据方法查找对应的配置
        const mockArr = mockMap.get(method) || [];

        for (let i = 0; i < mockArr.length; i++) {
          const item = mockArr[i];
          const { type, path: itemUrl } = item;
          if (
            (type === 'include' && url.includes(itemUrl)) ||
            (type === 'regexp' && new RegExp(itemUrl).test(url))
          ) {
            const res = await editResponse({ method: _method, headers, url, bufferData: body }, item.response);
            cache.set(`${url}_${method}`, res);
            return true;
          }
        }
      }

      return false;
    })
    .respond((req, res) => {
      const { url, method } = req;
      const response = cache.get(`${url}_${method}`);
      res.headers['Access-Control-Allow-Credentials'] = true;
      res.headers['content-type'] = 'application/json;charset=UTF-8';
      res.headers['Access-Control-Allow-Origin'] = req.headers.origin;
      res.headers['Access-Control-Expose-Headers'] = 'X-Tsign-Session-Trace-Id, X-Tsign-Open-Operator-Id, x-ts-token, X-Tsign-Open-Tenant-Id, X-Tsign-Trace-Id, X-Tsign-Open-App-Id, X-Tsign-token';
      res.setBody(response);
      cache.delete(`${url}_${method}`);
    });

  return [ logger, mock ];
};



exports.requestInterceptionFN = requestInterceptionFN