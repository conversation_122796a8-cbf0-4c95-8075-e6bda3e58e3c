const { RequestHook} = require('testcafe');

const requesstMap = new Map()

class CustomHeaderHook extends RequestHook {
  /**
   * 构造函数，创建自定义请求钩子实例
   * @param {Object} requestFilterRules - 请求过滤规则配置
   */
  constructor(log) {
    super(null, { includeHeaders: true, includeBody: true });
    this.log = log;
  }

  // 用于修复 静态资源请求的 cookie 问题
  lastCookie = ''

  log = null

  /**
   * 请求拦截处理方法
   * @param {Object} event - 请求事件对象
   * @property {Object} event.requestOptions - 请求配置参数
   * @property {string} event.requestOptions.url - 当前请求的URL地址
   */
  onRequest(event) {
    const { _requestInfo: { requestId, sessionId, url, method }, requestOptions, id, isAjax } = event
    if (isAjax && requestOptions.headers.cookie) {
      this.lastCookie = requestOptions.headers.cookie
    } else {
      requestOptions.headers.cookie = this.lastCookie
    }
    const key = `${requestId}-${sessionId}`

    if (method.toUpperCase() === 'OPTIONS') return

    requesstMap.set(key, {
      url,
      method,
      isAjax
    })

    // 先写一个简单的逻辑
    const timeout = setTimeout(() => {
      if (requesstMap.has(key)) {
        this.log.error(`请求超20秒: ${JSON.stringify({ url, method })}`)
      }
      clearTimeout(timeout)
    }, 20 * 1000);
  }
  onResponse(event) {
    const { sessionId, requestId, id, body, statusCode } = event
    const key = `${requestId}-${sessionId}`

    if (!requesstMap.has(key)) return

    const { url, method, isAjax } = requesstMap.get(key)
    requesstMap.delete(key)

    if (statusCode === 200) {
      if (!isAjax) return

      // 转换Buffer为对象
      let responseBody = body;
      if (Buffer.isBuffer(responseBody)) {
        try {
          responseBody = JSON.parse(responseBody.toString('utf8'));
        } catch (e) {
          // 说明不是 json 格式 不需要处理
          return
        }
      }

      if (responseBody?.code && responseBody.code !== 0) {
        this.log.warn(`接口调用失败: ${JSON.stringify({ url, method, body: responseBody })}`)
      }
    } else if (statusCode >= 400) {
      this.log.error(`请求失败: ${JSON.stringify({ url, method, statusCode })}`)
    } else {
      // this.log.info(`接口调用非200: ${JSON.stringify({ url, method })}`)
    }
  }
}

exports.CustomHeaderHook = CustomHeaderHook