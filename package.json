{"name": "ui-test", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "pnpm i && pnpm build && pm2 start ecosystem.config.js --env production", "local": "node ./core/local.js", "start:devp": "pnpm i && pnpm build && pm2 start ecosystem.config.js --env development", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:request": "node ./tools/requestTransform/index.js"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^0.6.3", "@nestjs/common": "^11.0.1", "@nestjs/config": "^2.3.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.0.3", "@nestjs/typeorm": "^11.0.0", "axios": "^1.3.4", "bull": "^4.10.4", "cross-env": "^7.0.3", "file-type": "^16.5.4", "form-data": "^4.0.4", "ioredis": "^5.3.2", "jszip": "^3.10.1", "moment": "^2.30.1", "mysql2": "^3.12.0", "pm2": "^6.0.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "testcafe": "^3.7.1", "testcafe-reporter-custom": "^1.2.1", "testcafe-reporter-html": "^1.4.6", "typeorm": "^0.3.20", "uuid": "^11.0.3", "xmind": "^2.2.33", "xmindparser": "^1.0.2", "xml-formatter": "^3.6.6", "xml-js": "^1.6.11", "yargs-parser": "^21.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}